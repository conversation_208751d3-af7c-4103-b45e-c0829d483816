import React, { useRef, useState, useEffect, useCallback } from 'react';
import {
  View,
  Modal,
  TouchableWithoutFeedback,
  Animated,
  Easing,
  StyleProp,
  ViewStyle,
  Dimensions,
} from 'react-native';

interface PopoverProps {
  visible: boolean;
  onClose: () => void;
  children: React.ReactNode;
  containerStyle?: StyleProp<ViewStyle>;
  contentStyle?: StyleProp<ViewStyle>;
  position?: 'bottom' | 'top' | 'center';
  closeOnBackdropPress?: boolean;
  backdropOpacity?: number;
}

const Popover: React.FC<PopoverProps> = ({
  visible,
  onClose,
  children,
  containerStyle,
  contentStyle,
  position = 'bottom',
  closeOnBackdropPress = true,
  backdropOpacity = 0.5,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const [modalVisible, setModalVisible] = useState(visible);
  const { height: windowHeight } = Dimensions.get('window');

  // 计算滑出位置
  const getSlideOutValue = useCallback(() => {
    switch (position) {
      case 'bottom':
        return windowHeight;
      case 'top':
        return -windowHeight;
      case 'center':
        return windowHeight;
      default:
        return windowHeight;
    }
  }, [position, windowHeight]);

  // 计算初始滑入位置
  const getSlideInValue = useCallback(() => {
    switch (position) {
      case 'bottom':
        return windowHeight;
      case 'top':
        return -windowHeight;
      case 'center':
        return 0;
      default:
        return windowHeight;
    }
  }, [position, windowHeight]);

  // 设置初始滑动位置
  useEffect(() => {
    slideAnim.setValue(getSlideInValue());
  }, [position, slideAnim, getSlideInValue]);

  useEffect(() => {
    if (visible) {
      setModalVisible(true);
      Animated.parallel([
        // 背景淡入
        Animated.timing(fadeAnim, {
          toValue: backdropOpacity,
          duration: 300,
          useNativeDriver: true,
          easing: Easing.ease,
        }),
        // 内容滑入
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
          easing: Easing.out(Easing.ease),
        }),
      ]).start();
    } else {
      // 执行关闭动画
      const slideOutValue = getSlideOutValue();
      Animated.parallel([
        // 背景淡出
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
          easing: Easing.ease,
        }),
        // 内容滑出
        Animated.timing(slideAnim, {
          toValue: slideOutValue,
          duration: 250,
          useNativeDriver: true,
          easing: Easing.in(Easing.ease),
        }),
      ]).start(() => {
        setModalVisible(false);
      });
    }
  }, [visible, fadeAnim, slideAnim, backdropOpacity, getSlideOutValue]);

  const getPositionStyle = useCallback((): ViewStyle => {
    switch (position) {
      case 'bottom':
        return { justifyContent: 'flex-end' };
      case 'top':
        return { justifyContent: 'flex-start' };
      case 'center':
        return { justifyContent: 'center' };
      default:
        return { justifyContent: 'flex-end' };
    }
  }, [position]);

  const getContentStyle = useCallback((): ViewStyle => {
    switch (position) {
      case 'bottom':
        return {
          width: '100%',
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
        };
      case 'top':
        return {
          width: '100%',
          borderBottomLeftRadius: 16,
          borderBottomRightRadius: 16,
        };
      case 'center':
        return {
          borderRadius: 16,
          marginHorizontal: 20,
        };
      default:
        return {
          width: '100%',
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
        };
    }
  }, [position]);

  const handleBackdropPress = () => {
    if (closeOnBackdropPress) {
      onClose();
    }
  };

  return (
    <Modal
      transparent
      visible={modalVisible}
      animationType="none"
      onRequestClose={onClose}
    >
      <View
        style={[
          {
            flex: 1,
            ...getPositionStyle(),
          } as ViewStyle,
          containerStyle,
        ]}
      >
        <TouchableWithoutFeedback onPress={handleBackdropPress}>
          <Animated.View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'black',
              opacity: fadeAnim,
            }}
          />
        </TouchableWithoutFeedback>

        <Animated.View
          style={[
            {
              backgroundColor: 'white',
              transform: [{ translateY: slideAnim }],
              ...getContentStyle(),
            } as ViewStyle,
            contentStyle,
          ]}
        >
          {children}
        </Animated.View>
      </View>
    </Modal>
  );
};

export default Popover;
