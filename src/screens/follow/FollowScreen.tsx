import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { FollowStackParamList } from '@navigation/types';
import Header from '@components/common/Header';
import ScrollTabs from '../../components/common/ScrollTabs';
import Card from '../../components/common/Card';

type Props = NativeStackScreenProps<FollowStackParamList, 'Follow'>;

interface FollowItem {
  id: string;
  name: string;
  type: 'company' | 'person' | 'project' | 'performance' | 'patent';
  description: string;
  updatedAt: string;
  hasUpdate: boolean;
  avatar?: string;
  tags: string[];
}

const FollowScreen: React.FC<Props> = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState('all');
  const [refreshing, setRefreshing] = useState(false);

  const tabs = [
    { id: 'all', title: '全部' },
    { id: 'company', title: '企业' },
    { id: 'person', title: '人员' },
    { id: 'project', title: '项目' },
    { id: 'performance', title: '业绩' },
    { id: 'patent', title: '专利' },
  ];

  // 模拟关注数据
  const followItems: FollowItem[] = [
    {
      id: '1',
      name: '某建筑集团有限公司',
      type: 'company',
      description: '国家一级建筑施工企业，专注于大型基础设施建设',
      updatedAt: '2023-06-15',
      hasUpdate: true,
      tags: ['建筑', '基础设施'],
    },
    {
      id: '301',
      name: '张工程',
      type: 'person',
      description:
        '高级工程师，从事建筑工程领域多年，具有丰富的项目管理和技术经验',
      updatedAt: '2023-06-10',
      hasUpdate: false,
      tags: ['工程师', '建筑'],
    },
    {
      id: '201',
      name: '某城市中心商业综合体项目',
      type: 'performance',
      description:
        '总建筑面积约10万平方米的城市综合体项目，包含商业、办公、娱乐等多种功能',
      updatedAt: '2023-06-12',
      hasUpdate: true,
      tags: ['商业', '综合体'],
    },
    {
      id: '101',
      name: '城市地下综合管廊工程',
      type: 'project',
      description: '某市首个地下综合管廊示范项目，全长约5公里',
      updatedAt: '2023-06-08',
      hasUpdate: false,
      tags: ['管廊', '市政'],
    },
    {
      id: '2',
      name: '某建筑设计院有限公司',
      type: 'company',
      description: '大型甲级建筑设计院，拥有建筑行业多项专业资质',
      updatedAt: '2023-06-02',
      hasUpdate: false,
      tags: ['设计', '建筑'],
    },
    {
      id: '401',
      name: '一种新型节能墙体结构',
      type: 'patent',
      description: '一种应用于建筑外墙的新型节能结构，可有效降低建筑能耗',
      updatedAt: '2023-06-05',
      hasUpdate: true,
      tags: ['节能', '墙体', '专利'],
    },
  ];

  // 根据当前选中的tab筛选数据
  const filteredItems = followItems.filter(
    item => activeTab === 'all' || item.type === activeTab,
  );

  const handleTabPress = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleItemPress = (item: FollowItem) => {
    // 由于导航限制，这里需要跨栈导航到Detail栈，所以先不实现
    // 实际项目中需要根据具体的导航结构进行调整
    console.log('Navigate to detail:', item.id, item.type);
  };

  const handleRefresh = () => {
    setRefreshing(true);
    // 模拟刷新数据
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const handleSettings = () => {
    navigation.navigate('FollowSettings');
  };

  const renderFollowItem = ({ item }: { item: FollowItem }) => (
    <TouchableOpacity onPress={() => handleItemPress(item)}>
      <Card className="mb-3 p-4">
        <View className="flex-row justify-between items-start">
          <View className="flex-1">
            <Text className="text-lg font-bold">{item.name}</Text>
            <Text className="text-gray-500 text-sm mt-1">
              {getTypeLabel(item.type)}
            </Text>
          </View>
          {item.hasUpdate && (
            <View className="bg-red-50 px-2 py-1 rounded">
              <Text className="text-red-500 text-xs">有更新</Text>
            </View>
          )}
        </View>

        <Text className="text-gray-600 text-sm mt-2" numberOfLines={2}>
          {item.description}
        </Text>

        <View className="flex-row justify-between mt-2">
          <View className="flex-row flex-wrap">
            {item.tags.map((tag, index) => (
              <View
                key={index}
                className="bg-gray-100 rounded-full px-2 py-0.5 mr-1 mb-1"
              >
                <Text className="text-gray-600 text-xs">{tag}</Text>
              </View>
            ))}
          </View>
          <Text className="text-gray-400 text-xs">{item.updatedAt}</Text>
        </View>
      </Card>
    </TouchableOpacity>
  );

  // 获取类型对应的中文标签
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'company':
        return '企业';
      case 'person':
        return '人员';
      case 'project':
        return '项目';
      case 'performance':
        return '业绩';
      case 'patent':
        return '专利';
      default:
        return '未知';
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <Header
        title="我的关注"
        showBackButton={false}
        rightComponent={
          <TouchableOpacity onPress={handleSettings}>
            <Text className="text-blue-500">设置</Text>
          </TouchableOpacity>
        }
      />

      {/* 选项卡 */}
      <ScrollTabs
        tabs={tabs}
        activeTab={activeTab}
        onTabPress={handleTabPress}
      />

      {/* 关注列表 */}
      <FlatList
        data={filteredItems}
        renderItem={renderFollowItem}
        keyExtractor={item => item.id}
        contentContainerStyle={{ padding: 16 }}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => (
          <View className="items-center justify-center py-10">
            <Text className="text-gray-400">暂无关注内容</Text>
          </View>
        )}
      />
    </SafeAreaView>
  );
};

export default FollowScreen;
