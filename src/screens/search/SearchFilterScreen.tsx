import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { GraphStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';

type Props = NativeStackScreenProps<GraphStackParamList, 'SearchFilter'>;

interface FilterOption {
  id: string;
  name: string;
}

const SearchFilterScreen: React.FC<Props> = ({ navigation, route }) => {
  const { type } = route.params;
  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([]);
  const [selectedSizes, setSelectedSizes] = useState<string[]>([]);
  const [selectedSortBy, setSelectedSortBy] = useState('relevance');

  // 模拟筛选选项
  const regions: FilterOption[] = [
    { id: 'beijing', name: '北京' },
    { id: 'shanghai', name: '上海' },
    { id: 'guangzhou', name: '广州' },
    { id: 'shenzhen', name: '深圳' },
    { id: 'hangzhou', name: '杭州' },
    { id: 'nanjing', name: '南京' },
    { id: 'chengdu', name: '成都' },
    { id: 'wuhan', name: '武汉' },
    { id: 'xiamen', name: '厦门' },
    { id: 'tianjin', name: '天津' },
  ];

  const industries: FilterOption[] = [
    { id: 'construction', name: '建筑业' },
    { id: 'civil', name: '土木工程' },
    { id: 'municipal', name: '市政工程' },
    { id: 'road', name: '公路工程' },
    { id: 'decoration', name: '装饰装修' },
    { id: 'water', name: '水利水电' },
    { id: 'real_estate', name: '房地产' },
    { id: 'design', name: '建筑设计' },
    { id: 'consulting', name: '工程咨询' },
    { id: 'material', name: '建材生产' },
  ];

  const sizes: FilterOption[] = [
    { id: 'large', name: '大型企业' },
    { id: 'medium', name: '中型企业' },
    { id: 'small', name: '小型企业' },
    { id: 'micro', name: '微型企业' },
  ];

  const sortOptions: FilterOption[] = [
    { id: 'relevance', name: '相关度' },
    { id: 'time_desc', name: '时间从新到旧' },
    { id: 'time_asc', name: '时间从旧到新' },
    { id: 'name_asc', name: '名称A-Z' },
    { id: 'name_desc', name: '名称Z-A' },
  ];

  const toggleRegion = (id: string) => {
    setSelectedRegions(prev =>
      prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id],
    );
  };

  const toggleIndustry = (id: string) => {
    setSelectedIndustries(prev =>
      prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id],
    );
  };

  const toggleSize = (id: string) => {
    setSelectedSizes(prev =>
      prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id],
    );
  };

  const handleReset = () => {
    setSelectedRegions([]);
    setSelectedIndustries([]);
    setSelectedSizes([]);
    setSelectedSortBy('relevance');
  };

  const handleApply = () => {
    // 实现应用筛选逻辑，可以将筛选条件传回到搜索结果页面
    navigation.goBack();
  };

  const renderFilterSection = (
    title: string,
    options: FilterOption[],
    selectedValues: string[],
    onToggle: (id: string) => void,
  ) => (
    <View className="mb-6">
      <Text className="font-bold text-lg mb-3">{title}</Text>
      <View className="flex-row flex-wrap">
        {options.map(option => (
          <TouchableOpacity
            key={option.id}
            className={`rounded-full px-4 py-2 mr-2 mb-2 ${
              selectedValues.includes(option.id)
                ? 'bg-blue-100 border border-blue-500'
                : 'bg-gray-100'
            }`}
            onPress={() => onToggle(option.id)}
          >
            <Text
              className={
                selectedValues.includes(option.id)
                  ? 'text-blue-500'
                  : 'text-gray-700'
              }
            >
              {option.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-white">
      <Header
        title="筛选"
        showBackButton
        onBackPress={() => navigation.goBack()}
        rightComponent={
          <TouchableOpacity onPress={handleReset}>
            <Text className="text-blue-500">重置</Text>
          </TouchableOpacity>
        }
      />

      <ScrollView className="flex-1 p-4">
        {renderFilterSection('地区', regions, selectedRegions, toggleRegion)}
        {renderFilterSection(
          '行业',
          industries,
          selectedIndustries,
          toggleIndustry,
        )}

        {type === 'company' &&
          renderFilterSection('企业规模', sizes, selectedSizes, toggleSize)}

        <View className="mb-6">
          <Text className="font-bold text-lg mb-3">排序方式</Text>
          {sortOptions.map(option => (
            <TouchableOpacity
              key={option.id}
              className="flex-row items-center py-3 border-b border-gray-100"
              onPress={() => setSelectedSortBy(option.id)}
            >
              <View
                className={`w-5 h-5 rounded-full border ${
                  selectedSortBy === option.id
                    ? 'border-blue-500 bg-blue-500'
                    : 'border-gray-300'
                } mr-3 items-center justify-center`}
              >
                {selectedSortBy === option.id && (
                  <View className="w-2.5 h-2.5 rounded-full bg-white" />
                )}
              </View>
              <Text className="text-gray-700">{option.name}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      <View className="p-4 border-t border-gray-200">
        <TouchableOpacity
          className="bg-blue-500 rounded-md py-3 items-center"
          onPress={handleApply}
        >
          <Text className="text-white font-bold">应用筛选</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default SearchFilterScreen;
