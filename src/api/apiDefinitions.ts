/// <reference types='./globals.d.ts' />
/* tslint:disable */
/* eslint-disable */
/**
 * 启魔方1.0.1 - version 1.0.0
 *
 *
 *
 * OpenAPI version:
 *
 *
 * NOTE: This file is auto generated by the alova's vscode plugin.
 *
 * https://alova.js.org/devtools/vscode
 *
 * **Do not edit the file manually.**
 */
export default {
  'general.post_aicustomers_public_mobstatus': ['POST', '/aicustomers/public/mobstatus'],
  'general.post_aicustomers_map_toker_query': ['POST', '/aicustomers/map/toker/query'],
  'general.post_aicustomers_map_heat_heatmap': ['POST', '/aicustomers/map/heat/heatMap'],
  'general.post_aicustomers_map_batch_receive': ['POST', '/aicustomers/map/batch/receive'],
  'general.get_aicustomers_appcenter_list': ['GET', '/aicustomers/appcenter/list'],
  'general.post_aicustomers_appcenter_trial': ['POST', '/aicustomers/appcenter/trial'],
  'general.post_aicustomers_appcenter_list_patent_enterprises': [
    'POST',
    '/aicustomers/appcenter/list/patent-enterprises'
  ],
  'general.post_aicustomers_appcenter_list_job_enterprises': ['POST', '/aicustomers/appcenter/list/job-enterprises'],
  'general.post_aicustomers_appcenter_list_trademark_enterprises': [
    'POST',
    '/aicustomers/appcenter/list/trademark-enterprises'
  ],
  'general.post_aicustomers_appcenter_list_baidu_enterprises': [
    'POST',
    '/aicustomers/appcenter/list/baidu-enterprises'
  ],
  'general.post_aicustomers_appcenter_list_wechat_enterprises': [
    'POST',
    '/aicustomers/appcenter/list/wechat-enterprises'
  ],
  'general.post_aicustomers_appcenter_patent_count': ['POST', '/aicustomers/appcenter/patent/count'],
  'general.post_aicustomers_appcenter_job_count': ['POST', '/aicustomers/appcenter/job/count'],
  'general.post_aicustomers_appcenter_tminfo_count': ['POST', '/aicustomers/appcenter/tminfo/count'],
  'general.post_aicustomers_appcenter_weixin_count': ['POST', '/aicustomers/appcenter/weixin/count'],
  'general.post_aicustomers_appcenter_baidu_count': ['POST', '/aicustomers/appcenter/baidu/count'],
  'general.post_aicustomers_appcenter_patent_query': ['POST', '/aicustomers/appcenter/patent/query'],
  'general.post_aicustomers_appcenter_job_query': ['POST', '/aicustomers/appcenter/job/query'],
  'general.post_aicustomers_appcenter_tminfo_query': ['POST', '/aicustomers/appcenter/tminfo/query'],
  'general.post_aicustomers_appcenter_baidu_query': ['POST', '/aicustomers/appcenter/baidu/query'],
  'general.post_aicustomers_appcenter_weixin_query': ['POST', '/aicustomers/appcenter/weixin/query'],
  'general.get_aicustomers_appcenter_patent_detail': ['GET', '/aicustomers/appcenter/patent/detail'],
  'general.get_aicustomers_appcenter_tminfo_detail': ['GET', '/aicustomers/appcenter/tminfo/detail'],
  'general.get_aicustomers_appcenter_baidu_detail': ['GET', '/aicustomers/appcenter/baidu/detail'],
  'general.get_aicustomers_appcenter_job_detail': ['GET', '/aicustomers/appcenter/job/detail'],
  'general.get_aicustomers_appcenter_weixn_detail': ['GET', '/aicustomers/appcenter/weixn/detail'],
  'general.get_aicustomers_appcenter_provinces': ['GET', '/aicustomers/appcenter/provinces'],
  'general.get_aicustomers_appcenter_bidtypes': ['GET', '/aicustomers/appcenter/bidTypes'],
  'general.post_aicustomers_appcenter_bid_count': ['POST', '/aicustomers/appcenter/bid/count'],
  'general.post_aicustomers_appcenter_bid_query': ['POST', '/aicustomers/appcenter/bid/query'],
  'general.get_aicustomers_appcenter_bid_detail': ['GET', '/aicustomers/appcenter/bid/detail'],
  'general.post_aicustomers_contacts_query': ['POST', '/aicustomers/contacts/query'],
  'general.post_aicustomers_contacts_pushforconvertsolution': ['POST', '/aicustomers/contacts/pushforconvertsolution'],
  'general.get_aicustomers_contacts_single_query': ['GET', '/aicustomers/contacts/single/query'],
  'general.post_aicustomers_contacts_grab': ['POST', '/aicustomers/contacts/grab'],
  'general.post_aicustomers_contacts_dispatch': ['POST', '/aicustomers/contacts/dispatch'],
  'general.post_aicustomers_contacts_dispatch_enterprise': ['POST', '/aicustomers/contacts/dispatch/enterprise'],
  'general.post_aicustomers_contacts_dispatch_enterprise_batch_v2': [
    'POST',
    '/aicustomers/contacts/dispatch/enterprise-batch/v2'
  ],
  'general.post_aicustomers_contacts_dispatch_enterprise_batch': [
    'POST',
    '/aicustomers/contacts/dispatch/enterprise-batch'
  ],
  'general.post_aicustomers_contacts_dispatch_contact_batch': ['POST', '/aicustomers/contacts/dispatch/contact-batch'],
  'general.post_aicustomers_contacts_dispatch_enterprise_num': [
    'POST',
    '/aicustomers/contacts/dispatch/enterprise-num'
  ],
  'general.post_aicustomers_contacts_dispatch_enterprise_num_v2': [
    'POST',
    '/aicustomers/contacts/dispatch/enterprise-num/v2'
  ],
  'general.delete_aicustomers_contacts': ['DELETE', '/aicustomers/contacts'],
  'general.delete_aicustomers_contacts_all': ['DELETE', '/aicustomers/contacts/all'],
  'general.put_aicustomers_contacts_wish': ['PUT', '/aicustomers/contacts/wish'],
  'general.get_aicustomers_contacts_grab_num': ['GET', '/aicustomers/contacts/grab-num'],
  'general.post_aicustomers_contacts_multi_convert_clue': ['POST', '/aicustomers/contacts/multi/convert/clue'],
  'general.post_aicustomers_contacts_convert_clue_async': ['POST', '/aicustomers/contacts/convert/clue/async'],
  'general.post_aicustomers_contacts_convert_clue': ['POST', '/aicustomers/contacts/convert/clue'],
  'general.post_aicustomers_contacts_check_count': ['POST', '/aicustomers/contacts/check-count'],
  'general.post_aicustomers_contacts_mobile_check': ['POST', '/aicustomers/contacts/mobile-check'],
  'general.get_aicustomers_contacts_relation_info': ['GET', '/aicustomers/contacts/relation-info'],
  'general.put_aicustomers_contacts_ent_state': ['PUT', '/aicustomers/contacts/ent_state'],
  'general.put_aicustomers_contacts_remark': ['PUT', '/aicustomers/contacts/remark'],
  'general.post_aicustomers_contacts_telrobot_check': ['POST', '/aicustomers/contacts/telrobot/check'],
  'general.post_aicustomers_contacts_telrobot_launch': ['POST', '/aicustomers/contacts/telrobot/launch'],
  'general.post_aicustomers_contacts_telrobot_launch_all': ['POST', '/aicustomers/contacts/telrobot/launch/all'],
  'general.post_aicustomers_contacts_telrobot_launch_all_check': [
    'POST',
    '/aicustomers/contacts/telrobot/launch/all/check'
  ],
  'general.put_aicustomers_contacts_telrobot_tag': ['PUT', '/aicustomers/contacts/telrobot/tag'],
  'general.get_aicustomers_contacts_custom_tag': ['GET', '/aicustomers/contacts/custom/tag'],
  'general.put_aicustomers_contacts_custom_tag': ['PUT', '/aicustomers/contacts/custom/tag'],
  'general.delete_aicustomers_contacts_custom_tag': ['DELETE', '/aicustomers/contacts/custom/tag'],
  'general.get_aicustomers_contacts_number_tag': ['GET', '/aicustomers/contacts/number/tag'],
  'general.post_aicustomers_enterprises_query': ['POST', '/aicustomers/enterprises/query'],
  'general.post_aicustomers_enterprises_try_grab': ['POST', '/aicustomers/enterprises/try-grab'],
  'general.post_aicustomers_enterprises_grab_batch': ['POST', '/aicustomers/enterprises/grab/batch'],
  'general.get_aicustomers_enterprises_stats': ['GET', '/aicustomers/enterprises/stats'],
  'general.get_aicustomers_enterprises_conditions': ['GET', '/aicustomers/enterprises/conditions'],
  'general.get_aicustomers_code_nic': ['GET', '/aicustomers/code/nic'],
  'general.get_aicustomers_code_nic_with_parent': ['GET', '/aicustomers/code/nic-with-parent'],
  'general.get_aicustomers_code_general': ['GET', '/aicustomers/code/general'],
  'general.get_aicustomers_code_ca': ['GET', '/aicustomers/code/ca'],
  'general.get_aicustomers_code_ca_with_parent': ['GET', '/aicustomers/code/ca-with-parent'],
  'general.get_aicustomers_code_region': ['GET', '/aicustomers/code/region'],
  'general.get_aicustomers_code_region_with_parent': ['GET', '/aicustomers/code/region-with-parent'],
  'general.get_aicustomers_code_phone_region': ['GET', '/aicustomers/code/phone-region'],
  'general.post_aicustomers_enterprises_grab': ['POST', '/aicustomers/enterprises/grab'],
  'general.post_aicustomers_enterprises_grabmonitor': ['POST', '/aicustomers/enterprises/grabmonitor'],
  'general.post_aicustomers_enterprises_grab_uptodate': ['POST', '/aicustomers/enterprises/grab/uptodate'],
  'general.post_aicustomers_enterprises_grab_async': ['POST', '/aicustomers/enterprises/grab/async'],
  'general.post_aicustomers_enterprises_grab_async_uptodate': ['POST', '/aicustomers/enterprises/grab/async/uptodate'],
  'general.get_aicustomers_enterprises_names_register': ['GET', '/aicustomers/enterprises/names/register'],
  'general.delete_aicustomers_enterprises': ['DELETE', '/aicustomers/enterprises'],
  'general.post_aicustomers_enterprises_query_super': ['POST', '/aicustomers/enterprises/query/super'],
  'general.get_aicustomers_enterprises_hot_nic': ['GET', '/aicustomers/enterprises/hot/nic'],
  'general.get_aicustomers_keyword_search': ['GET', '/aicustomers/keyword/search'],
  'general.post_aicustomers_grab_view_status': ['POST', '/aicustomers/grab/view-status'],
  'general.get_aicustomers_third_app_config_bycode': ['GET', '/aicustomers/third-app/config-bycode'],
  'general.get_aicustomers_third_app_config': ['GET', '/aicustomers/third-app/config'],
  'general.get_aicustomers_third_app_try_unlock': ['GET', '/aicustomers/third-app/try-unlock'],
  'general.post_aicustomers_third_app_seed': ['POST', '/aicustomers/third-app/seed'],
  'general.post_aicustomers_enterprises_collect_save': ['POST', '/aicustomers/enterprises/collect/save'],
  'general.post_aicustomers_enterprises_collect_list': ['POST', '/aicustomers/enterprises/collect/list'],
  'general.post_aicustomers_enterprises_collect_query': ['POST', '/aicustomers/enterprises/collect/query'],
  'general.post_aicustomers_batch_excel_query': ['POST', '/aicustomers/batch/excel/query'],
  'general.post_aicustomers_batch_txt_query': ['POST', '/aicustomers/batch/txt/query'],
  'general.post_aicustomers_batch_history_excel': ['POST', '/aicustomers/batch/history/excel'],
  'general.delete_aicustomers_batch_history': ['DELETE', '/aicustomers/batch/history'],
  'general.post_aicustomers_batch_history_txt': ['POST', '/aicustomers/batch/history/txt'],
  'general.post_aicustomers_batch_history_last_basicinfo': ['POST', '/aicustomers/batch/history/last/basicinfo'],
  'general.post_aicustomers_batch_history_last_data': ['POST', '/aicustomers/batch/history/last/data'],
  'general.get_aicustomers_batch_ent_query': ['GET', '/aicustomers/batch/ent/query'],
  'general.get_aicustomers_website_home_enterprises_names': ['GET', '/aicustomers/website/home/<USER>/names'],
  'general.get_aicustomers_website_home_templates_first_nic': ['GET', '/aicustomers/website/home/<USER>/first-nic'],
  'general.get_aicustomers_website_home_enterprises_latest_regist': [
    'GET',
    '/aicustomers/website/home/<USER>/latest-regist'
  ],
  'general.post_aicustomers_website_home_enterprises_nearby': ['POST', '/aicustomers/website/home/<USER>/nearby'],
  'general.get_aicustomers_annualreport_years': ['GET', '/aicustomers/annualreport/years'],
  'general.get_aicustomers_annualreport_baseinfo': ['GET', '/aicustomers/annualreport/baseinfo'],
  'general.get_aicustomers_annualreport_equityalter': ['GET', '/aicustomers/annualreport/equityalter'],
  'general.get_aicustomers_annualreport_stockholder': ['GET', '/aicustomers/annualreport/stockholder'],
  'general.get_aicustomers_annualreport_invest': ['GET', '/aicustomers/annualreport/invest'],
  'general.get_aicustomers_annualreport_vouch': ['GET', '/aicustomers/annualreport/vouch'],
  'general.get_aicustomers_annualreport_website': ['GET', '/aicustomers/annualreport/website'],
  'general.get_aicustomers_annualreport_alter': ['GET', '/aicustomers/annualreport/alter'],
  'general.get_v2_aicustomers_enterprises_simple_infos': ['GET', '/v2/aicustomers/enterprises/simple-infos'],
  'general.post_v2_aicustomers_enterprises_convert_clue': ['POST', '/v2/aicustomers/enterprises/convert/clue'],
  'general.post_v2_aicustomers_enterprises_convert_customer': ['POST', '/v2/aicustomers/enterprises/convert/customer'],
  'general.post_v2_aicustomers_enterprises_create_customer': ['POST', '/v2/aicustomers/enterprises/create/customer'],
  'general.post_v2_aicustomers_enterprises_bind_customer': ['POST', '/v2/aicustomers/enterprises/bind/customer'],
  'general.get_v2_aicustomers_enterprises_customers_list': ['GET', '/v2/aicustomers/enterprises/customers/list'],
  'general.get_v2_aicustomers_enterprises_customers_detail': ['GET', '/v2/aicustomers/enterprises/customers/detail'],
  'general.get_aicustomers_enterpriserisk_logout_revocationinfo': [
    'GET',
    '/aicustomers/enterpriserisk/logout-revocationinfo'
  ],
  'general.get_aicustomers_enterpriserisk_restrict_consumption': [
    'GET',
    '/aicustomers/enterpriserisk/restrict-consumption'
  ],
  'general.get_aicustomers_enterpriserisk_restrict_consumption_detail': [
    'GET',
    '/aicustomers/enterpriserisk/restrict-consumption-detail'
  ],
  'general.get_aicustomers_enterpriserisk_lesscredit_executed_personinfo': [
    'GET',
    '/aicustomers/enterpriserisk/lesscredit-executed-personinfo'
  ],
  'general.get_aicustomers_enterpriserisk_illegal_dishonest': ['GET', '/aicustomers/enterpriserisk/illegal-dishonest'],
  'general.get_aicustomers_enterpriserisk_filing_info': ['GET', '/aicustomers/enterpriserisk/filing-info'],
  'general.get_aicustomers_enterpriserisk_filing_info_total': ['GET', '/aicustomers/enterpriserisk/filing-info-total'],
  'general.get_aicustomers_enterpriserisk_owing_tax': ['GET', '/aicustomers/enterpriserisk/owing-tax'],
  'general.get_aicustomers_enterpriserisk_bankruptcy_info': ['GET', '/aicustomers/enterpriserisk/bankruptcy-info'],
  'general.get_aicustomers_enterpriserisk_bankruptcy_detail': ['GET', '/aicustomers/enterpriserisk/bankruptcy-detail'],
  'general.get_aicustomers_enterpriserisk_judicial_assistances': [
    'GET',
    '/aicustomers/enterpriserisk/judicial/assistances'
  ],
  'general.get_aicustomers_enterpriserisk_judicial_assistance_detail': [
    'GET',
    '/aicustomers/enterpriserisk/judicial/assistance/detail'
  ],
  'general.get_aicustomers_enterpriserisk_chattel_mortgages': ['GET', '/aicustomers/enterpriserisk/chattel/mortgages'],
  'general.get_aicustomers_enterpriserisk_chattel_mortgage_detail': [
    'GET',
    '/aicustomers/enterpriserisk/chattel/mortgage/detail'
  ],
  'general.get_aicustomers_enterpriserisk_chattel_mortgage_goods': [
    'GET',
    '/aicustomers/enterpriserisk/chattel/mortgage/goods'
  ],
  'general.get_aicustomers_enterpriserisk_tax_exceptions': ['GET', '/aicustomers/enterpriserisk/tax/exceptions'],
  'general.get_aicustomers_enterpriserisk_tax_illegals': ['GET', '/aicustomers/enterpriserisk/tax/illegals'],
  'general.get_aicustomers_enterpriserisk_tax_penalties': ['GET', '/aicustomers/enterpriserisk/tax/penalties'],
  'general.get_aicustomers_enterpriserisk_auction_info': ['GET', '/aicustomers/enterpriserisk/auction-info'],
  'general.get_aicustomers_enterpriserisk_auction_detail': ['GET', '/aicustomers/enterpriserisk/auction-detail'],
  'general.get_aicustomers_enterpriserisk_service_notice': ['GET', '/aicustomers/enterpriserisk/service-notice'],
  'general.get_aicustomers_enterpriserisk_service_notice_total': [
    'GET',
    '/aicustomers/enterpriserisk/service-notice-total'
  ],
  'general.get_aicustomers_enterpriserisk_notice_detail': ['GET', '/aicustomers/enterpriserisk/notice-detail'],
  'general.get_aicustomers_enterpriserisk_node_child': ['GET', '/aicustomers/enterpriserisk/node-child'],
  'general.get_aicustomers_enterpriserisk_node_child_v2': ['GET', '/aicustomers/enterpriserisk/node-child/v2'],
  'general.get_aicustomers_enterpriserisk_node_child_v3': ['GET', '/aicustomers/enterpriserisk/node-child/v3'],
  'general.get_aicustomers_searchtemplate_condition_list': ['GET', '/aicustomers/searchtemplate/condition/list'],
  'general.get_aicustomers_searchtemplate_condition_category': [
    'GET',
    '/aicustomers/searchtemplate/condition/category'
  ],
  'general.post_aicustomers_searchtemplate': ['POST', '/aicustomers/searchtemplate'],
  'general.put_aicustomers_searchtemplate': ['PUT', '/aicustomers/searchtemplate'],
  'general.delete_aicustomers_searchtemplate': ['DELETE', '/aicustomers/searchtemplate'],
  'general.get_aicustomers_searchtemplate_mine': ['GET', '/aicustomers/searchtemplate/mine'],
  'general.get_aicustomers_website_free_code_region_with_parent': [
    'GET',
    '/aicustomers/website/free/code/region-with-parent'
  ],
  'general.get_aicustomers_website_free_code_nic_with_parent': [
    'GET',
    '/aicustomers/website/free/code/nic-with-parent'
  ],
  'general.get_aicustomers_website_free_code_general': ['GET', '/aicustomers/website/free/code/general'],
  'general.get_aicustomers_website_free_condition_list': ['GET', '/aicustomers/website/free/condition/list'],
  'general.get_aicustomers_website_free_condition_category': ['GET', '/aicustomers/website/free/condition/category'],
  'general.get_aicustomers_website_free_templates_nic': ['GET', '/aicustomers/website/free/templates/nic'],
  'general.post_aicustomers_website_free_enterprises': ['POST', '/aicustomers/website/free/enterprises'],
  'general.post_aicustomers_website_free_enterprises_by_template': [
    'POST',
    '/aicustomers/website/free/enterprises/by-template'
  ],
  'general.get_aicustomers_website_free_enterprises_stats': ['GET', '/aicustomers/website/free/enterprises/stats'],
  'general.get_aicustomers_website_free_enterprises_conditions': [
    'GET',
    '/aicustomers/website/free/enterprises/conditions'
  ],
  'general.post_aicustomers_website_free_enterprises_query': ['POST', '/aicustomers/website/free/enterprises/query'],
  'general.get_aicustomers_website_free_enterprise_query': ['GET', '/aicustomers/website/free/enterprise/query'],
  'general.get_aicustomers_website_free_contacts': ['GET', '/aicustomers/website/free/contacts'],
  'general.get_aicustomers_enterpage_contacts': ['GET', '/aicustomers/enterpage/contacts'],
  'general.get_aicustomers_enterpage_relationship_contacts': ['GET', '/aicustomers/enterpage/relationship-contacts'],
  'general.get_aicustomers_enterpage_contacts_source_v2': ['GET', '/aicustomers/enterpage/contacts/source-v2'],
  'general.get_aicustomers_enterpage_contacts_relatioinship_source': [
    'GET',
    '/aicustomers/enterpage/contacts/relatioinship-source'
  ],
  'general.get_aicustomers_enterpage_contacts_uptodate': ['GET', '/aicustomers/enterpage/contacts/uptodate'],
  'general.get_aicustomers_enterpage_contacts_count': ['GET', '/aicustomers/enterpage/contacts/count'],
  'general.get_aicustomers_enterpage_contacts_count_with_key_person': [
    'GET',
    '/aicustomers/enterpage/contacts/count-with-key-person'
  ],
  'general.get_aicustomers_enterpage_contacts_source': ['GET', '/aicustomers/enterpage/contacts/source'],
  'general.get_aicustomers_enterpage_contacts_source_uptodate': [
    'GET',
    '/aicustomers/enterpage/contacts/source/uptodate'
  ],
  'general.get_aicustomers_enterpage_risk_abnormal': ['GET', '/aicustomers/enterpage/risk/abnormal'],
  'general.get_aicustomers_enterpage_market_wechatapplet': ['GET', '/aicustomers/enterpage/market/wechatapplet'],
  'general.get_aicustomers_enterpage_market_goods_statistics': [
    'GET',
    '/aicustomers/enterpage/market/goods_statistics'
  ],
  'general.get_aicustomers_enterpage_market_shop_list': ['GET', '/aicustomers/enterpage/market/shop_list'],
  'general.get_aicustomers_enterpage_market_ecommerce_statistics': [
    'GET',
    '/aicustomers/enterpage/market/ecommerce/statistics'
  ],
  'general.get_aicustomers_enterpage_market_ecommerce_shops': ['GET', '/aicustomers/enterpage/market/ecommerce/shops'],
  'general.get_aicustomers_enterpage_market_ecommerce_goods': ['GET', '/aicustomers/enterpage/market/ecommerce/goods'],
  'general.get_aicustomers_enterprise_detail': ['GET', '/aicustomers/enterprise/detail'],
  'general.get_aicustomers_enterprise_business': ['GET', '/aicustomers/enterprise/business'],
  'general.get_aicustomers_enterprise_ipo_info': ['GET', '/aicustomers/enterprise/ipo-info'],
  'general.get_aicustomers_enterprise_risks': ['GET', '/aicustomers/enterprise/risks'],
  'general.get_aicustomers_enterprise_manages': ['GET', '/aicustomers/enterprise/manages'],
  'general.get_aicustomers_enterprise_marketing': ['GET', '/aicustomers/enterprise/marketing'],
  'general.get_aicustomers_enterprise_intellectual': ['GET', '/aicustomers/enterprise/intellectual'],
  'general.get_aicustomers_enterprise_tender': ['GET', '/aicustomers/enterprise/tender'],
  'general.get_aicustomers_enterprise_business_base': ['GET', '/aicustomers/enterprise/business/base'],
  'general.get_aicustomers_enterprise_business_alterations': ['GET', '/aicustomers/enterprise/business/alterations'],
  'general.get_aicustomers_enterprise_business_branches': ['GET', '/aicustomers/enterprise/business/branches'],
  'general.get_aicustomers_enterprise_business_finance_courses': [
    'GET',
    '/aicustomers/enterprise/business/finance/courses'
  ],
  'general.get_aicustomers_enterprise_business_shareholders': ['GET', '/aicustomers/enterprise/business/shareholders'],
  'general.get_aicustomers_enterprise_business_legalperson': ['GET', '/aicustomers/enterprise/business/legalperson'],
  'general.get_aicustomers_enterprise_list_basic': ['GET', '/aicustomers/enterprise/list/basic'],
  'general.get_aicustomers_enterprise_list_finance': ['GET', '/aicustomers/enterprise/list/finance'],
  'general.get_aicustomers_enterprise_list_gposts': ['GET', '/aicustomers/enterprise/list/gposts'],
  'general.get_aicustomers_enterprise_list_holders': ['GET', '/aicustomers/enterprise/list/holders'],
  'general.get_aicustomers_enterprise_list_profiles': ['GET', '/aicustomers/enterprise/list/profiles'],
  'general.get_aicustomers_enterprise_list_discs': ['GET', '/aicustomers/enterprise/list/discs'],
  'general.get_aicustomers_enterprise_judgments': ['GET', '/aicustomers/enterprise/judgments'],
  'general.get_aicustomers_enterprise_judgments_total': ['GET', '/aicustomers/enterprise/judgments-total'],
  'general.get_aicustomers_enterprise_execute_persons': ['GET', '/aicustomers/enterprise/execute-persons'],
  'general.get_aicustomers_enterprise_execute_persons_total': ['GET', '/aicustomers/enterprise/execute-persons-total'],
  'general.get_aicustomers_enterprise_opencourts': ['GET', '/aicustomers/enterprise/opencourts'],
  'general.get_aicustomers_enterprise_courtinfos': ['GET', '/aicustomers/enterprise/courtinfos'],
  'general.get_aicustomers_enterprise_endcases': ['GET', '/aicustomers/enterprise/endcases'],
  'general.get_aicustomers_enterprise_exposures': ['GET', '/aicustomers/enterprise/exposures'],
  'general.get_aicustomers_enterprise_sharesimpawns': ['GET', '/aicustomers/enterprise/sharesimpawns'],
  'general.get_aicustomers_enterprise_caseinfos': ['GET', '/aicustomers/enterprise/caseinfos'],
  'general.get_aicustomers_enterprise_build': ['GET', '/aicustomers/enterprise/build'],
  'general.get_aicustomers_enterprise_news': ['GET', '/aicustomers/enterprise/news'],
  'general.get_aicustomers_enterprise_baidu_bidding': ['GET', '/aicustomers/enterprise/baidu/bidding'],
  'general.get_aicustomers_enterprise_website_monitor': ['GET', '/aicustomers/enterprise/website/monitor'],
  'general.get_aicustomers_enterprise_ipc_record': ['GET', '/aicustomers/enterprise/ipc/record'],
  'general.get_aicustomers_enterprise_b2b_info': ['GET', '/aicustomers/enterprise/b2b/info'],
  'general.get_aicustomers_enterprise_baidu_intro': ['GET', '/aicustomers/enterprise/baidu/intro'],
  'general.get_aicustomers_enterprise_android_market': ['GET', '/aicustomers/enterprise/android/market'],
  'general.get_aicustomers_enterprise_apple_app': ['GET', '/aicustomers/enterprise/apple/app'],
  'general.get_aicustomers_enterprise_weibo': ['GET', '/aicustomers/enterprise/weibo'],
  'general.get_aicustomers_enterprise_wechat_public': ['GET', '/aicustomers/enterprise/wechat/public'],
  'general.post_aicustomers_enterprise_patent': ['POST', '/aicustomers/enterprise/patent'],
  'general.post_aicustomers_enterprise_register': ['POST', '/aicustomers/enterprise/register'],
  'general.get_aicustomers_enterprise_brand': ['GET', '/aicustomers/enterprise/brand'],
  'general.get_aicustomers_enterprise_softwareright': ['GET', '/aicustomers/enterprise/softwareright'],
  'general.get_aicustomers_enterprise_work_copyright': ['GET', '/aicustomers/enterprise/work/copyright'],
  'general.get_aicustomers_enterprise_system_certification': ['GET', '/aicustomers/enterprise/system/certification'],
  'general.get_aicustomers_enterprise_land': ['GET', '/aicustomers/enterprise/land'],
  'general.get_aicustomers_enterprise_product_certification': ['GET', '/aicustomers/enterprise/product/certification'],
  'general.get_aicustomers_enterprise_licence_permission': ['GET', '/aicustomers/enterprise/licence/permission'],
  'general.get_aicustomers_enterprise_list_tmtypes': ['GET', '/aicustomers/enterprise/list/tmtypes'],
  'general.post_aicustomers_enterprise_qualification_certificate': [
    'POST',
    '/aicustomers/enterprise/qualification/certificate'
  ],
  'general.get_aicustomers_enterprise_business_investment': ['GET', '/aicustomers/enterprise/business/investment'],
  'general.get_aicustomers_enterprise_business_inv_graph': ['GET', '/aicustomers/enterprise/business/inv-graph'],
  'general.get_aicustomers_enterprise_business_final_inv': ['GET', '/aicustomers/enterprise/business/final-inv'],
  'general.get_aicustomers_enterprise_marketing_product': ['GET', '/aicustomers/enterprise/marketing/product'],
  'general.get_aicustomers_enterprise_operation_customs': ['GET', '/aicustomers/enterprise/operation/customs'],
  'general.get_aicustomers_enterprise_operation_random_check': [
    'GET',
    '/aicustomers/enterprise/operation/random-check'
  ],
  'general.get_aicustomers_enterprise_operation_random_check_detail': [
    'GET',
    '/aicustomers/enterprise/operation/random-check/detail'
  ],
  'general.get_aicustomers_enterprise_patent_legal_status': ['GET', '/aicustomers/enterprise/patent/legal-status'],
  'general.get_aicustomers_enterprise_operation_tax_state': ['GET', '/aicustomers/enterprise/operation/tax-state'],
  'general.get_aicustomers_enterprise_operation_tax_rating': ['GET', '/aicustomers/enterprise/operation/tax-rating'],
  'general.get_aicustomers_enterprise_operation_case_check': ['GET', '/aicustomers/enterprise/operation/case-check'],
  'general.get_aicustomers_enterprise_operation_fire_check': ['GET', '/aicustomers/enterprise/operation/fire-check'],
  'general.get_aicustomers_enterprise_risk_cause_names': ['GET', '/aicustomers/enterprise/risk/cause-names'],
  'general.get_aicustomers_enterprise_risk_ptypes': ['GET', '/aicustomers/enterprise/risk/ptypes'],
  'general.get_aicustomers_enterprise_job_joblist': ['GET', '/aicustomers/enterprise/job/jobList'],
  'general.get_aicustomers_enterprise_job_charts': ['GET', '/aicustomers/enterprise/job/charts'],
  'general.get_aicustomers_enterprise_job_cities': ['GET', '/aicustomers/enterprise/job/cities'],
  'general.get_aicustomers_enterprise_job_salary': ['GET', '/aicustomers/enterprise/job/salary'],
  'general.get_aicustomers_enterprise_job_experience': ['GET', '/aicustomers/enterprise/job/experience'],
  'general.get_aicustomers_enterprise_management_patent_types': [
    'GET',
    '/aicustomers/enterprise/management/patent-types'
  ],
  'general.get_aicustomers_enterprise_management_brand_types': [
    'GET',
    '/aicustomers/enterprise/management/brand-types'
  ],
  'general.get_aicustomers_enterprise_management_brand_status': [
    'GET',
    '/aicustomers/enterprise/management/brand-status'
  ],
  'general.get_aicustomers_enterprise_bids_bidtype': ['GET', '/aicustomers/enterprise/bids/bidtype'],
  'general.get_aicustomers_enterprise_bids_roles': ['GET', '/aicustomers/enterprise/bids/roles'],
  'general.get_aicustomers_enterprise_market_ios_detail': ['GET', '/aicustomers/enterprise/market/ios-detail'],
  'general.get_aicustomers_enterprise_market_andriod_detail': ['GET', '/aicustomers/enterprise/market/andriod-detail'],
  'general.get_aicustomers_enterprise_market_webo_detail': ['GET', '/aicustomers/enterprise/market/webo-detail'],
  'general.get_aicustomers_enterprise_market_weixin_detail': ['GET', '/aicustomers/enterprise/market/weixin-detail'],
  'general.get_aicustomers_enterprise_risk_judgment_detail': ['GET', '/aicustomers/enterprise/risk/judgment-detail'],
  'general.get_aicustomers_enterprise_management_manage_certification': [
    'GET',
    '/aicustomers/enterprise/management/manage-certification'
  ],
  'general.get_aicustomers_enterprise_management_compulsory_certification': [
    'GET',
    '/aicustomers/enterprise/management/compulsory-certification'
  ],
  'general.get_aicustomers_enterprise_risk_opencourts_detail': [
    'GET',
    '/aicustomers/enterprise/risk/opencourts-detail'
  ],
  'general.get_aicustomers_enterprise_risk_courtinfos_detail': [
    'GET',
    '/aicustomers/enterprise/risk/courtinfos-detail'
  ],
  'general.get_aicustomers_enterprise_risk_filing_info_detail': [
    'GET',
    '/aicustomers/enterprise/risk/filing-info-detail'
  ],
  'general.get_aicustomers_enterprise_risk_endcases_detail': ['GET', '/aicustomers/enterprise/risk/endcases-detail'],
  'general.get_aicustomers_enterprise_risk_executepersons_detail': [
    'GET',
    '/aicustomers/enterprise/risk/executepersons-detail'
  ],
  'general.get_aicustomers_enterprise_risk_exposures_detail': ['GET', '/aicustomers/enterprise/risk/exposures-detail'],
  'general.get_aicustomers_enterprise_risk_caseinfos_detail': ['GET', '/aicustomers/enterprise/risk/caseinfos-detail'],
  'general.get_aicustomers_enterprise_risk_lesscredit_executepersons_detail': [
    'GET',
    '/aicustomers/enterprise/risk/lesscredit-executepersons-detail'
  ],
  'general.get_aicustomers_enterprise_management_patent_detail': [
    'GET',
    '/aicustomers/enterprise/management/patent-detail'
  ],
  'general.get_aicustomers_enterprise_management_software_right_detail': [
    'GET',
    '/aicustomers/enterprise/management/software-right-detail'
  ],
  'general.get_aicustomers_enterprise_management_work_copyright_detail': [
    'GET',
    '/aicustomers/enterprise/management/work-copyright-detail'
  ],
  'general.get_aicustomers_enterprise_management_qualification_certificate_detail': [
    'GET',
    '/aicustomers/enterprise/management/qualification-certificate-detail'
  ],
  'general.get_aicustomers_enterprise_management_licence_permission_detail': [
    'GET',
    '/aicustomers/enterprise/management/licence-permission-detail'
  ],
  'general.get_aicustomers_enterprise_management_land_detail': [
    'GET',
    '/aicustomers/enterprise/management/land-detail'
  ],
  'general.get_aicustomers_enterprise_entevents_build_detail': [
    'GET',
    '/aicustomers/enterprise/entevents/build-detail'
  ],
  'general.get_aicustomers_enterprise_entevents_tender_detail': [
    'GET',
    '/aicustomers/enterprise/entevents/tender-detail'
  ],
  'general.get_aicustomers_enterprise_market_wechatapplet_detail': [
    'GET',
    '/aicustomers/enterprise/market/wechatapplet-detail'
  ],
  'general.get_aicustomers_enterprise_business_merger_info': ['GET', '/aicustomers/enterprise/business/merger-info'],
  'general.get_aicustomers_enterprise_gene': ['GET', '/aicustomers/enterprise/gene'],
  'general.get_aicustomers_enterprise_tax_title': ['GET', '/aicustomers/enterprise/tax/title'],
  'general.post_aicustomers_senior_enterprises_grab_batch': ['POST', '/aicustomers/senior/enterprises/grab/batch'],
  'general.post_aicustomers_senior_enterprises_uptodate_grab_batch': [
    'POST',
    '/aicustomers/senior/enterprises/uptodate/grab/batch'
  ],
  'general.post_aicustomers_senior_enterprises': ['POST', '/aicustomers/senior/enterprises'],
  'general.post_aicustomers_senior_enterprises_by_template': ['POST', '/aicustomers/senior/enterprises/by-template'],
  'general.get_aicustomers_senior_templates_mine': ['GET', '/aicustomers/senior/templates/mine'],
  'general.post_aicustomers_senior_templates': ['POST', '/aicustomers/senior/templates'],
  'general.put_aicustomers_senior_templates': ['PUT', '/aicustomers/senior/templates'],
  'general.delete_aicustomers_senior_templates': ['DELETE', '/aicustomers/senior/templates'],
  'general.get_aicustomers_senior_templates_nic': ['GET', '/aicustomers/senior/templates/nic'],
  'general.get_aicustomers_senior_condition_list': ['GET', '/aicustomers/senior/condition/list'],
  'general.get_aicustomers_senior_condition_list_by_type': ['GET', '/aicustomers/senior/condition/list/by-type'],
  'general.get_aicustomers_senior_condition_category_v3': ['GET', '/aicustomers/senior/condition/category/v3'],
  'general.get_aicustomers_senior_condition_category_uptodate': [
    'GET',
    '/aicustomers/senior/condition/category/uptodate'
  ],
  'general.get_aicustomers_senior_condition_category': ['GET', '/aicustomers/senior/condition/category'],
  'general.post_aicustomers_probe_latest_register_query': ['POST', '/aicustomers/probe/latest-register/query'],
  'general.post_aicustomers_probe_latest_register_query': ['POST', '/aicustomers/probe/latest-register/query'],
  'general.get_aicustomers_probe_latest_register_conditions': ['GET', '/aicustomers/probe/latest-register/conditions'],
  'general.get_aicustomers_probe_ent_basic_client_status': ['GET', '/aicustomers/probe/ent/basic/client-status'],
  'general.post_aicustomers_probe_person_query': ['POST', '/aicustomers/probe/person/query'],
  'general.post_aicustomers_probe_person_query': ['POST', '/aicustomers/probe/person/query'],
  'general.post_aicustomers_bookmark_mine': ['POST', '/aicustomers/bookmark/mine'],
  'general.put_aicustomers_bookmark': ['PUT', '/aicustomers/bookmark'],
  'general.delete_aicustomers_bookmark': ['DELETE', '/aicustomers/bookmark'],
  'general.post_aicustomers_bookmark': ['POST', '/aicustomers/bookmark'],
  'general.get_aicustomers_bookmark_get': ['GET', '/aicustomers/bookmark/get'],
  'general.get_aicustomers_seeds_first': ['GET', '/aicustomers/seeds/first'],
  'general.post_aicustomers_seeds_valid': ['POST', '/aicustomers/seeds/valid'],
  'general.post_aicustomers_seeds_list': ['POST', '/aicustomers/seeds/list'],
  'general.post_aicustomers_seeds_batch': ['POST', '/aicustomers/seeds/batch'],
  'general.post_aicustomers_seeds_batch_upload': ['POST', '/aicustomers/seeds/batch/upload'],
  'general.post_aicustomers_seeds_ent_single': ['POST', '/aicustomers/seeds/ent-single'],
  'general.delete_aicustomers_seeds': ['DELETE', '/aicustomers/seeds'],
  'general.get_aicustomers_senior_search_entid': ['GET', '/aicustomers/senior-search/entid'],
  'general.get_aicustomers_senior_search_getentid': ['GET', '/aicustomers/senior-search/getentid'],
  'general.post_aicustomers_senior_search_advanced_filtering_uptodate': [
    'POST',
    '/aicustomers/senior-search/advanced-filtering/uptodate'
  ],
  'general.post_aicustomers_senior_search_advanced_filtering_uptodate': [
    'POST',
    '/aicustomers/senior-search/advanced-filtering/uptodate'
  ],
  'general.post_aicustomers_senior_search_template': ['POST', '/aicustomers/senior-search/template'],
  'general.post_aicustomers_senior_search_template': ['POST', '/aicustomers/senior-search/template'],
  'general.post_aicustomers_senior_search_txt_query': ['POST', '/aicustomers/senior-search/txt-query'],
  'general.post_aicustomers_senior_search_txt_query': ['POST', '/aicustomers/senior-search/txt-query'],
  'general.post_aicustomers_senior_search_excel_query': ['POST', '/aicustomers/senior-search/excel-query'],
  'general.post_aicustomers_senior_search_excel_query': ['POST', '/aicustomers/senior-search/excel-query'],
  'general.post_aicustomers_senior_search_prev_history': ['POST', '/aicustomers/senior-search/prev-history'],
  'general.post_aicustomers_senior_search_toker_query': ['POST', '/aicustomers/senior-search/toker-query'],
  'general.post_aicustomers_senior_search_toker_query': ['POST', '/aicustomers/senior-search/toker-query'],
  'general.post_aicustomers_senior_search_toker_query_location': [
    'POST',
    '/aicustomers/senior-search/toker-query/location'
  ],
  'general.post_aicustomers_senior_search_toker_query_location': [
    'POST',
    '/aicustomers/senior-search/toker-query/location'
  ],
  'general.post_aicustomers_senior_search_ent_receive': ['POST', '/aicustomers/senior-search/ent-receive'],
  'general.post_aicustomers_senior_search_intelligent_receive': [
    'POST',
    '/aicustomers/senior-search/intelligent-receive'
  ],
  'general.get_aicustomers_senior_search_last_condition': ['GET', '/aicustomers/senior-search/last-condition'],
  'general.get_aicustomers_senior_search_last_condition_uptodate': [
    'GET',
    '/aicustomers/senior-search/last-condition/uptodate'
  ],
  'general.post_aicustomers_senior_search_recommend': ['POST', '/aicustomers/senior-search/recommend'],
  'general.get_aicustomers_senior_search_recommend_with_keyword': [
    'GET',
    '/aicustomers/senior-search/recommend-with-keyword'
  ],
  'general.get_aicustomers_senior_search_keyword_recommend': ['GET', '/aicustomers/senior-search/keyword/recommend'],
  'general.get_aicustomers_senior_search_keyword_associate': ['GET', '/aicustomers/senior-search/keyword/associate'],
  'general.get_aicustomers_senior_search_refresh_keyword_cache': [
    'GET',
    '/aicustomers/senior-search/refresh-keyword-cache'
  ],
  'general.delete_aicustomers_intelligent_search_words_history_search': [
    'DELETE',
    '/aicustomers/intelligent-search/words/history-search'
  ],
  'general.get_aicustomers_potentials_mining': ['GET', '/aicustomers/potentials/mining'],
  'general.get_aicustomers_potentials_gene': ['GET', '/aicustomers/potentials/gene'],
  'general.get_aicustomers_potentials_gene_v2': ['GET', '/aicustomers/potentials/gene/v2'],
  'general.get_aicustomers_potentials_topic': ['GET', '/aicustomers/potentials/topic'],
  'general.get_aicustomers_potentials_topic': ['GET', '/aicustomers/potentials/topic'],
  'general.get_aicustomers_potentials_dis_gene': ['GET', '/aicustomers/potentials/dis-gene'],
  'general.get_aicustomers_potentials_dis_gene_v2': ['GET', '/aicustomers/potentials/dis-gene/v2'],
  'general.post_aicustomers_potentials_no_favorite': ['POST', '/aicustomers/potentials/no-favorite'],
  'general.post_aicustomers_potentials_batch_collection': ['POST', '/aicustomers/potentials/batch-collection'],
  'general.post_aicustomers_aicall_unlock_item_page': ['POST', '/aicustomers/aicall/unlock-item/page'],
  'general.post_aicustomers_aicall_unlock_item_page_count': ['POST', '/aicustomers/aicall/unlock-item/page/count'],
  'general.get_aicustomers_aicall_unlock_item_condition': ['GET', '/aicustomers/aicall/unlock-item/condition'],
  'general.delete_aicustomers_aicall_unlock_item_list': ['DELETE', '/aicustomers/aicall/unlock-item/list'],
  'general.delete_aicustomers_aicall_unlock_item_by_condition': [
    'DELETE',
    '/aicustomers/aicall/unlock-item/by-condition'
  ],
  'general.get_aicustomers_aicall_work_space_unlock_statistics': [
    'GET',
    '/aicustomers/aicall/work-space/unlock-statistics'
  ],
  'general.get_aicustomers_aicall_work_space_call_statistics': [
    'GET',
    '/aicustomers/aicall/work-space/call-statistics'
  ],
  'general.get_aicustomers_aicall_work_space_templates': ['GET', '/aicustomers/aicall/work-space/templates'],
  'general.get_aicustomers_aicall_market_industry': ['GET', '/aicustomers/aicall/market/industry'],
  'general.post_aicustomers_aicall_market_tempcontacts': ['POST', '/aicustomers/aicall/market/tempcontacts'],
  'general.post_aicustomers_aicall_market_templates': ['POST', '/aicustomers/aicall/market/templates'],
  'general.get_aicustomers_aicall_market_count': ['GET', '/aicustomers/aicall/market/count'],
  'general.post_aicustomers_aicall_resource_pool_enterprises_try_unlock': [
    'POST',
    '/aicustomers/aicall/resource-pool/enterprises/try-unlock'
  ],
  'general.post_aicustomers_aicall_resource_pool_enterprises_unlock': [
    'POST',
    '/aicustomers/aicall/resource-pool/enterprises/unlock'
  ],
  'general.post_aicustomers_aicall_resource_pool_enterprises_unlock_async': [
    'POST',
    '/aicustomers/aicall/resource-pool/enterprises/unlock/async'
  ],
  'general.post_aicustomers_aicall_resource_pool_unlock_ent_query': [
    'POST',
    '/aicustomers/aicall/resource-pool/unlock/ent-query'
  ],
  'general.post_aicustomers_aicall_resource_pool_unlock_intelligent': [
    'POST',
    '/aicustomers/aicall/resource-pool/unlock/intelligent'
  ],
  'general.post_aicustomers_aicall_resource_pool_unlock_senior_search': [
    'POST',
    '/aicustomers/aicall/resource-pool/unlock/senior-search'
  ],
  'general.post_aicustomers_aicall_resource_pool_unlock_batch_txt': [
    'POST',
    '/aicustomers/aicall/resource-pool/unlock/batch-txt'
  ],
  'general.post_aicustomers_aicall_resource_pool_unlock_batch_excel': [
    'POST',
    '/aicustomers/aicall/resource-pool/unlock/batch-excel'
  ],
  'general.post_aicustomers_aicall_resource_pool_unlock_potential_mining': [
    'POST',
    '/aicustomers/aicall/resource-pool/unlock/potential-mining'
  ],
  'general.post_aicustomers_aicall_resource_pool_unlock_potential_subscriber': [
    'POST',
    '/aicustomers/aicall/resource-pool/unlock/potential-subscriber'
  ],
  'general.post_aicustomers_aicall_resource_pool_unlock_map_customer': [
    'POST',
    '/aicustomers/aicall/resource-pool/unlock/map-customer'
  ],
  'general.post_aicustomers_aicall_resource_pool_unlock_relation_customer': [
    'POST',
    '/aicustomers/aicall/resource-pool/unlock/relation-customer'
  ],
  'general.post_aicustomers_aicall_resource_pool_telrobot_launch_all': [
    'POST',
    '/aicustomers/aicall/resource-pool/telrobot/launch/all'
  ],
  'general.post_aicustomers_aicall_resource_pool_telrobot_launch_select': [
    'POST',
    '/aicustomers/aicall/resource-pool/telrobot/launch/select'
  ],
  'general.post_aicustomers_aicall_resource_pool_telrobot_tasks': [
    'POST',
    '/aicustomers/aicall/resource-pool/telrobot/tasks'
  ],
  'general.post_aicustomers_aicall_resource_pool_history': ['POST', '/aicustomers/aicall/resource-pool/history'],
  'general.post_aicustomers_aicall_resource_pool_ent_unlock': ['POST', '/aicustomers/aicall/resource-pool/ent/unlock'],
  'general.post_aicustomers_aicall_resource_pool_unlock_remove': [
    'POST',
    '/aicustomers/aicall/resource-pool/unlock/remove'
  ],
  'general.post_aicustomers_aicall_resource_pool_unlock_remove_batch': [
    'POST',
    '/aicustomers/aicall/resource-pool/unlock/remove-batch'
  ],
  'general.post_aicustomers_aicall_resource_item_page': ['POST', '/aicustomers/aicall/resource-item/page'],
  'general.get_aicustomers_aicall_resource_item_condition': ['GET', '/aicustomers/aicall/resource-item/condition'],
  'general.delete_aicustomers_aicall_resource_item_list': ['DELETE', '/aicustomers/aicall/resource-item/list'],
  'general.delete_aicustomers_aicall_resource_item_by_condition': [
    'DELETE',
    '/aicustomers/aicall/resource-item/by-condition'
  ],
  'general.get_aicustomers_aicall_resource_item_init_sharding': [
    'GET',
    '/aicustomers/aicall/resource-item/init-sharding'
  ],
  'general.get_aicustomers_aicall_resource_group': ['GET', '/aicustomers/aicall/resource-group'],
  'general.post_aicustomers_aicall_resource_group': ['POST', '/aicustomers/aicall/resource-group'],
  'general.put_aicustomers_aicall_resource_group': ['PUT', '/aicustomers/aicall/resource-group'],
  'general.delete_aicustomers_aicall_resource_group': ['DELETE', '/aicustomers/aicall/resource-group'],
  'general.get_aicustomers_aicall_resource_group_statistics': ['GET', '/aicustomers/aicall/resource-group/statistics'],
  'general.get_aicustomers_aicall_resource_group_phone_count': [
    'GET',
    '/aicustomers/aicall/resource-group/phone-count'
  ],
  'general.get_aicustomers_aicall_resource_group_phone_all_count': [
    'GET',
    '/aicustomers/aicall/resource-group/phone-all-count'
  ],
  'general.post_aicustomers_law_resource_pool_unlock_query': ['POST', '/aicustomers/law/resource-pool/unlock/query'],
  'general.post_aicustomers_qualification_resource_pool_unlock_query': [
    'POST',
    '/aicustomers/qualification/resource-pool/unlock/query'
  ],
  'general.get_aicustomers_cule_query_cluepackage': ['GET', '/aicustomers/cule/query-cluepackage'],
  'general.post_aicustomers_cule_balance': ['POST', '/aicustomers/cule/balance'],
  'general.post_aicustomers_cule_aicall_balance': ['POST', '/aicustomers/cule/aicall/balance'],
  'general.get_aicustomers_subscriber': ['GET', '/aicustomers/subscriber'],
  'general.put_aicustomers_subscriber': ['PUT', '/aicustomers/subscriber'],
  'general.post_aicustomers_subscriber_pub': ['POST', '/aicustomers/subscriber/pub'],
  'general.put_aicustomers_subscriber_cancel': ['PUT', '/aicustomers/subscriber/cancel'],
  'general.put_aicustomers_subscriber_currency': ['PUT', '/aicustomers/subscriber/currency'],
  'general.get_aicustomers_subscriber_flag': ['GET', '/aicustomers/subscriber/flag'],
  'general.post_aicustomers_potential_subscriber': ['POST', '/aicustomers/potential/subscriber'],
  'general.get_aicustomers_potential_subscriber': ['GET', '/aicustomers/potential/subscriber'],
  'general.post_aicustomers_potential_subscriber_grab_collection': [
    'POST',
    '/aicustomers/potential/subscriber/grab-collection'
  ],
  'general.get_aicustomers_subscriber_condition': ['GET', '/aicustomers/subscriber/condition'],
  'general.get_aicustomers_subscriber_condition_list': ['GET', '/aicustomers/subscriber/condition/list'],
  'general.get_aicustomers_subscriber_condition_filter': ['GET', '/aicustomers/subscriber/condition/filter'],
  'general.post_aicustomers_relation_customer_batch_grab': ['POST', '/aicustomers/relation-customer/batch-grab'],
  'general.get_aicustomers_relation_customer_edit_tag': ['GET', '/aicustomers/relation-customer/edit-tag'],
  'general.post_aicustomers_relation_customer_list': ['POST', '/aicustomers/relation-customer/list'],
  'general.post_aicustomers_relation_customer_valid': ['POST', '/aicustomers/relation-customer/valid'],
  'general.post_aicustomers_relation_customer': ['POST', '/aicustomers/relation-customer'],
  'general.delete_aicustomers_relation_customer': ['DELETE', '/aicustomers/relation-customer'],
  'general.put_aicustomers_relation_customer': ['PUT', '/aicustomers/relation-customer'],
  'general.post_aicustomers_relation_customer_ent_list': ['POST', '/aicustomers/relation-customer/ent-list'],
  'general.get_aicustomers_relation_customer_status': ['GET', '/aicustomers/relation-customer/status'],
  'general.get_aicustomers_relation_customer_condition': ['GET', '/aicustomers/relation-customer/condition'],
  'general.post_aicustomers_relation_customer_file': ['POST', '/aicustomers/relation-customer/file'],
  'general.get_aicustomers_relation_customer_overview': ['GET', '/aicustomers/relation-customer/overview'],
  'general.get_aicustomers_relation_customer_ent_detail_base_info': [
    'GET',
    '/aicustomers/relation-customer/ent-detail/base-info'
  ],
  'general.get_aicustomers_relation_customer_ent_detail_overview': [
    'GET',
    '/aicustomers/relation-customer/ent-detail/overview'
  ],
  'general.get_aicustomers_relation_customer_ent_detail_intel_prop_agency': [
    'GET',
    '/aicustomers/relation-customer/ent-detail/intel-prop/agency'
  ],
  'general.get_aicustomers_relation_customer_ent_detail_ent_risk_lawsuit': [
    'GET',
    '/aicustomers/relation-customer/ent-detail/ent-risk/lawsuit'
  ],
  'general.get_aicustomers_relation_customer_ent_detail_ent_risk_lawsuit_stats': [
    'GET',
    '/aicustomers/relation-customer/ent-detail/ent-risk/lawsuit/stats'
  ],
  'general.get_aicustomers_relation_customer_ent_detail_bidding_purchase': [
    'GET',
    '/aicustomers/relation-customer/ent-detail/bidding/purchase'
  ],
  'general.get_aicustomers_relation_customer_ent_detail_subsidiary': [
    'GET',
    '/aicustomers/relation-customer/ent-detail/subsidiary'
  ],
  'general.get_aicustomers_relation_customer_ent_detail_investment': [
    'GET',
    '/aicustomers/relation-customer/ent-detail/investment'
  ],
  'general.get_aicustomers_relation_customer_ent_detail_resource': [
    'GET',
    '/aicustomers/relation-customer/ent-detail/resource'
  ],
  'general.get_aicustomers_relation_customer_ent_detail_resource_v2': [
    'GET',
    '/aicustomers/relation-customer/ent-detail/resource/v2'
  ],
  'general.get_aicustomers_relation_customer_ent_detail_icon_intel_prop_agency': [
    'GET',
    '/aicustomers/relation-customer/ent-detail/icon/intel-prop/agency'
  ],
  'general.get_aicustomers_relation_customer_ent_detail_icon_ent_risk_lawsuit': [
    'GET',
    '/aicustomers/relation-customer/ent-detail/icon/ent-risk/lawsuit'
  ],
  'general.get_aicustomers_relation_customer_ent_detail_icon_bidding_purchase': [
    'GET',
    '/aicustomers/relation-customer/ent-detail/icon/bidding/purchase'
  ],
  'general.get_aicustomers_relation_customer_ent_detail_icon_subsidiary': [
    'GET',
    '/aicustomers/relation-customer/ent-detail/icon/subsidiary'
  ],
  'general.get_aicustomers_relation_customer_ent_detail_icon_investment': [
    'GET',
    '/aicustomers/relation-customer/ent-detail/icon/investment'
  ],
  'general.get_aicustomers_relation_customer_ent_detail_icon_resource': [
    'GET',
    '/aicustomers/relation-customer/ent-detail/icon/resource'
  ],
  'general.get_aicustomers_relation_customer_ent_detail_icon_resource_v2': [
    'GET',
    '/aicustomers/relation-customer/ent-detail/icon/resource/v2'
  ],
  'general.get_aicustomers_law_condition': ['GET', '/aicustomers/law/condition'],
  'general.post_aicustomers_law_recent_court': ['POST', '/aicustomers/law/recent_court'],
  'general.post_aicustomers_law_legal_advice': ['POST', '/aicustomers/law/legal-advice'],
  'general.post_aicustomers_law_bankruptcy_reorg': ['POST', '/aicustomers/law/bankruptcy-reorg'],
  'general.post_aicustomers_law_credit_repair': ['POST', '/aicustomers/law/credit-repair'],
  'general.post_aicustomers_law_industry': ['POST', '/aicustomers/law/industry'],
  'general.post_aicustomers_law_recent_court_count': ['POST', '/aicustomers/law/recent_court/count'],
  'general.post_aicustomers_law_legal_advice_count': ['POST', '/aicustomers/law/legal-advice/count'],
  'general.post_aicustomers_law_bankruptcy_reorg_count': ['POST', '/aicustomers/law/bankruptcy-reorg/count'],
  'general.post_aicustomers_law_credit_repair_count': ['POST', '/aicustomers/law/credit-repair/count'],
  'general.post_aicustomers_law_industry_count': ['POST', '/aicustomers/law/industry/count'],
  'general.post_aicustomers_law_bookmark': ['POST', '/aicustomers/law/bookmark'],
  'general.delete_aicustomers_law_bookmark': ['DELETE', '/aicustomers/law/bookmark'],
  'general.put_aicustomers_law_bookmark': ['PUT', '/aicustomers/law/bookmark'],
  'general.get_aicustomers_law_bookmark': ['GET', '/aicustomers/law/bookmark'],
  'general.get_aicustomers_law_bookmark_list': ['GET', '/aicustomers/law/bookmark/list'],
  'general.post_aicustomers_law_history': ['POST', '/aicustomers/law/history'],
  'general.delete_aicustomers_law_history': ['DELETE', '/aicustomers/law/history'],
  'general.post_aicustomers_law_batch_grab': ['POST', '/aicustomers/law/batch-grab'],
  'general.post_aicustomers_law_mail': ['POST', '/aicustomers/law/mail'],
  'general.get_aicustomers_finance_custom_conditions': ['GET', '/aicustomers/finance/custom/conditions'],
  'general.post_aicustomers_finance_advanced_filtering': ['POST', '/aicustomers/finance/advanced-filtering'],
  'general.post_aicustomers_finance_template': ['POST', '/aicustomers/finance/template'],
  'general.get_aicustomers_finance_custom_year': ['GET', '/aicustomers/finance/custom/year'],
  'general.get_aicustomers_finance_custom_list': ['GET', '/aicustomers/finance/custom/list'],
  'general.get_aicustomers_finance_custom_mine_list': ['GET', '/aicustomers/finance/custom/mine/list'],
  'general.get_aicustomers_finance_custom_more': ['GET', '/aicustomers/finance/custom/more'],
  'general.get_aicustomers_finance_custom_made': ['GET', '/aicustomers/finance/custom/made'],
  'general.post_aicustomers_finance_custom_collect': ['POST', '/aicustomers/finance/custom/collect'],
  'general.delete_aicustomers_finance_custom_collect': ['DELETE', '/aicustomers/finance/custom/collect'],
  'general.post_aicustomers_finance_custom_apply': ['POST', '/aicustomers/finance/custom/apply'],
  'general.post_aicustomers_finance_batch_grab': ['POST', '/aicustomers/finance/batch-grab'],
  'general.post_aicustomers_finance_custom_contact_statistics': [
    'POST',
    '/aicustomers/finance/custom/contact-statistics'
  ],
  'general.post_aicustomers_finance_scale_statistics': ['POST', '/aicustomers/finance/scale-statistics'],
  'general.post_aicustomers_finance_duration_statistics': ['POST', '/aicustomers/finance/duration-statistics'],
  'general.post_aicustomers_finance_capital_statistics': ['POST', '/aicustomers/finance/capital-statistics'],
  'general.post_aicustomers_finance_pay_statistics': ['POST', '/aicustomers/finance/pay-statistics'],
  'general.post_aicustomers_finance_revenue_statistics': ['POST', '/aicustomers/finance/revenue-statistics'],
  'general.post_aicustomers_finance_location_statistics': ['POST', '/aicustomers/finance/location-statistics'],
  'general.post_aicustomers_finance_completion_file': ['POST', '/aicustomers/finance/completion-file'],
  'general.post_aicustomers_finance_completion_txt': ['POST', '/aicustomers/finance/completion-txt'],
  'general.get_aicustomers_finance_completion_list': ['GET', '/aicustomers/finance/completion/list'],
  'general.get_aicustomers_finance_completion_last': ['GET', '/aicustomers/finance/completion/last'],
  'general.get_aicustomers_finance_completion_surplus': ['GET', '/aicustomers/finance/completion/surplus'],
  'general.post_aicustomers_finance_custom_book_mark': ['POST', '/aicustomers/finance/custom/book-mark'],
  'general.delete_aicustomers_finance_custom_book_mark': ['DELETE', '/aicustomers/finance/custom/book-mark'],
  'general.get_aicustomers_finance_custom_book_mark': ['GET', '/aicustomers/finance/custom/book-mark'],
  'general.put_aicustomers_finance_custom_book_mark': ['PUT', '/aicustomers/finance/custom/book-mark'],
  'general.get_aicustomers_hx_quota': ['GET', '/aicustomers/hx/quota'],
  'general.get_aicustomers_hx_preferred_ent': ['GET', '/aicustomers/hx/preferred/ent'],
  'general.post_aicustomers_hx_ent_auth': ['POST', '/aicustomers/hx/ent-auth'],
  'general.post_aicustomers_hx_creditcompany': ['POST', '/aicustomers/hx/creditCompany'],
  'general.get_aicustomers_hx_better_ent_list': ['GET', '/aicustomers/hx/better-ent/list'],
  'general.get_aicustomers_finance_main_quota': ['GET', '/aicustomers/finance/main/quota'],
  'general.get_aicustomers_finance_main_advanced_filtering': ['GET', '/aicustomers/finance/main/advanced-filtering'],
  'general.get_aicustomers_finance_main_ent_recommend': ['GET', '/aicustomers/finance/main/ent-recommend'],
  'general.get_aicustomers_finance_main_new': ['GET', '/aicustomers/finance/main/new'],
  'general.get_aicustomers_finance_main_advanced_filtering_use': [
    'GET',
    '/aicustomers/finance/main/advanced-filtering/use'
  ],
  'general.get_aicustomers_qualification_eligibility_condition': [
    'GET',
    '/aicustomers/qualification/eligibility/condition'
  ],
  'general.post_aicustomers_qualification_eligibility_establish': [
    'POST',
    '/aicustomers/qualification/eligibility/establish'
  ],
  'general.post_aicustomers_qualification_eligibility_add': ['POST', '/aicustomers/qualification/eligibility/add'],
  'general.post_aicustomers_qualification_eligibility_expand': [
    'POST',
    '/aicustomers/qualification/eligibility/expand'
  ],
  'general.post_aicustomers_qualification_eligibility_upgrade': [
    'POST',
    '/aicustomers/qualification/eligibility/upgrade'
  ],
  'general.post_aicustomers_qualification_eligibility_personnel': [
    'POST',
    '/aicustomers/qualification/eligibility/personnel'
  ],
  'general.post_aicustomers_qualification_eligibility_receipt': [
    'POST',
    '/aicustomers/qualification/eligibility/receipt'
  ],
  'general.post_aicustomers_qualification_bookmark': ['POST', '/aicustomers/qualification/bookmark'],
  'general.delete_aicustomers_qualification_bookmark': ['DELETE', '/aicustomers/qualification/bookmark'],
  'general.put_aicustomers_qualification_bookmark': ['PUT', '/aicustomers/qualification/bookmark'],
  'general.get_aicustomers_qualification_bookmark': ['GET', '/aicustomers/qualification/bookmark'],
  'general.get_aicustomers_qualification_bookmark_list': ['GET', '/aicustomers/qualification/bookmark/list'],
  'general.post_aicustomers_qualification_history': ['POST', '/aicustomers/qualification/history'],
  'general.delete_aicustomers_qualification_history': ['DELETE', '/aicustomers/qualification/history'],
  'general.post_aicustomers_qualification_batch_grab': ['POST', '/aicustomers/qualification/batch-grab'],
  'general.get_aicustomers_qualification_eligibility_type': ['GET', '/aicustomers/qualification/eligibility/type'],
  'general.post_aicustomers_tiandao_asset_profit': ['POST', '/aicustomers/tiandao/asset-profit'],
  'general.post_aicustomers_tiandao_asset_tax': ['POST', '/aicustomers/tiandao/asset-tax'],
  'general.get_aicustomers_tiandao_asset_profit_mid': ['GET', '/aicustomers/tiandao/asset-profit-mid'],
  'general.get_aicustomers_tiandao_asset_tax_mid': ['GET', '/aicustomers/tiandao/asset-tax-mid'],
  'general.get_aicustomers_tiandao_conditions': ['GET', '/aicustomers/tiandao/conditions'],
  'general.get_aicustomers_tiandao_code_profit': ['GET', '/aicustomers/tiandao/code/profit'],
  'general.get_aicustomers_tiandao_code_tax': ['GET', '/aicustomers/tiandao/code/tax'],
  'general.post_aicustomers_tiandao_page': ['POST', '/aicustomers/tiandao/page'],
  'general.post_aicustomers_tiandao_batch_grab': ['POST', '/aicustomers/tiandao/batch-grab'],
  'general.post_aicustomers_tiandao_asset_profit_tax_batch_selected': [
    'POST',
    '/aicustomers/tiandao/asset-profit-tax/batch-selected'
  ],
  'general.post_aicustomers_tiandao_asset_profit_tax_senior_search': [
    'POST',
    '/aicustomers/tiandao/asset-profit-tax/senior-search'
  ],
  'general.post_aicustomers_tiandao_asset_profit_tax_ent_query': [
    'POST',
    '/aicustomers/tiandao/asset-profit-tax/ent-query'
  ],
  'general.post_aicustomers_tiandao_asset_profit_tax_intelligent': [
    'POST',
    '/aicustomers/tiandao/asset-profit-tax/intelligent'
  ],
  'general.post_aicustomers_tiandao_asset_profit_tax_batch_txt': [
    'POST',
    '/aicustomers/tiandao/asset-profit-tax/batch-txt'
  ],
  'general.post_aicustomers_tiandao_asset_profit_tax_batch_excel': [
    'POST',
    '/aicustomers/tiandao/asset-profit-tax/batch-excel'
  ],
  'general.get_aicustomers_wechat_mp_access': ['GET', '/aicustomers/wechat/mp/access'],
  'general.post_aicustomers_wechat_mp_access': ['POST', '/aicustomers/wechat/mp/access'],
  'general.post_aicustomers_wechat_mp_qr_code_url': ['POST', '/aicustomers/wechat/mp/qr-code-url'],
  'general.post_aicustomers_wechat_mp_unbind_wx': ['POST', '/aicustomers/wechat/mp/unbind-wx'],
  'general.get_aicustomers_wechat_mp_snsapi_base_code': ['GET', '/aicustomers/wechat/mp/snsapi-base-code'],
  'general.post_aicustomers_account_center_qr_code_url': ['POST', '/aicustomers/account/center/qr-code-url'],
  'general.get_aicustomers_account_center_bind_wx': ['GET', '/aicustomers/account/center/bind-wx'],
  'general.post_aicustomers_resource_pool_import': ['POST', '/aicustomers/resource-pool/import'],
  'general.post_aicustomers_enterprises_grab_export': ['POST', '/aicustomers/enterprises/grab/export'],
  'general.post_aicustomers_enterprises_grab_export_quota': ['POST', '/aicustomers/enterprises/grab/export/quota'],
  'general.get_aicustomers_enterprises_grab_export_setting': ['GET', '/aicustomers/enterprises/grab/export/setting'],
  'general.put_aicustomers_enterprises_grab_export_setting': ['PUT', '/aicustomers/enterprises/grab/export/setting'],
  'general.get_aicustomers_data_base_grab_export_setting': ['GET', '/aicustomers/data/base-grab/export/setting'],
  'general.put_aicustomers_data_base_grab_export_setting': ['PUT', '/aicustomers/data/base-grab/export/setting'],
  'general.post_aicustomers_data_base_grab_export': ['POST', '/aicustomers/data/base-grab/export'],
  'general.get_aicustomers_resource_pool_export_settings': ['GET', '/aicustomers/resource-pool/export/settings'],
  'general.put_aicustomers_resource_pool_export_settings': ['PUT', '/aicustomers/resource-pool/export/settings'],
  'general.post_aicustomers_resource_pool_export': ['POST', '/aicustomers/resource-pool/export'],
  'general.post_aicustomers_resource_pool_export_log': ['POST', '/aicustomers/resource-pool/export/log'],
  'general.get_aicustomers_third_telrobot_export_button_auth': [
    'GET',
    '/aicustomers/third-telrobot/export/button/auth'
  ],
  'general.get_aicustomers_third_telrobot_export_field': ['GET', '/aicustomers/third-telrobot/export/field'],
  'general.post_aicustomers_third_telrobot_export': ['POST', '/aicustomers/third-telrobot/export'],
  'general.post_aicustomers_third_telrobot_export_log': ['POST', '/aicustomers/third-telrobot/export/log'],
  'general.post_aicustomers_data_completion_export_quota': ['POST', '/aicustomers/data/completion/export/quota'],
  'general.post_aicustomers_data_completion_export': ['POST', '/aicustomers/data/completion/export'],
  'general.post_aicustomers_data_completion_export_setting': ['POST', '/aicustomers/data/completion/export/setting'],
  'general.put_aicustomers_data_completion_export_setting': ['PUT', '/aicustomers/data/completion/export/setting'],
  'general.post_aicustomers_aicall_records': ['POST', '/aicustomers/aicall/records'],
  'general.get_aicustomers_aicall_clue_package_consume': ['GET', '/aicustomers/aicall/clue-package/consume'],
  'general.get_aicustomers_aicall_radio': ['GET', '/aicustomers/aicall/radio'],
  'general.get_aicustomers_aicall_querycustomerrobots': ['GET', '/aicustomers/aicall/queryCustomerRobots'],
  'general.get_aicustomers_altas_company_map': ['GET', '/aicustomers/altas/company-map'],
  'general.get_aicustomers_altas_equity_penetration': ['GET', '/aicustomers/altas/equity-penetration'],
  'general.get_aicustomers_altas_equity_penetration_person': ['GET', '/aicustomers/altas/equity-penetration-person'],
  'general.get_aicustomers_altas_relationship_graph': ['GET', '/aicustomers/altas/relationship-graph'],
  'general.get_aicustomers_altas_business_relationship': ['GET', '/aicustomers/altas/business-relationship'],
  'general.get_aicustomers_altas_human_info': ['GET', '/aicustomers/altas/human-info'],
  'general.get_aicustomers_altas_graph_able': ['GET', '/aicustomers/altas/graph-able'],
  'general.get_aicustomers_risk_details_creditors': ['GET', '/aicustomers/risk/details/creditors'],
  'general.get_aicustomers_risk_details_complaint': ['GET', '/aicustomers/risk/details/complaint'],
  'general.get_aicustomers_risk_details_stock_frost': ['GET', '/aicustomers/risk/details/stock-frost'],
  'general.get_aicustomers_risk_details_stock_frost_total': ['GET', '/aicustomers/risk/details/stock-frost/total'],
  'general.get_aicustomers_risk_details_pledge': ['GET', '/aicustomers/risk/details/pledge'],
  'general.get_aicustomers_risk_details_pledge_change': ['GET', '/aicustomers/risk/details/pledge-change'],
  'general.get_aicustomers_risk_details_stock_count': ['GET', '/aicustomers/risk/details/stock-count'],
  'general.get_aicustomers_risk_details_unfreeze': ['GET', '/aicustomers/risk/details/unfreeze'],
  'general.get_aicustomers_risk_details_land_mortgage': ['GET', '/aicustomers/risk/details/land-mortgage'],
  'general.get_aicustomers_risk_details_land_transfer': ['GET', '/aicustomers/risk/details/land-transfer'],
  'general.get_aicustomers_config_company_info': ['GET', '/aicustomers/config/company/info'],
  'general.get_aicustomers_config_user_list': ['GET', '/aicustomers/config/user/list'],
  'general.post_aicustomers_config_user_authority': ['POST', '/aicustomers/config/user/authority'],
  'general.post_aicustomers_config_user_export': ['POST', '/aicustomers/config/user/export'],
  'general.post_aicustomers_config_user_profit_tax': ['POST', '/aicustomers/config/user/profit-tax'],
  'general.post_aicustomers_config_third_user_authority': ['POST', '/aicustomers/config/third/user/authority'],
  'general.post_aicustomers_config_third_unlock_remove': ['POST', '/aicustomers/config/third/unlock/remove'],
  'general.post_aicustomers_config_user_limit': ['POST', '/aicustomers/config/user/limit'],
  'general.post_aicustomers_config_release_resource': ['POST', '/aicustomers/config/release/resource'],
  'general.post_aicustomers_config_protect_resource': ['POST', '/aicustomers/config/protect/resource'],
  'general.get_aicustomers_config_user_config': ['GET', '/aicustomers/config/user/config'],
  'general.get_aicustomers_config_grab_config': ['GET', '/aicustomers/config/grab/config'],
  'general.post_aicustomers_config_grab_config': ['POST', '/aicustomers/config/grab/config'],
  'general.get_aicustomers_config_aicall_unlock': ['GET', '/aicustomers/config/aicall/unlock'],
  'general.post_aicustomers_config_unlock_config': ['POST', '/aicustomers/config/unlock/config'],
  'general.get_aicustomers_config_asset_profit_tax_config': ['GET', '/aicustomers/config/asset-profit-tax/config'],
  'general.post_aicustomers_config_third_tenant_id_user_authority': [
    'POST',
    '/aicustomers/config/third/{tenant-id}/user/authority'
  ],
  'general.post_aicustomers_config_third_tenant_id_user_authority': [
    'POST',
    '/aicustomers/config/third/{tenant-id}/user/authority'
  ],
  'general.get_aicustomers_config_third_tenant_id_user_unlockinfo': [
    'GET',
    '/aicustomers/config/third/{tenant-id}/user/unlockinfo'
  ],
  'general.get_aicustomers_config_third_tenant_id_user_unlockinfo': [
    'GET',
    '/aicustomers/config/third/{tenant-id}/user/unlockinfo'
  ],
  'general.post_aicustomers_config_third_tenant_id_user_limit': [
    'POST',
    '/aicustomers/config/third/{tenant-id}/user/limit'
  ],
  'general.post_aicustomers_config_third_tenant_id_user_limit': [
    'POST',
    '/aicustomers/config/third/{tenant-id}/user/limit'
  ],
  'general.post_aicustomers_config_third_tenant_id_user_view_status': [
    'POST',
    '/aicustomers/config/third/{tenant-id}/user/view-status'
  ],
  'general.post_aicustomers_config_third_tenant_id_user_view_status': [
    'POST',
    '/aicustomers/config/third/{tenant-id}/user/view-status'
  ],
  'general.post_aicustomers_config_third_tenant_id_protectresource': [
    'POST',
    '/aicustomers/config/third/{tenant-id}/protectResource'
  ],
  'general.post_aicustomers_config_third_tenant_id_protectresource': [
    'POST',
    '/aicustomers/config/third/{tenant-id}/protectResource'
  ],
  'general.post_aicustomers_config_ops_corp_id_user_limit': ['POST', '/aicustomers/config/ops/{corp-id}/user/limit'],
  'general.post_aicustomers_config_third_tenant_id_user_enterprises': [
    'POST',
    '/aicustomers/config/third/{tenant-id}/user/enterprises'
  ],
  'general.post_aicustomers_config_third_tenant_id_user_contacts': [
    'POST',
    '/aicustomers/config/third/{tenant-id}/user/contacts'
  ],
  'general.get_aicustomers_suiteauths': ['GET', '/aicustomers/suiteauths'],
  'general.post_aicustomers_suiteauths': ['POST', '/aicustomers/suiteauths'],
  'general.post_aicustomers_suiteauths_config': ['POST', '/aicustomers/suiteauths/config'],
  'general.get_aicustomers_suiteauths_export_grab': ['GET', '/aicustomers/suiteauths/export/grab'],
  'general.post_aicustomers_suiteauths_buy_suite_v2': ['POST', '/aicustomers/suiteauths/buy/suite/v2'],
  'general.post_aicustomers_suiteauths_buy': ['POST', '/aicustomers/suiteauths/buy'],
  'general.post_aicustomers_suiteauths_buy_hangxin_preferred': [
    'POST',
    '/aicustomers/suiteauths/buy/hangxin-preferred'
  ],
  'general.post_aicustomers_suiteauths_buy_quota': ['POST', '/aicustomers/suiteauths/buy/quota'],
  'general.post_aicustomers_suiteauths_third_telrobot_export_quota': [
    'POST',
    '/aicustomers/suiteauths/third-telrobot/export/quota'
  ],
  'general.post_aicustomers_suiteauths_open_customer_package': [
    'POST',
    '/aicustomers/suiteauths/open/customer-package'
  ],
  'general.post_aicustomers_suiteauths_update_suite': ['POST', '/aicustomers/suiteauths/update-suite'],
  'general.post_aicustomers_suiteauths_market_app': ['POST', '/aicustomers/suiteauths/market/app'],
  'general.post_aicustomers_pay_create_order_h5': ['POST', '/aicustomers/pay/create-order/h5'],
  'general.post_aicustomers_pay_create_order_app': ['POST', '/aicustomers/pay/create-order/app'],
  'general.post_aicustomers_pay_pay_order': ['POST', '/aicustomers/pay/pay-order'],
  'general.get_aicustomers_pay_query_order': ['GET', '/aicustomers/pay/query-order'],
  'general.delete_aicustomers_pay_close_order': ['DELETE', '/aicustomers/pay/close-order'],
  'general.get_aicustomers_logs_pv': ['GET', '/aicustomers/logs/pv'],
  'general.get_aicustomers_logs_uv': ['GET', '/aicustomers/logs/uv'],
  'general.get_aicustomers_logs_currentmonthdaily': ['GET', '/aicustomers/logs/currentmonthdaily'],
  'general.get_aicustomers_logs_email': ['GET', '/aicustomers/logs/email'],
  'general.get_aicustomers_bid_public_info_types': ['GET', '/aicustomers/bid/public/info-types'],
  'general.get_aicustomers_bid_public_conditions': ['GET', '/aicustomers/bid/public/conditions'],
  'general.get_aicustomers_bid_public_code_nic': ['GET', '/aicustomers/bid/public/code/nic'],
  'general.get_aicustomers_bid_public_code_nic_with_parent': ['GET', '/aicustomers/bid/public/code/nic-with-parent'],
  'general.post_aicustomers_bid_mapboard_region': ['POST', '/aicustomers/bid/mapboard/region'],
  'general.post_aicustomers_bid_mapboard_enterprise': ['POST', '/aicustomers/bid/mapboard/enterprise'],
  'general.post_aicustomers_bid_details_charts': ['POST', '/aicustomers/bid/details/charts'],
  'general.post_aicustomers_bid_details_project_list': ['POST', '/aicustomers/bid/details/project/list'],
  'general.post_aicustomers_bid_details_condition': ['POST', '/aicustomers/bid/details/condition'],
  'general.get_aicustomers_bid_details_project_details': ['GET', '/aicustomers/bid/details/project/details'],
  'general.get_aicustomers_bid_details_propose_details': ['GET', '/aicustomers/bid/details/propose/details'],
  'general.get_aicustomers_bid_details_project_total': ['GET', '/aicustomers/bid/details/project/total'],
  'general.get_aicustomers_bid_details_role': ['GET', '/aicustomers/bid/details/role'],
  'general.get_aicustomers_bid_subscribes_show_qr': ['GET', '/aicustomers/bid/subscribes/show-qr'],
  'general.get_aicustomers_bid_subscribes_templates': ['GET', '/aicustomers/bid/subscribes/templates'],
  'general.post_aicustomers_bid_subscribes_search': ['POST', '/aicustomers/bid/subscribes/search'],
  'general.get_aicustomers_bid_subscribes_list': ['GET', '/aicustomers/bid/subscribes/list'],
  'general.get_aicustomers_bid_subscribes': ['GET', '/aicustomers/bid/subscribes'],
  'general.delete_aicustomers_bid_subscribes': ['DELETE', '/aicustomers/bid/subscribes'],
  'general.post_aicustomers_bid_subscribes': ['POST', '/aicustomers/bid/subscribes'],
  'general.put_aicustomers_bid_subscribes': ['PUT', '/aicustomers/bid/subscribes'],
  'general.get_aicustomers_bid_subscribes_avalible_num': ['GET', '/aicustomers/bid/subscribes/avalible-num'],
  'general.get_aicustomers_bid_subscribes_log_today': ['GET', '/aicustomers/bid/subscribes/log/today'],
  'general.get_aicustomers_bid_search_recommend_config': ['GET', '/aicustomers/bid/search/recommend/config'],
  'general.post_aicustomers_bid_search_recommend_config': ['POST', '/aicustomers/bid/search/recommend/config'],
  'general.get_aicustomers_bid_search_increment': ['GET', '/aicustomers/bid/search/increment'],
  'general.post_aicustomers_bid_search_projects': ['POST', '/aicustomers/bid/search/projects'],
  'general.get_aicustomers_bid_search_contacts': ['GET', '/aicustomers/bid/search/contacts'],
  'general.get_aicustomers_bid_search_bid_detail': ['GET', '/aicustomers/bid/search/bid/detail'],
  'general.get_aicustomers_bid_search_propose_detail': ['GET', '/aicustomers/bid/search/propose/detail'],
  'general.get_aicustomers_bid_search_names': ['GET', '/aicustomers/bid/search/names'],
  'general.get_aicustomers_bid_search_project_map': ['GET', '/aicustomers/bid/search/project/map'],
  'general.get_aicustomers_bid_bookmarks': ['GET', '/aicustomers/bid/bookmarks'],
  'general.post_aicustomers_bid_bookmarks': ['POST', '/aicustomers/bid/bookmarks'],
  'general.delete_aicustomers_bid_bookmarks': ['DELETE', '/aicustomers/bid/bookmarks'],
  'general.post_api_uac_third_personal_register_hasreg': ['POST', '/api/uac/third/personal/register/hasreg'],
  'general.post_api_sms_indetifycodebid': ['POST', '/api/sms/indetifycodebid'],
  'general.post_api_uac_third_personal_register': ['POST', '/api/uac/third/personal/register'],
  'general.post_api_uac_send_sms_bid_code': ['POST', '/api/uac/send/sms-bid-code'],
  'general.get_api_uac_oauth_token': ['GET', '/api/uac/oauth/token'],
  'general.get_api_uac_tenants_access_token': ['GET', '/api/uac/tenants/access-token'],
  'general.post_api_uac_sms_valid_code_bid': ['POST', '/api/uac/sms/valid-code-bid'],
  'general.put_api_uac_tenants_uac_users_pwd_mobile_code': ['PUT', '/api/uac/tenants/uac-users/pwd/mobile/code'],
  'general.get_api_scrm_users_current': ['GET', '/api/scrm/users/current'],
  'general.get_api_scrm_users_currentrole': ['GET', '/api/scrm/users/currentRole'],
  'general.put_api_uac_tenants_uac_users_union_id_pwd': ['PUT', '/api/uac/tenants/uac-users/union_id/pwd'],
  'general.post_api_scrm_users_verify': ['POST', '/api/scrm/users/verify'],
  'general.get_api_company_order_info': ['GET', '/api/company/order/info'],
  'general.post_api_aicustomers_intelligent_search': ['POST', '/api/aicustomers/intelligent-search'],
  'general.post_api_aicustomers_intelligent_search_associations_history': [
    'POST',
    '/api/aicustomers/intelligent-search/associations/history'
  ],
  'general.post_aicustomers_intelligent_search_count': ['POST', '/aicustomers/intelligent-search/count'],
  'general.get_aicustomers_intelligent_search_conditions': ['GET', '/aicustomers/intelligent-search/conditions'],
  'general.post_aicustomers_intelligent_search_match_report': ['POST', '/aicustomers/intelligent-search/match-report'],
  'general.post_api_aicustomers_intelligent_search_words_hot_words': [
    'POST',
    '/api/aicustomers/intelligent-search/words/hot-words'
  ],
  'general.post_api_aicustomers_intelligent_search_recommendation': [
    'POST',
    '/api/aicustomers/intelligent-search/recommendation'
  ],
  'general.get_api_aicustomers_enterprises_names': ['GET', '/api/aicustomers/enterprises/names'],
  'general.post_api_aicustomers_probe_ent_query': ['POST', '/api/aicustomers/probe/ent/query'],
  'general.post_api_aicustomers_probe_ent_query': ['POST', '/api/aicustomers/probe/ent/query'],
  'general.post_aicustomers_probe_ent_query_count': ['POST', '/aicustomers/probe/ent/query/count'],
  'general.post_api_aicustomers_probe_hints': ['POST', '/api/aicustomers/probe/hints'],
  'general.post_api_aicustomers_probe_hints': ['POST', '/api/aicustomers/probe/hints'],
  'general.post_aicustomers_intelligent_search_product_query': [
    'POST',
    '/aicustomers/intelligent-search/product/query'
  ],
  'general.post_aicustomers_intelligent_search_product_query': [
    'POST',
    '/aicustomers/intelligent-search/product/query'
  ],
  'general.get_aicustomers_intelligent_search_product_conditions': [
    'GET',
    '/aicustomers/intelligent-search/product/conditions'
  ],
  'general.post_aicustomers_senior_search_product_receive': ['POST', '/aicustomers/senior-search/product-receive'],
  'general.post_api_aicustomers_achievement_query': ['POST', '/api/aicustomers/achievement/query'],
  'general.get_aicustomers_achievement_info': ['GET', '/aicustomers/achievement/info'],
  'general.get_aicustomers_achievement_history_search': ['GET', '/aicustomers/achievement/history/search'],
  'general.delete_aicustomers_achievement_history_search': ['DELETE', '/aicustomers/achievement/history/search'],
  'general.get_aicustomers_achievement_condition': ['GET', '/aicustomers/achievement/condition'],
  'general.post_aicustomers_registrar_query': ['POST', '/aicustomers/registrar/query'],
  'general.get_aicustomers_registrar_info': ['GET', '/aicustomers/registrar/info'],
  'general.get_aicustomers_registrar_certificate': ['GET', '/aicustomers/registrar/certificate'],
  'general.get_aicustomers_registrar_achievement': ['GET', '/aicustomers/registrar/achievement'],
  'general.get_aicustomers_registrar_bad_behavior': ['GET', '/aicustomers/registrar/bad/behavior'],
  'general.get_aicustomers_registrar_project_type': ['GET', '/aicustomers/registrar/project/type'],
  'general.get_aicustomers_registrar_history_search': ['GET', '/aicustomers/registrar/history/search'],
  'general.delete_aicustomers_registrar_history_search': ['DELETE', '/aicustomers/registrar/history/search'],
  'general.get_aicustomers_registrar_condition': ['GET', '/aicustomers/registrar/condition'],
  'general.get_aicustomers_credential_history_search': ['GET', '/aicustomers/credential/history/search'],
  'general.delete_aicustomers_credential_history_search': ['DELETE', '/aicustomers/credential/history/search'],
  'general.post_aicustomers_credential_query': ['POST', '/aicustomers/credential/query'],
  'general.get_aicustomers_credential_detail_first': ['GET', '/aicustomers/credential/detail/first'],
  'general.get_aicustomers_credential_info': ['GET', '/aicustomers/credential/info'],
  'general.get_aicustomers_credential_condition': ['GET', '/aicustomers/credential/condition'],
  'general.get_aicustomers_patent_history_search': ['GET', '/aicustomers/patent/history/search'],
  'general.delete_aicustomers_patent_history_search': ['DELETE', '/aicustomers/patent/history/search'],
  'general.post_aicustomers_patent_query': ['POST', '/aicustomers/patent/query'],
  'general.get_aicustomers_patent_detail_head': ['GET', '/aicustomers/patent/detail/head'],
  'general.get_aicustomers_patent_info': ['GET', '/aicustomers/patent/info'],
  'general.get_aicustomers_patent_condition': ['GET', '/aicustomers/patent/condition'],
  'general.get_api_aicustomers_potentials_topic_v2': ['GET', '/api/aicustomers/potentials/topic/v2'],
  'general.get_api_aicustomers_potentials_topic_v2': ['GET', '/api/aicustomers/potentials/topic/v2'],
  'general.get_aicustomers_probe_ent_basic_score': ['GET', '/aicustomers/probe/ent/basic/score'],
  'general.get_aicustomers_probe_ent_status': ['GET', '/aicustomers/probe/ent/status'],
  'general.get_aicustomers_probe_ent_basic': ['GET', '/aicustomers/probe/ent/basic'],
  'general.post_api_aicustomers_senior_search_advanced_filtering_count': [
    'POST',
    '/api/aicustomers/senior-search/advanced-filtering/count'
  ],
  'general.post_api_aicustomers_senior_search_advanced_filtering': [
    'POST',
    '/api/aicustomers/senior-search/advanced-filtering'
  ],
  'general.post_api_aicustomers_senior_search_advanced_filtering': [
    'POST',
    '/api/aicustomers/senior-search/advanced-filtering'
  ],
  'general.post_api_aicustomers_senior_search_advanced_filtering': [
    'POST',
    '/api/aicustomers/senior-search/advanced-filtering'
  ],
  'general.post_api_aicustomers_senior_search_advanced_filtering': [
    'POST',
    '/api/aicustomers/senior-search/advanced-filtering'
  ],
  'general.post_api_bid_enterprises_list': ['POST', '/api/bid/enterprises/list'],
  'general.post_api_bid_account_third_oemlogin': ['POST', '/api/bid/account/third/oemlogin'],
  'general.post_mp_api_coupon_chinasme_claim': ['POST', '/mp/api/coupon/chinasme/claim'],
  'general.post_mp_api_coupon_user_claim': ['POST', '/mp/api/coupon/user/claim'],
  'general.get_mp_api_coupon_user_coupon_list': ['GET', '/mp/api/coupon/user/coupon-list'],
  'general.get_mp_api_coupon_user_coupon_detail': ['GET', '/mp/api/coupon/user/coupon-detail'],
  'general.post_mp_api_coupon_phone_claim': ['POST', '/mp/api/coupon/phone/claim'],
  'general.get_mp_api_goods_list': ['GET', '/mp/api/goods/list'],
  'general.post_mp_api_order_ordersubmit': ['POST', '/mp/api/order/orderSubmit'],
  'general.get_mp_api_order_orderpay_wechat': ['GET', '/mp/api/order/orderPay/wechat'],
  'general.post_mp_api_order_redemptionordersubmit': ['POST', '/mp/api/order/redemptionOrderSubmit'],
  'general.get_mp_api_order_orderpaystatus': ['GET', '/mp/api/order/orderPayStatus'],
  'general.post_mp_api_order_ordercancel': ['POST', '/mp/api/order/orderCancel'],
  'general.get_mp_api_order_orderlist': ['GET', '/mp/api/order/orderList']
};
