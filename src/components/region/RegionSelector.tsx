import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Modal, StyleSheet } from 'react-native';
import { RegionItem } from './index';
import RegionPicker from './RegionPicker';

interface RegionSelectorProps {
  visible: boolean;
  regions: RegionItem[];
  selectedRegions?: string[];
  onClose: () => void;
  onConfirm: (selected: string[]) => void;
}

const RegionSelector: React.FC<RegionSelectorProps> = ({
  visible,
  regions,
  selectedRegions = [],
  onClose,
  onConfirm,
}) => {
  const [selected, setSelected] = useState<string[]>(selectedRegions);

  // 当外部selectedRegions变化时更新内部状态
  useEffect(() => {
    setSelected(selectedRegions);
  }, [selectedRegions]);

  // 处理选择切换
  const handleToggleSelect = (regionId: string, isSelected: boolean) => {
    if (isSelected) {
      setSelected(prev => [...prev, regionId]);
    } else {
      setSelected(prev => prev.filter(id => id !== regionId));
    }
  };

  // 重置选择
  const handleReset = () => {
    setSelected([]);
  };

  // 确认选择
  const handleConfirm = () => {
    onConfirm(selected);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <RegionPicker
            regions={regions}
            showCheckbox={true}
            selectedRegions={selected}
            onToggleSelect={handleToggleSelect}
          />

          {/* 底部按钮区域 */}
          <View style={styles.footer}>
            <TouchableOpacity style={styles.resetButton} onPress={handleReset}>
              <Text style={styles.resetButtonText}>重置</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.confirmButton}
              onPress={handleConfirm}
            >
              <Text style={styles.confirmButtonText}>
                确定（已选{selected.length}个）
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    overflow: 'hidden',
  },
  footer: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  resetButton: {
    flex: 1,
    paddingVertical: 14,
    alignItems: 'center',
    justifyContent: 'center',
    borderRightWidth: 1,
    borderRightColor: '#EEEEEE',
  },
  resetButtonText: {
    fontSize: 16,
    color: '#666666',
  },
  confirmButton: {
    flex: 2,
    paddingVertical: 14,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4B74FF',
  },
  confirmButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '500',
  },
});

export default RegionSelector;
