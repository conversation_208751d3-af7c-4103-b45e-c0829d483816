import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Header from '../../components/common/Header';
import { RegionSelector, Region } from '../../components/region';

type Props = {};

// 模拟地区数据
const mockRegions: Region[] = [
  {
    id: 'henan',
    name: '河南省',
    children: [{ id: 'henan_all', name: '全部' }],
  },
  {
    id: 'beijing',
    name: '北京市',
    count: 3,
    children: [
      { id: 'beijing_xicheng', name: '西城区' },
      { id: 'beijing_changping', name: '昌平区' },
      { id: 'beijing_huairou', name: '怀柔区' },
    ],
  },
  {
    id: 'tianjin',
    name: '天津市',
    children: [
      { id: 'tianjin_heping', name: '和平区' },
      { id: 'tianjin_xiqing', name: '西青区' },
    ],
  },
  {
    id: 'hebei',
    name: '河北省',
    children: [
      { id: 'hebei_shijiazhuang', name: '石家庄市' },
      { id: 'hebei_tangshan', name: '唐山市' },
      { id: 'hebei_fangshan', name: '房山区' },
    ],
  },
  {
    id: 'shanxi',
    name: '山西省',
    children: [
      { id: 'shanxi_taiyuan', name: '太原市' },
      { id: 'shanxi_datong', name: '大同市' },
    ],
  },
  {
    id: 'shaanxi',
    name: '陕西省',
    children: [
      { id: 'shaanxi_xian', name: '西安市' },
      { id: 'shaanxi_haizhou', name: '海州区' },
    ],
  },
  {
    id: 'hunan',
    name: '湖南省',
    children: [
      { id: 'hunan_changsha', name: '长沙市' },
      { id: 'hunan_fenghuang', name: '凤凰县' },
    ],
  },
];

const RegionSelectorDemoScreen: React.FC<Props> = () => {
  const navigation = useNavigation();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);
  const [selectedRegionNames, setSelectedRegionNames] = useState<string[]>([]);

  // 打开地区选择器
  const openRegionSelector = () => {
    setModalVisible(true);
  };

  // 关闭地区选择器
  const closeRegionSelector = () => {
    setModalVisible(false);
  };

  // 确认选择
  const handleConfirm = (selected: string[]) => {
    setSelectedRegions(selected);

    // 获取选中地区的名称
    const names: string[] = [];
    selected.forEach(id => {
      // 查找省份
      for (const province of mockRegions) {
        if (province.id === id) {
          names.push(province.name);
          return;
        }

        // 查找城市
        const city = province.children?.find(city => city.id === id);
        if (city) {
          names.push(city.name);
          return;
        }
      }
    });

    setSelectedRegionNames(names);
  };

  // 获取选中地区数量
  const getSelectedCount = () => {
    return selectedRegions.length;
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header title="地区选择演示" onBackPress={() => navigation.goBack()} />

      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>地区选择组件演示</Text>
          <Text style={styles.description}>
            点击下方按钮打开地区选择器，可以选择省份和城市
          </Text>

          <TouchableOpacity
            style={styles.selectButton}
            onPress={openRegionSelector}
          >
            <Text style={styles.selectButtonText}>
              {getSelectedCount() > 0
                ? `已选择 ${getSelectedCount()} 个地区`
                : '选择地区'}
            </Text>
          </TouchableOpacity>

          {selectedRegionNames.length > 0 && (
            <View style={styles.resultContainer}>
              <Text style={styles.resultTitle}>已选择地区：</Text>
              <View style={styles.tagsContainer}>
                {selectedRegionNames.map((name, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{name}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>使用说明</Text>
          <Text style={styles.description}>
            1. RegionSelector 组件支持省份和城市的多选
          </Text>
          <Text style={styles.description}>
            2. 可以通过 selectedRegions 属性设置默认选中的地区
          </Text>
          <Text style={styles.description}>
            3. onConfirm 回调函数会返回所有选中的地区ID
          </Text>
          <Text style={styles.description}>4. 组件支持自定义标题、样式等</Text>
        </View>
      </ScrollView>

      {/* 地区选择器 */}
      <RegionSelector
        visible={modalVisible}
        regions={mockRegions}
        selectedRegions={selectedRegions}
        onClose={closeRegionSelector}
        onConfirm={handleConfirm}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F7FA',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333333',
  },
  description: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
    lineHeight: 20,
  },
  selectButton: {
    backgroundColor: '#4B74FF',
    borderRadius: 6,
    padding: 12,
    alignItems: 'center',
    marginTop: 16,
  },
  selectButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  resultContainer: {
    marginTop: 20,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 10,
    color: '#333333',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    backgroundColor: '#F0F7FF',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E6EFFD',
  },
  tagText: {
    color: '#4B74FF',
    fontSize: 14,
  },
});

export default RegionSelectorDemoScreen;
