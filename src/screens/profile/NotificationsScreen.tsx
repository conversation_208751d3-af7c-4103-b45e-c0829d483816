import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ProfileStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';
import ScrollTabs from '../../components/common/ScrollTabs';
import Card from '../../components/common/Card';

type Props = NativeStackScreenProps<ProfileStackParamList, 'Notifications'>;

interface Notification {
  id: string;
  title: string;
  content: string;
  type: 'system' | 'follow' | 'update';
  isRead: boolean;
  timestamp: string;
  relatedId?: string;
  relatedType?: 'company' | 'person' | 'project' | 'performance' | 'patent';
}

const NotificationsScreen: React.FC<Props> = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState('all');
  const [refreshing, setRefreshing] = useState(false);

  const tabs = [
    { id: 'all', title: '全部' },
    { id: 'system', title: '系统' },
    { id: 'follow', title: '关注' },
    { id: 'update', title: '更新' },
  ];

  // 模拟通知数据
  const notifications: Notification[] = [
    {
      id: '1',
      title: '系统通知',
      content: '欢迎使用企业图谱App，点击查看新功能介绍',
      type: 'system',
      isRead: false,
      timestamp: '2023-06-15 10:30',
    },
    {
      id: '2',
      title: '企业动态',
      content: '您关注的某建筑集团有限公司发布了新的业绩信息',
      type: 'follow',
      isRead: false,
      timestamp: '2023-06-14 15:45',
      relatedId: '1',
      relatedType: 'company',
    },
    {
      id: '3',
      title: '人员信息更新',
      content: '您关注的张工程资质信息有更新',
      type: 'update',
      isRead: true,
      timestamp: '2023-06-13 09:20',
      relatedId: '301',
      relatedType: 'person',
    },
    {
      id: '4',
      title: '系统维护通知',
      content: '系统将于2023年6月20日22:00-次日02:00进行维护升级',
      type: 'system',
      isRead: true,
      timestamp: '2023-06-12 18:00',
    },
    {
      id: '5',
      title: '专利状态更新',
      content: '您关注的专利"一种新型节能墙体结构"状态已更新为"已授权"',
      type: 'update',
      isRead: false,
      timestamp: '2023-06-10 14:15',
      relatedId: '401',
      relatedType: 'patent',
    },
    {
      id: '6',
      title: '新增关注通知',
      content: '您的关注列表已添加"某建筑设计院有限公司"',
      type: 'follow',
      isRead: true,
      timestamp: '2023-06-08 11:30',
      relatedId: '2',
      relatedType: 'company',
    },
  ];

  // 根据当前选中的tab筛选数据
  const filteredNotifications = notifications.filter(
    notification => activeTab === 'all' || notification.type === activeTab,
  );

  const handleTabPress = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleNotificationPress = (notification: Notification) => {
    // 标记为已读
    // 实际项目中需要调用API更新状态

    // 如果有关联对象，跳转到相应详情页
    if (notification.relatedId && notification.relatedType) {
      switch (notification.relatedType) {
        case 'company':
          // 使用rootNavigation导航到Detail栈
          console.log('Navigate to company detail:', notification.relatedId);
          break;
        case 'person':
          console.log('Navigate to person detail:', notification.relatedId);
          break;
        case 'project':
          console.log('Navigate to project detail:', notification.relatedId);
          break;
        case 'performance':
          console.log(
            'Navigate to performance detail:',
            notification.relatedId,
          );
          break;
        case 'patent':
          console.log('Navigate to patent detail:', notification.relatedId);
          break;
      }
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    // 模拟刷新数据
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const handleReadAll = () => {
    // 实现标记全部已读的逻辑
    console.log('Mark all as read');
  };

  const renderNotificationItem = ({ item }: { item: Notification }) => (
    <TouchableOpacity onPress={() => handleNotificationPress(item)}>
      <Card
        className={`mb-3 p-4 ${
          !item.isRead ? 'border-l-4 border-blue-500' : ''
        }`}
      >
        <View className="flex-row justify-between items-start">
          <View className="flex-1">
            <Text
              className={`text-base ${
                !item.isRead ? 'font-bold' : 'font-medium'
              }`}
            >
              {item.title}
            </Text>
            <Text className="text-gray-600 text-sm mt-1">{item.content}</Text>
          </View>
          {!item.isRead && (
            <View className="bg-blue-50 px-2 py-1 rounded">
              <Text className="text-blue-500 text-xs">未读</Text>
            </View>
          )}
        </View>

        <View className="flex-row justify-between mt-2">
          <Text className="text-gray-400 text-xs">{item.timestamp}</Text>
          <Text className={`text-xs ${getTypeColor(item.type)}`}>
            {getTypeLabel(item.type)}
          </Text>
        </View>
      </Card>
    </TouchableOpacity>
  );

  // 获取类型对应的中文标签
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'system':
        return '系统通知';
      case 'follow':
        return '关注动态';
      case 'update':
        return '信息更新';
      default:
        return '其他通知';
    }
  };

  // 获取类型对应的颜色
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'system':
        return 'text-purple-500';
      case 'follow':
        return 'text-green-500';
      case 'update':
        return 'text-blue-500';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <Header
        title="消息通知"
        showBackButton
        onBackPress={() => navigation.goBack()}
        rightComponent={
          <TouchableOpacity onPress={handleReadAll}>
            <Text className="text-blue-500">全部已读</Text>
          </TouchableOpacity>
        }
      />

      {/* 选项卡 */}
      <ScrollTabs
        tabs={tabs}
        activeTab={activeTab}
        onTabPress={handleTabPress}
      />

      {/* 通知列表 */}
      <FlatList
        data={filteredNotifications}
        renderItem={renderNotificationItem}
        keyExtractor={item => item.id}
        contentContainerStyle={{ padding: 16 }}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => (
          <View className="items-center justify-center py-10">
            <Text className="text-gray-400">暂无通知消息</Text>
          </View>
        )}
      />
    </SafeAreaView>
  );
};

export default NotificationsScreen;
