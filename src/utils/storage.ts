import { MMKV } from 'react-native-mmkv';
import AsyncStorage from '@react-native-async-storage/async-storage';

// 检查是否在远程调试模式
const isRemoteDebugging = typeof (global as any).atob !== 'undefined';

// 创建MMKV实例（仅在非远程调试模式下）
let storage: MMKV | null = null;

try {
  if (!isRemoteDebugging) {
    storage = new MMKV({
      id: 'user-storage',
      encryptionKey: 'mof-app-key', // 可以使用更安全的密钥
    });
  }
} catch (error) {
  console.warn(
    'MMKV initialization failed, falling back to AsyncStorage:',
    error,
  );
  storage = null;
}

// 存储工具类
export class Storage {
  // 存储字符串
  static setString(key: string, value: string): void {
    if (storage) {
      storage.set(key, value);
    } else {
      AsyncStorage.setItem(key, value).catch(console.error);
    }
  }

  // 获取字符串
  static getString(key: string): string | undefined {
    if (storage) {
      return storage.getString(key);
    } else {
      // AsyncStorage 是异步的，这里返回 undefined，实际项目中可能需要异步版本
      console.warn(
        'Using AsyncStorage fallback - consider using async methods',
      );
      return undefined;
    }
  }

  // 存储对象
  static setObject(key: string, value: any): void {
    if (storage) {
      storage.set(key, JSON.stringify(value));
    } else {
      AsyncStorage.setItem(key, JSON.stringify(value)).catch(console.error);
    }
  }

  // 获取对象
  static getObject<T>(key: string): T | null {
    if (storage) {
      const value = storage.getString(key);
      if (value) {
        try {
          return JSON.parse(value) as T;
        } catch (error) {
          console.error('Failed to parse stored object:', error);
          return null;
        }
      }
      return null;
    } else {
      console.warn(
        'Using AsyncStorage fallback - consider using async methods',
      );
      return null;
    }
  }

  // 存储布尔值
  static setBoolean(key: string, value: boolean): void {
    if (storage) {
      storage.set(key, value);
    } else {
      AsyncStorage.setItem(key, value.toString()).catch(console.error);
    }
  }

  // 获取布尔值
  static getBoolean(key: string): boolean | undefined {
    if (storage) {
      return storage.getBoolean(key);
    } else {
      console.warn(
        'Using AsyncStorage fallback - consider using async methods',
      );
      return undefined;
    }
  }

  // 存储数字
  static setNumber(key: string, value: number): void {
    if (storage) {
      storage.set(key, value);
    } else {
      AsyncStorage.setItem(key, value.toString()).catch(console.error);
    }
  }

  // 获取数字
  static getNumber(key: string): number | undefined {
    if (storage) {
      return storage.getNumber(key);
    } else {
      console.warn(
        'Using AsyncStorage fallback - consider using async methods',
      );
      return undefined;
    }
  }

  // 删除键
  static delete(key: string): void {
    if (storage) {
      storage.delete(key);
    } else {
      AsyncStorage.removeItem(key).catch(console.error);
    }
  }

  // 清空所有数据
  static clearAll(): void {
    if (storage) {
      storage.clearAll();
    } else {
      AsyncStorage.clear().catch(console.error);
    }
  }

  // 检查键是否存在
  static contains(key: string): boolean {
    if (storage) {
      return storage.contains(key);
    } else {
      console.warn(
        'Using AsyncStorage fallback - consider using async methods',
      );
      return false;
    }
  }

  // 获取所有键
  static getAllKeys(): string[] {
    if (storage) {
      return storage.getAllKeys();
    } else {
      console.warn(
        'Using AsyncStorage fallback - consider using async methods',
      );
      return [];
    }
  }
}

// 存储键常量
export const StorageKeys = {
  USER_TOKEN: 'user_token',
  USER_INFO: 'user_info',
  IS_LOGGED_IN: 'is_logged_in',
  REMEMBER_PASSWORD: 'remember_password',
  LAST_LOGIN_PHONE: 'last_login_phone',
} as const;
