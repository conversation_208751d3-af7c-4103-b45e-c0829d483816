/**
 * 导航类型定义文件
 * 定义应用中所有导航栈和路由参数的类型
 */

import type { NavigatorScreenParams } from '@react-navigation/native';

// 主导航器参数
export type RootStackParamList = {
  Auth: NavigatorScreenParams<AuthStackParamList>;
  Main: NavigatorScreenParams<TabParamList>;
  Detail: NavigatorScreenParams<DetailStackParamList>;
  Settings: undefined;
  // 添加搜索相关页面到根导航
  Search: undefined;
  CompanySearch: undefined; // 新增企业搜索路由
  SearchResult: { keyword?: string; type: string };
  SearchFilter: { type: string };
  // 添加标讯相关页面到根导航
  BidNoticeSearch: { keyword?: string };
  BidNoticeFilter: undefined;
  OwnerSearch: undefined;
  BidNotice: undefined;
  BidDetail: { id: string };
};

// 底部标签页导航器参数
export type TabParamList = {
  HomeTab: NavigatorScreenParams<HomeStackParamList>;
  GraphTab: NavigatorScreenParams<GraphStackParamList>;
  FollowTab: NavigatorScreenParams<FollowStackParamList>;
  ProfileTab: NavigatorScreenParams<ProfileStackParamList>;
};

// 首页相关路由参数
export type HomeStackParamList = {
  Home: undefined;
  BidNotice: undefined;
  BidNoticeSearch: { keyword?: string };
  BidNoticeFilter: undefined;
  OwnerSearch: undefined;
};

// 登录相关路由参数
export type AuthStackParamList = {
  OneClickLogin: undefined;
  Login: { initialTab?: 'code' | 'password' };
  Register: undefined;
  VerificationCode: { phone: string; type: 'login' | 'register' | 'reset' };
  ForgotPassword: undefined;
  ResetPassword: { phone: string; code: string; type?: 'register' | 'reset' };
};

// 企业图谱相关路由参数
export type GraphStackParamList = {
  Graph: undefined;
  SearchHistory: undefined;
  CompanyList: { category?: string; title?: string };
  ProductList: { category?: string; title?: string };
  PerformanceList: { category?: string; title?: string };
  PersonList: { category?: string; title?: string };
  PatentList: { category?: string; title?: string };
};

// 详情相关路由参数
export type DetailStackParamList = {
  CompanyDetail: { id: string };
  CompanyGraph: { id: string };
  CompanyContact: { id: string };
  ProductDetail: { id: string };
  PersonDetail: { id: string };
  ProjectDetail: { id: string };
  PatentDetail: { id: string };
  PatentProgress: { id: string };
  PerformanceDetail: { id: string };
  PerformanceBusinessInfo: { id: string };
  PerformanceRelatedUnit: { id: string };
  QualificationDetail: { id: string };
  BidDetail: { id: string };
};

// 个人中心相关路由参数
export type ProfileStackParamList = {
  Profile: undefined;
  ProfileHome: undefined;
  Notifications: undefined;
  Feedback: undefined;
  EditProfile: undefined;
  EditName: undefined;
  Orders: undefined;
  Coupons: undefined;
  CouponPayment: undefined;
  CustomerService: undefined;
  PotentialCustomerList: undefined;
  PotentialCustomerPackage: undefined;
  Follows: undefined;
  About: undefined;
  Help: undefined;
  UserAgreement: undefined;
  Disclaimer: undefined;
  TabsDemo: undefined;
  RegionSelectorDemo: undefined;
  RegionPopoverDemo: undefined;
};

// 关注相关路由参数
export type FollowStackParamList = {
  Follows: undefined;
  FollowDetail: undefined;
  FollowSettings: undefined;
};
