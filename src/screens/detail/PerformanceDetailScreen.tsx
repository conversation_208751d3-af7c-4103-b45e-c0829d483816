import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { DetailStackParamList } from '@navigation/types';
import Header from '@components/common/Header';

type Props = NativeStackScreenProps<DetailStackParamList, 'PerformanceDetail'>;

interface Performance {
  id: string;
  name: string;
  projectNumber: string;
  projectType: string;
  constructionUnit: string;
  totalInvestment: string;
  category: string;
  status: string;
}

interface InfoCard {
  id: string;
  title: string;
  icon: any;
  items: InfoCardItem[];
}

interface InfoCardItem {
  id: string;
  title: string;
  icon: any;
}

const PerformanceDetailScreen: React.FC<Props> = ({ navigation, route }) => {
  const { id } = route.params;
  const [performance, setPerformance] = useState<Performance | null>(null);
  const [loading, setLoading] = useState(true);

  // 模拟信息卡片数据
  const infoCards: InfoCard[] = [
    {
      id: '1',
      title: '工商基本信息',
      icon: require('../../assets/h1.png'),
      items: [
        { id: '1-1', title: '详细信息', icon: require('../../assets/h1.png') },
        {
          id: '1-2',
          title: '参与单位及相关负责人',
          icon: require('../../assets/h1.png'),
        },
        { id: '1-3', title: '单位信息', icon: require('../../assets/h1.png') },
      ],
    },
    {
      id: '2',
      title: '招投标信息',
      icon: require('../../assets/h1.png'),
      items: [
        {
          id: '2-1',
          title: '参与单位及相关负责人',
          icon: require('../../assets/h1.png'),
        },
      ],
    },
    {
      id: '3',
      title: '合同登记',
      icon: require('../../assets/h1.png'),
      items: [
        {
          id: '3-1',
          title: '合同登记记录',
          icon: require('../../assets/h1.png'),
        },
      ],
    },
    {
      id: '4',
      title: '施工图审查',
      icon: require('../../assets/h1.png'),
      items: [
        {
          id: '4-1',
          title: '施工图审查信息',
          icon: require('../../assets/h1.png'),
        },
        {
          id: '4-2',
          title: '专业技术人员信息',
          icon: require('../../assets/h1.png'),
        },
        {
          id: '4-3',
          title: '违反强制性标准信息',
          icon: require('../../assets/h1.png'),
        },
      ],
    },
    {
      id: '5',
      title: '施工许可',
      icon: require('../../assets/h1.png'),
      items: [
        { id: '5-1', title: '许可信息', icon: require('../../assets/h1.png') },
        {
          id: '5-2',
          title: '质量监督信息',
          icon: require('../../assets/h1.png'),
        },
        {
          id: '5-3',
          title: '安全监督信息',
          icon: require('../../assets/h1.png'),
        },
        {
          id: '5-4',
          title: '施工现场安全专业人员信息',
          icon: require('../../assets/h1.png'),
        },
        {
          id: '5-5',
          title: '施工现场管理人员信息',
          icon: require('../../assets/h1.png'),
        },
        {
          id: '5-6',
          title: '施工现场特种作业人员信息',
          icon: require('../../assets/h1.png'),
        },
        {
          id: '5-7',
          title: '施工现场主要机械设备信息',
          icon: require('../../assets/h1.png'),
        },
        {
          id: '5-8',
          title: '施工现场检查信息',
          icon: require('../../assets/h1.png'),
        },
        {
          id: '5-9',
          title: '施工现场管理人员信息',
          icon: require('../../assets/h1.png'),
        },
      ],
    },
    {
      id: '6',
      title: '竣工验收',
      icon: require('@assets/h1.png'),
      items: [
        {
          id: '6-1',
          title: '竣工验收备案信息',
          icon: require('@assets/h1.png'),
        },
        {
          id: '6-2',
          title: '竣工验收信息',
          icon: require('@assets/h1.png'),
        },
      ],
    },
  ];

  useEffect(() => {
    // 模拟获取业绩数据
    const fetchPerformanceData = () => {
      setLoading(true);
      // 模拟网络请求
      setTimeout(() => {
        // 模拟业绩数据
        const mockPerformance: Performance = {
          id: id,
          name: '平顶山市第一人民医院（老院区）布服务采购',
          projectNumber: '12345623456',
          projectType: 'XXXXX',
          constructionUnit: 'XXXXXX',
          totalInvestment: '1000万',
          category: '市政建筑工程',
          status: 'D8',
        };

        setPerformance(mockPerformance);
        setLoading(false);
      }, 1000);
    };

    fetchPerformanceData();
  }, [id]);

  if (loading || !performance) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <Header
          title="业绩详情"
          showBackButton
          onBackPress={() => navigation.goBack()}
        />
        <View className="flex-1 items-center justify-center">
          <Text className="text-gray-500">加载中...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const renderInfoCard = (card: InfoCard) => (
    <View key={card.id} className="bg-white mb-3 p-4">
      <View className="flex-row items-center mb-4">
        <View className="w-1 h-5 bg-indigo-500 mr-2"></View>
        <Image
          source={card.icon}
          className="w-5 h-5 mr-2"
          resizeMode="contain"
        />
        <Text className="text-base font-medium text-gray-800">
          {card.title}
        </Text>
      </View>

      <View className="flex-row flex-wrap">
        {card.items.map(item => (
          <TouchableOpacity
            key={item.id}
            className="w-1/3 items-center mb-4"
            onPress={() => console.log(`Pressed ${item.title}`)}
          >
            <Image
              source={item.icon}
              className="w-10 h-10 mb-2"
              resizeMode="contain"
            />
            <Text className="text-xs text-gray-600 text-center">
              {item.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-100">
      <Header
        title="业绩详情"
        showBackButton
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView>
        {/* 顶部基本信息 */}
        <View className="bg-white p-4 mb-3">
          <Text className="text-lg font-medium mb-2">{performance.name}</Text>

          <View className="flex-row mb-2">
            <View className="bg-blue-100 px-2 py-0.5 rounded mr-2">
              <Text className="text-blue-500 text-xs">
                {performance.category}
              </Text>
            </View>
            <View className="bg-yellow-100 px-2 py-0.5 rounded">
              <Text className="text-yellow-600 text-xs">
                {performance.status}
              </Text>
            </View>
          </View>

          <Text className="text-gray-500 text-xs">
            项目编号: {performance.projectNumber} 项目类别:{' '}
            {performance.projectType}
          </Text>
          <Text className="text-gray-500 text-xs mt-1">
            建设单位: {performance.constructionUnit} 总投资:{' '}
            {performance.totalInvestment}
          </Text>
        </View>

        {/* 建设单位信息 */}
        <View className="bg-white p-4 mb-3 flex-row items-center">
          <View className="w-5 h-5 rounded-full bg-orange-500 items-center justify-center mr-2">
            <Text className="text-white text-xs">O</Text>
          </View>
          <Text className="text-gray-500 text-xs flex-1">
            建设单位: xxx有限责任公司（自然人投资或控股）
          </Text>

          <TouchableOpacity
            className="flex-row items-center ml-2"
            onPress={() => console.log('咨询 长沙')}
          >
            <Image
              source={require('@assets/h1.png')}
              className="w-5 h-5 mr-1"
              resizeMode="contain"
            />
            <Text className="text-blue-500 text-xs">湖南 长沙</Text>
          </TouchableOpacity>
        </View>

        {/* 信息卡片 */}
        {infoCards.map(card => renderInfoCard(card))}
      </ScrollView>
    </SafeAreaView>
  );
};

export default PerformanceDetailScreen;
