import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { DetailStackParamList } from '@navigation/types';
import Header from '@components/common/Header';

type Props = NativeStackScreenProps<
  DetailStackParamList,
  'QualificationDetail'
>;

interface QualificationItem {
  id: string;
  name: string;
  certificateNumber: string;
  issueDate: string;
  expiryDate: string;
  issuingAuthority: string;
}

const QualificationDetailScreen: React.FC<Props> = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState<string>('建筑业企业资质');

  // 模拟公司数据
  const company = {
    id: '1',
    name: '启魔方科技有限公司',
    logo: require('../../assets/h1.png'),
    size: '大型',
    level: 'A股',
    strength: '500强',
    technology: '国家高新技术',
    legalPerson: '启小柯',
    registeredCapital: '6000万',
    registrationDate: '1998-09-09',
    address: '青海省西宁市东川工业园区金桥路38号',
    industry: 'xxxxxxxxxxxxxx',
    website: 'www.qimofang.com',
  };

  // 模拟资质分类标签
  const qualificationTabs = [
    { id: '建筑业企业资质', title: '建筑业企业资质14', active: true },
    { id: '工程设计', title: '工程设计8', active: false },
    { id: '工程监理', title: '工程监理3', active: false },
    { id: '安全生产许可', title: '安全生产许可', active: false },
  ];

  // 模拟资质列表数据
  const qualifications: QualificationItem[] = [
    {
      id: '1',
      name: '建筑工程施工总承包一级',
      certificateNumber: 'D163006654',
      issueDate: '2020-05-09',
      expiryDate: '2023-12-31',
      issuingAuthority: '中华人民共和国住房和城乡建设部',
    },
    {
      id: '2',
      name: '建筑工程施工总承包一级',
      certificateNumber: 'D163006654',
      issueDate: '2020-05-09',
      expiryDate: '2023-12-31',
      issuingAuthority: '中华人民共和国住房和城乡建设部',
    },
  ];

  const handleBack = () => {
    navigation.goBack();
  };

  const handleTabPress = (tabId: string) => {
    setActiveTab(tabId);
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-100">
      <Header title="资质详情" showBackButton onBackPress={handleBack} />

      {/* 公司基本信息 */}
      <View className="bg-white p-4 mb-2">
        <View className="flex-row mb-4">
          <Image
            source={company.logo}
            className="w-16 h-16 rounded-md"
            resizeMode="contain"
          />
          <View className="ml-3 flex-1">
            <Text className="text-lg font-medium text-red-500">
              {company.name}
            </Text>
            <View className="flex-row flex-wrap mt-1">
              <View className="bg-orange-100 rounded-sm px-1 mr-1 mb-1">
                <Text className="text-orange-500 text-xs">{company.size}</Text>
              </View>
              <View className="bg-blue-100 rounded-sm px-1 mr-1 mb-1">
                <Text className="text-blue-500 text-xs">{company.level}</Text>
              </View>
              <View className="bg-green-100 rounded-sm px-1 mr-1 mb-1">
                <Text className="text-green-500 text-xs">
                  {company.strength}
                </Text>
              </View>
              <View className="bg-blue-100 rounded-sm px-1 mr-1 mb-1">
                <Text className="text-blue-500 text-xs">
                  {company.technology}
                </Text>
              </View>
            </View>
          </View>
          <TouchableOpacity className="ml-2">
            <Text className="text-blue-500">▼</Text>
          </TouchableOpacity>
        </View>

        <View className="flex-row justify-between py-2 border-b border-gray-100">
          <Text className="text-gray-500">企业法人</Text>
          <Text className="text-gray-700">{company.legalPerson}</Text>
        </View>

        <View className="flex-row justify-between py-2 border-b border-gray-100">
          <Text className="text-gray-500">注册资本</Text>
          <Text className="text-gray-700">{company.registeredCapital}</Text>
        </View>

        <View className="flex-row justify-between py-2 border-b border-gray-100">
          <Text className="text-gray-500">注册日期</Text>
          <Text className="text-gray-700">{company.registrationDate}</Text>
        </View>
      </View>

      {/* 公司详细信息 */}
      <View className="bg-blue-50 p-4 mb-2">
        <View className="mb-2">
          <Text className="text-gray-500">企业地址：{company.address}</Text>
        </View>
        <View className="mb-2">
          <Text className="text-gray-500">所属行业：{company.industry}</Text>
        </View>
        <View className="flex-row items-center">
          <Text className="text-gray-500">企业官网：</Text>
          <Text className="text-blue-500">{company.website}</Text>
        </View>
      </View>

      {/* 资质分类标签 */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        className="bg-white"
      >
        {qualificationTabs.map(tab => (
          <TouchableOpacity
            key={tab.id}
            className={`px-4 py-3 ${
              activeTab === tab.id ? 'border-b-2 border-blue-500' : ''
            }`}
            onPress={() => handleTabPress(tab.id)}
          >
            <Text
              className={
                activeTab === tab.id ? 'text-blue-500' : 'text-gray-500'
              }
            >
              {tab.title}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* 资质列表 */}
      <ScrollView className="flex-1">
        {qualifications.map(qualification => (
          <View
            key={qualification.id}
            className="bg-white p-4 mb-2 mx-4 mt-4 rounded-lg"
          >
            <View className="flex-row justify-between items-center mb-6">
              <Text className="text-lg font-medium">{qualification.name}</Text>
              <Image
                source={require('../../assets/h1.png')}
                className="w-12 h-12"
                resizeMode="contain"
              />
            </View>

            <View className="mb-2">
              <Text className="text-gray-500">
                资质编号：{qualification.certificateNumber}
              </Text>
            </View>

            <View className="mb-2">
              <Text className="text-gray-500">
                发证日期：{qualification.issueDate}
              </Text>
            </View>

            <View className="mb-2">
              <Text className="text-gray-500">
                有效期：{qualification.expiryDate}
              </Text>
            </View>

            <View>
              <Text className="text-gray-500">
                发证机关：{qualification.issuingAuthority}
              </Text>
            </View>
          </View>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
};

export default QualificationDetailScreen;
