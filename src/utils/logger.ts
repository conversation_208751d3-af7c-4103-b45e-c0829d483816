/**
 * API 日志工具
 * 提供统一的 API 请求和响应日志记录功能
 */

export interface ApiRequestLog {
  method: string;
  url: string;
  headers?: Record<string, any>;
  data?: any;
  params?: Record<string, any>;
  timestamp: string;
}

export interface ApiResponseLog {
  method: string;
  url: string;
  status: number;
  statusText: string;
  headers?: Record<string, any>;
  data?: any;
  timestamp: string;
  duration?: number;
}

export interface ApiErrorLog {
  method: string;
  url: string;
  status?: number;
  statusText?: string;
  message: string;
  error: any;
  timestamp: string;
}

class Logger {
  private isDevelopment = __DEV__;

  /**
   * 记录 API 请求
   */
  logRequest(log: ApiRequestLog) {
    if (!this.isDevelopment) return;

    console.group('🚀 API Request');
    console.log('Method:', log.method);
    console.log('URL:', log.url);
    console.log('Headers:', log.headers);
    if (log.data) {
      console.log('Data:', log.data);
    }
    if (log.params) {
      console.log('Params:', log.params);
    }
    console.log('Timestamp:', log.timestamp);
    console.groupEnd();
  }

  /**
   * 记录 API 响应成功
   */
  logResponse(log: ApiResponseLog) {
    if (!this.isDevelopment) return;

    console.group('✅ API Response Success');
    console.log('Method:', log.method);
    console.log('URL:', log.url);
    console.log('Status:', `${log.status} ${log.statusText}`);
    console.log('Headers:', log.headers);
    console.log('Data:', log.data);
    console.log('Timestamp:', log.timestamp);
    if (log.duration) {
      console.log('Duration:', `${log.duration}ms`);
    }
    console.groupEnd();
  }

  /**
   * 记录 API 错误
   */
  logError(log: ApiErrorLog) {
    if (!this.isDevelopment) return;

    console.group('❌ API Response Error');
    console.log('Method:', log.method);
    console.log('URL:', log.url);
    if (log.status) {
      console.log('Status:', `${log.status} ${log.statusText}`);
    }
    console.log('Message:', log.message);
    console.log('Error:', log.error);
    console.log('Timestamp:', log.timestamp);
    console.groupEnd();
  }

  /**
   * 记录认证相关日志
   */
  logAuth(message: string, data?: any) {
    if (!this.isDevelopment) return;

    console.group('🔐 Auth');
    console.log('Message:', message);
    if (data) {
      console.log('Data:', data);
    }
    console.log('Timestamp:', new Date().toISOString());
    console.groupEnd();
  }

  /**
   * 记录一般信息
   */
  logInfo(message: string, data?: any) {
    if (!this.isDevelopment) return;

    console.group('ℹ️ Info');
    console.log('Message:', message);
    if (data) {
      console.log('Data:', data);
    }
    console.log('Timestamp:', new Date().toISOString());
    console.groupEnd();
  }

  /**
   * 记录警告
   */
  logWarning(message: string, data?: any) {
    if (!this.isDevelopment) return;

    console.group('⚠️ Warning');
    console.log('Message:', message);
    if (data) {
      console.log('Data:', data);
    }
    console.log('Timestamp:', new Date().toISOString());
    console.groupEnd();
  }
}

export const logger = new Logger();
