import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Image,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ProfileStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';

type Props = NativeStackScreenProps<ProfileStackParamList, 'EditProfile'>;

interface UserProfile {
  avatar: string;
  name: string;
  company: string;
  position: string;
  phone: string;
  email: string;
  introduction: string;
}

const EditProfileScreen: React.FC<Props> = ({ navigation }) => {
  // 模拟用户数据
  const [profile, setProfile] = useState<UserProfile>({
    avatar: 'https://randomuser.me/api/portraits/men/43.jpg',
    name: '张三',
    company: '某建筑集团有限公司',
    position: '项目经理',
    phone: '13800138000',
    email: 'zhang<PERSON>@example.com',
    introduction: '从事建筑行业10年，专注于大型基础设施建设项目管理。',
  });

  const handleChangeAvatar = () => {
    // 实际项目中需要调用图片选择和上传功能
    Alert.alert('提示', '此功能需要接入图片选择和上传API');
  };

  const handleInputChange = (field: keyof UserProfile, value: string) => {
    setProfile(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = () => {
    // 表单验证
    if (!profile.name.trim()) {
      Alert.alert('提示', '请输入姓名');
      return;
    }

    if (!profile.phone.trim()) {
      Alert.alert('提示', '请输入手机号');
      return;
    }

    // 保存资料
    // 实际项目中需要调用API保存用户资料
    console.log('保存资料:', profile);

    // 保存成功后返回
    Alert.alert('保存成功', '个人资料已更新', [
      { text: '确定', onPress: () => navigation.goBack() },
    ]);
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <Header
        title="编辑资料"
        showBackButton
        onBackPress={() => navigation.goBack()}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView className="flex-1 px-4">
          {/* 头像 */}
          <View className="items-center mt-6 mb-6">
            <TouchableOpacity onPress={handleChangeAvatar}>
              <View className="relative">
                <Image
                  source={{ uri: profile.avatar }}
                  className="w-24 h-24 rounded-full"
                />
                <View className="absolute bottom-0 right-0 bg-blue-500 rounded-full w-8 h-8 items-center justify-center border-2 border-white">
                  <Text className="text-white text-xs">编辑</Text>
                </View>
              </View>
            </TouchableOpacity>
          </View>

          {/* 基本信息 */}
          <View className="mb-6">
            <Text className="text-base font-medium mb-4">基本信息</Text>

            <View className="mb-4">
              <Text className="text-gray-600 mb-1">姓名</Text>
              <TextInput
                className="bg-gray-50 rounded-lg p-3 text-base"
                value={profile.name}
                onChangeText={text => handleInputChange('name', text)}
                placeholder="请输入姓名"
                placeholderTextColor="#9CA3AF"
              />
            </View>

            <View className="mb-4">
              <Text className="text-gray-600 mb-1">公司</Text>
              <TextInput
                className="bg-gray-50 rounded-lg p-3 text-base"
                value={profile.company}
                onChangeText={text => handleInputChange('company', text)}
                placeholder="请输入公司名称"
                placeholderTextColor="#9CA3AF"
              />
            </View>

            <View className="mb-4">
              <Text className="text-gray-600 mb-1">职位</Text>
              <TextInput
                className="bg-gray-50 rounded-lg p-3 text-base"
                value={profile.position}
                onChangeText={text => handleInputChange('position', text)}
                placeholder="请输入职位"
                placeholderTextColor="#9CA3AF"
              />
            </View>
          </View>

          {/* 联系方式 */}
          <View className="mb-6">
            <Text className="text-base font-medium mb-4">联系方式</Text>

            <View className="mb-4">
              <Text className="text-gray-600 mb-1">手机号</Text>
              <TextInput
                className="bg-gray-50 rounded-lg p-3 text-base"
                value={profile.phone}
                onChangeText={text => handleInputChange('phone', text)}
                placeholder="请输入手机号"
                placeholderTextColor="#9CA3AF"
                keyboardType="phone-pad"
              />
            </View>

            <View className="mb-4">
              <Text className="text-gray-600 mb-1">邮箱</Text>
              <TextInput
                className="bg-gray-50 rounded-lg p-3 text-base"
                value={profile.email}
                onChangeText={text => handleInputChange('email', text)}
                placeholder="请输入邮箱"
                placeholderTextColor="#9CA3AF"
                keyboardType="email-address"
              />
            </View>
          </View>

          {/* 个人简介 */}
          <View className="mb-6">
            <Text className="text-base font-medium mb-4">个人简介</Text>
            <TextInput
              className="bg-gray-50 rounded-lg p-3 min-h-[120px] text-base"
              value={profile.introduction}
              onChangeText={text => handleInputChange('introduction', text)}
              placeholder="请输入个人简介"
              placeholderTextColor="#9CA3AF"
              multiline
              textAlignVertical="top"
            />
          </View>

          {/* 保存按钮 */}
          <View className="mt-4 mb-8">
            <TouchableOpacity
              className="bg-blue-500 py-3 rounded-lg items-center"
              onPress={handleSave}
            >
              <Text className="text-white font-medium text-base">保存</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default EditProfileScreen;
