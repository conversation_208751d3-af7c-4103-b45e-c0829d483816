import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import Button from './Button';
import WarmTip from './WarmTip';

const WarmTipExample: React.FC = () => {
  const [showBasicTip, setShowBasicTip] = useState(false);
  const [showCustomTip, setShowCustomTip] = useState(false);
  const [showAutoCloseTip, setShowAutoCloseTip] = useState(false);

  const handleBasicTipConfirm = () => {
    console.log('用户确认了基础提示');
  };

  const handleCustomTipConfirm = () => {
    console.log('用户确认了自定义提示');
  };

  return (
    <View style={styles.container}>
      {/* 基础温馨提示 */}
      <Button
        title="显示基础温馨提示"
        onPress={() => setShowBasicTip(true)}
        style={styles.button}
      />

      {/* 自定义温馨提示 */}
      <Button
        title="显示自定义温馨提示"
        onPress={() => setShowCustomTip(true)}
        style={styles.button}
      />

      {/* 自动关闭温馨提示 */}
      <Button
        title="显示自动关闭提示"
        onPress={() => setShowAutoCloseTip(true)}
        style={styles.button}
      />

      {/* 基础温馨提示 */}
      <WarmTip
        visible={showBasicTip}
        onClose={() => setShowBasicTip(false)}
        content="已阅读并同意《中国联通认证服务条款》以及《启魔方用户协议》和《启魔方免责声明》"
        onConfirm={handleBasicTipConfirm}
      />

      {/* 自定义温馨提示 */}
      <WarmTip
        visible={showCustomTip}
        onClose={() => setShowCustomTip(false)}
        title="重要提醒"
        content="请确保您的网络连接正常，以免影响数据同步。如果遇到问题，请联系客服获取帮助。"
        confirmText="明白了"
        onConfirm={handleCustomTipConfirm}
        showCloseButton={false}
        closeOnBackdropPress={false}
      />

      {/* 自动关闭温馨提示 */}
      <WarmTip
        visible={showAutoCloseTip}
        onClose={() => setShowAutoCloseTip(false)}
        title="操作成功"
        content="您的设置已保存成功！"
        confirmText="好的"
        autoClose={true}
        autoCloseDelay={2000}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    backgroundColor: '#F9FAFB',
  },
  button: {
    marginBottom: 16,
    width: '100%',
  },
});

export default WarmTipExample;
