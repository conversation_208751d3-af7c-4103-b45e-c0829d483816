import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  SafeAreaView,
  Image,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation/types';
import ScrollTabs from '../../components/common/ScrollTabs';

type Props = NativeStackScreenProps<RootStackParamList, 'SearchResult'>;

// 搜索结果接口
interface SearchResultItem {
  id: string;
  name: string;
  type: string;
  logo?: any;
  size?: string;
  level?: string;
  strength?: string;
  technology?: string;
  legalPerson?: string;
  registeredCapital?: string;
  registrationDate?: string;
  isFavorite?: boolean;
  // 业绩特有字段
  projectNumber?: string;
  projectType?: string;
  constructionUnit?: string;
  totalInvestment?: string;
  category?: string;
  status?: string;
  // 人员特有字段
  certificates?: string[];
  companyName?: string;
  avatarColor?: string;
  avatarText?: string;
  // 资质特有字段
  qualificationCount?: number;
  qualifications?: string[];
  address?: string;
  // 专利特有字段
  applicationNumber?: string;
  applicationDate?: string;
  publicationNumber?: string;
  publicationDate?: string;
  ipcClassification?: string;
  inventors?: string[];
  applicant?: string;
  agent?: string;
}

const SearchResultScreen: React.FC<Props> = ({ navigation, route }) => {
  const { keyword, type: initialType } = route.params;
  const [searchText, setSearchText] = useState(keyword);
  const [activeTab, setActiveTab] = useState(initialType);
  const [selectedFilters] = useState(['北京', '行业', '装修']);
  const [totalResults] = useState(1000);

  const tabs = [
    { id: 'company', title: '公司' },
    { id: 'product', title: '产品' },
    { id: 'performance', title: '业绩' },
    { id: 'person', title: '人员' },
    { id: 'qualification', title: '资质' },
    { id: 'patent', title: '专利' },
  ];

  // 模拟搜索结果数据
  const mockResults: SearchResultItem[] = [
    {
      id: '1',
      name: '启魔方科技有限公司',
      type: 'company',
      logo: require('../../assets/h1.png'),
      size: '大型',
      level: 'A股',
      strength: '500强',
      technology: '国家高新技术',
      legalPerson: '启小柯',
      registeredCapital: '6000万',
      registrationDate: '1998-09-09',
      isFavorite: false,
      qualificationCount: 14,
      qualifications: ['承包一级', '建筑业企业资质-施工总承包-建筑工程'],
      address: '青海省西宁市东川工业园区金桥路38号',
    },
    {
      id: '2',
      name: '启魔方科技有限公司',
      type: 'qualification',
      logo: require('../../assets/h1.png'),
      size: '大型',
      level: 'A股',
      strength: '500强',
      technology: '国家高新技术',
      legalPerson: '启小柯',
      registeredCapital: '6000万',
      registrationDate: '1998-09-09',
      qualificationCount: 14,
      qualifications: ['承包一级', '建筑业企业资质-施工总承包-建筑工程'],
      address: '青海省西宁市东川工业园区金桥路38号',
    },
    {
      id: '3',
      name: '启魔方科技有限公司',
      type: 'qualification',
      logo: require('../../assets/h2.png'),
      size: '大型',
      level: 'A股',
      strength: '500强',
      technology: '国家高新技术',
      legalPerson: '启小柯',
      registeredCapital: '6000万',
      registrationDate: '1998-09-09',
      qualificationCount: 14,
      qualifications: ['承包一级', '建筑业企业资质-施工总承包-建筑工程'],
      address: '青海省西宁市东川工业园区金桥路38号',
    },
    {
      id: '4',
      name: '专利名称专利名称专利名称专利名称专利名称专利名称专利名称专利名称专利名称专利名称专利名称利名称',
      type: 'patent',
      applicationNumber: '345678956',
      applicationDate: '2020-09-09',
      publicationNumber: '12345623456',
      publicationDate: '2020-09-09',
      ipcClassification: 'HD1B1/02',
      inventors: ['XXX', 'XXX'],
      applicant: 'XXX',
      agent: '北京远创理想知识产权代理事务所（普通合伙）',
    },
    {
      id: '5',
      name: '专利名称专利名称专利名称专利名称专利名称专利名称专利名称专利名称专利名称利名称',
      type: 'patent',
      applicationNumber: '345678956',
      applicationDate: '2020-09-09',
      publicationNumber: '12345623456',
      publicationDate: '2020-09-09',
      ipcClassification: 'HD1B1/02',
      inventors: ['XXX', 'XXX'],
      applicant: 'XXX',
      agent: '北京远创理想知识产权代理事务所（普通合伙）',
    },
  ];

  const handleSearch = () => {
    // 实现搜索逻辑，可以调用API进行搜索
  };

  const handleClearSearch = () => {
    setSearchText('');
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const handleItemPress = (item: SearchResultItem) => {
    if (item.type === 'performance') {
      navigation.navigate('Detail', {
        screen: 'PerformanceDetail',
        params: { id: item.id },
      });
    } else if (item.type === 'person') {
      navigation.navigate('Detail', {
        screen: 'PersonDetail',
        params: { id: item.id },
      });
    } else if (item.type === 'qualification') {
      navigation.navigate('Detail', {
        screen: 'QualificationDetail',
        params: { id: item.id },
      });
    } else if (item.type === 'patent') {
      navigation.navigate('Detail', {
        screen: 'PatentDetail',
        params: { id: item.id },
      });
    } else {
      navigation.navigate('Detail', {
        screen: 'CompanyDetail',
        params: { id: item.id },
      });
    }
  };

  const handleToggleFavorite = (_id: string) => {
    // 实现收藏/取消收藏逻辑
  };

  const renderSearchResultItem = ({ item }: { item: SearchResultItem }) => {
    // 只显示与当前activeTab类型匹配的搜索结果
    if (item.type !== activeTab) {
      return null;
    }

    if (activeTab === 'performance') {
      return (
        <TouchableOpacity
          onPress={() => handleItemPress(item)}
          className="mb-3 bg-white"
        >
          <View className="px-4 py-3">
            <Text className="text-base font-normal mb-2" numberOfLines={2}>
              {item.name}
            </Text>

            <View className="flex-row mb-2">
              <View className="bg-blue-100 px-2 py-0.5 rounded mr-2">
                <Text className="text-blue-500 text-xs">{item.category}</Text>
              </View>
              <View className="bg-yellow-100 px-2 py-0.5 rounded">
                <Text className="text-yellow-600 text-xs">{item.status}</Text>
              </View>
            </View>

            <Text className="text-gray-500 text-xs">
              项目编号: {item.projectNumber} 项目类别: {item.projectType}
            </Text>
            <Text className="text-gray-500 text-xs mt-1">
              建设单位: {item.constructionUnit} 总投资: {item.totalInvestment}
            </Text>
          </View>

          <View className="flex-row items-center px-4 py-2 border-t border-gray-100">
            <View className="w-5 h-5 rounded-full bg-orange-500 items-center justify-center mr-1">
              <Text className="text-white text-xs">O</Text>
            </View>
            <Text className="text-gray-500 text-xs flex-1">
              建设单位: xxx有限责任公司（自然人投资或控股）
            </Text>
          </View>

          <View className="flex-row justify-end px-4 py-2">
            <TouchableOpacity
              className="flex-row items-center"
              onPress={() => handleItemPress(item)}
            >
              <Image
                source={require('../../assets/h1.png')}
                className="w-5 h-5 mr-1"
                resizeMode="contain"
              />
              <Text className="text-blue-500 text-xs">咨询 长沙</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      );
    } else if (activeTab === 'person') {
      return (
        <TouchableOpacity
          onPress={() => handleItemPress(item)}
          className="mb-3 bg-white"
        >
          <View className="p-4">
            <View className="flex-row items-center">
              <View
                className={`w-12 h-12 ${
                  item.avatarColor || 'bg-blue-500'
                } rounded-full items-center justify-center mr-3`}
              >
                <Text className="text-white text-xl font-bold">
                  {item.avatarText || item.name.charAt(0)}
                </Text>
              </View>
              <Text className="text-lg font-medium flex-1">{item.name}</Text>
              <View>
                <Text className="text-gray-500">证书: 14</Text>
                <Text className="text-gray-500">业绩: 14</Text>
              </View>
            </View>

            <View className="flex-row flex-wrap mt-3">
              {item.certificates?.map((cert, index) => (
                <View
                  key={index}
                  className="bg-blue-50 px-3 py-1.5 rounded mr-2 mb-2"
                >
                  <Text className="text-blue-500 text-xs">{cert}</Text>
                </View>
              ))}
            </View>

            <View className="mt-2 bg-blue-50 p-3 rounded flex-row items-center">
              <View className="w-5 h-5 rounded-full bg-orange-500 items-center justify-center mr-2">
                <Text className="text-white text-xs">O</Text>
              </View>
              <Text className="text-gray-500 text-xs flex-1">
                证书所在单位: {item.companyName}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      );
    } else if (activeTab === 'qualification') {
      return (
        <TouchableOpacity
          onPress={() => handleItemPress(item)}
          className="mb-3 bg-white"
        >
          <View className="p-4">
            <View className="flex-row mb-4">
              <Image
                source={item.logo}
                className="w-16 h-16 rounded-md"
                resizeMode="contain"
              />
              <View className="ml-3 flex-1">
                <Text className="text-lg font-medium text-red-500">
                  {item.name}
                </Text>
                <View className="flex-row flex-wrap mt-1">
                  <View className="bg-orange-100 rounded-sm px-1 mr-1 mb-1">
                    <Text className="text-orange-500 text-xs">{item.size}</Text>
                  </View>
                  <View className="bg-blue-100 rounded-sm px-1 mr-1 mb-1">
                    <Text className="text-blue-500 text-xs">{item.level}</Text>
                  </View>
                  <View className="bg-green-100 rounded-sm px-1 mr-1 mb-1">
                    <Text className="text-green-500 text-xs">
                      {item.strength}
                    </Text>
                  </View>
                  <View className="bg-blue-100 rounded-sm px-1 mr-1 mb-1">
                    <Text className="text-blue-500 text-xs">
                      {item.technology}
                    </Text>
                  </View>
                </View>
              </View>
              <TouchableOpacity className="ml-2">
                <Text className="text-blue-500">▼</Text>
              </TouchableOpacity>
            </View>

            <View className="flex-row justify-between py-2 border-b border-gray-100">
              <Text className="text-gray-500">企业法人</Text>
              <Text className="text-gray-700">{item.legalPerson}</Text>
            </View>

            <View className="flex-row justify-between py-2 border-b border-gray-100">
              <Text className="text-gray-500">注册资本</Text>
              <Text className="text-gray-700">{item.registeredCapital}</Text>
            </View>

            <View className="flex-row justify-between py-2 border-b border-gray-100">
              <Text className="text-gray-500">注册日期</Text>
              <Text className="text-gray-700">{item.registrationDate}</Text>
            </View>

            <View className="bg-blue-50 p-3 mt-2 rounded">
              <Text className="text-gray-500">
                资质数量:{' '}
                <Text className="text-red-500 font-bold">
                  {item.qualificationCount}
                </Text>
              </Text>
              <Text className="text-gray-500 mt-1">
                企业资质: {item.qualifications?.join('、')}
              </Text>
            </View>

            <View className="flex-row items-center mt-3">
              <Image
                source={require('../../assets/h1.png')}
                className="w-5 h-5 mr-2"
                resizeMode="contain"
              />
              <Text className="text-blue-500 text-sm">{item.address}</Text>
            </View>
          </View>
        </TouchableOpacity>
      );
    } else if (activeTab === 'patent') {
      return (
        <TouchableOpacity
          onPress={() => handleItemPress(item)}
          className="mb-3 bg-white p-4"
        >
          <Text className="text-base mb-3" numberOfLines={2}>
            {item.name}
          </Text>

          <View className="flex-row">
            <View className="bg-blue-100 px-3 py-1 rounded mr-2">
              <Text className="text-blue-500 text-xs">
                申请号：{item.applicationNumber}
              </Text>
            </View>
            <View className="bg-orange-100 px-3 py-1 rounded">
              <Text className="text-orange-500 text-xs">
                申请日期：{item.applicationDate}
              </Text>
            </View>
          </View>

          <View className="mt-3">
            <Text className="text-gray-500 text-xs">
              公告号：{item.publicationNumber}
            </Text>
            <Text className="text-gray-500 text-xs mt-1">
              申请日期：{item.publicationDate}
            </Text>
            <Text className="text-gray-500 text-xs mt-1">
              IPC分类号：{item.ipcClassification}
            </Text>
            <Text className="text-gray-500 text-xs mt-1">
              发明人：{item.inventors?.join('、')}
            </Text>
            <Text className="text-gray-500 text-xs mt-1">
              申请人：{item.applicant}
            </Text>
            <Text className="text-gray-500 text-xs mt-1">
              代理人：{item.agent}
            </Text>
          </View>
        </TouchableOpacity>
      );
    } else {
      return (
        <TouchableOpacity
          onPress={() => handleItemPress(item)}
          className="mb-3 bg-white p-4"
        >
          <View className="flex-row">
            <Image
              source={item.logo}
              className="w-12 h-12 rounded-md"
              resizeMode="contain"
            />
            <View className="ml-3 flex-1">
              <Text className="text-base font-medium">{item.name}</Text>
              <View className="flex-row flex-wrap mt-1">
                <View className="bg-orange-100 rounded-sm px-1 mr-1 mb-1">
                  <Text className="text-orange-500 text-xs">{item.size}</Text>
                </View>
                <View className="bg-blue-100 rounded-sm px-1 mr-1 mb-1">
                  <Text className="text-blue-500 text-xs">{item.level}</Text>
                </View>
                <View className="bg-green-100 rounded-sm px-1 mr-1 mb-1">
                  <Text className="text-green-500 text-xs">
                    {item.strength}
                  </Text>
                </View>
                <View className="bg-blue-100 rounded-sm px-1 mr-1 mb-1">
                  <Text className="text-blue-500 text-xs">
                    {item.technology}
                  </Text>
                </View>
              </View>
            </View>
            <TouchableOpacity
              onPress={() => handleToggleFavorite(item.id)}
              className="ml-2"
            >
              <Text className="text-blue-500">★</Text>
            </TouchableOpacity>
          </View>
          <View className="mt-2">
            <Text className="text-gray-500 text-xs">
              企业法人：{item.legalPerson}
            </Text>
            <Text className="text-gray-500 text-xs">
              注册资本：{item.registeredCapital}
            </Text>
            <Text className="text-gray-500 text-xs">
              注册日期：{item.registrationDate}
            </Text>
          </View>
        </TouchableOpacity>
      );
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-100">
      {/* 搜索栏 */}
      <View className="flex-row items-center px-4 py-2 bg-white">
        <TouchableOpacity onPress={handleBack} className="mr-2">
          <Text className="text-2xl text-gray-700">←</Text>
        </TouchableOpacity>
        <View className="flex-row items-center flex-1 bg-gray-100 rounded-full px-4 py-2">
          <Image
            source={require('@assets/h1.png')}
            className="w-5 h-5 mr-2"
            resizeMode="contain"
          />
          <TextInput
            className="flex-1 h-8"
            placeholder="请输入公司名称、关键词"
            value={searchText}
            onChangeText={setSearchText}
            returnKeyType="search"
            onSubmitEditing={handleSearch}
          />
          {searchText.length > 0 && (
            <TouchableOpacity onPress={handleClearSearch}>
              <View className="bg-gray-300 rounded-full w-4 h-4 items-center justify-center">
                <Text className="text-white text-xs">×</Text>
              </View>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* 搜索类型标签 */}
      <View className="bg-white">
        <ScrollTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabPress={id => setActiveTab(id)}
        />
      </View>

      {/* 筛选条件 */}
      <View className="flex-row border-b border-gray-200 py-2 px-4 bg-white">
        <TouchableOpacity className="flex-row items-center mr-4">
          <Text
            className={`${
              activeTab === 'person' ? 'text-blue-500' : 'text-gray-700'
            }`}
          >
            {activeTab === 'patent' ? '专利类型' : '地区'}
          </Text>
          <Text
            className={`ml-1 ${
              activeTab === 'person' ? 'text-blue-500' : 'text-gray-400'
            }`}
          >
            ▼
          </Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-row items-center mr-4">
          <Text className="text-gray-700">
            {activeTab === 'person'
              ? '选择证书'
              : activeTab === 'patent'
              ? '法律状态'
              : '行业'}
          </Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-row items-center mr-4">
          <Text className="text-gray-700">
            {activeTab === 'person' ? '项目等级' : '更多筛选'}
          </Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-row items-center">
          <Text className="text-gray-700">
            {activeTab === 'person' ? '注册证书' : '排序名称'}
          </Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
      </View>

      {/* 已选筛选条件 */}
      <View className="bg-white px-4 py-2">
        <Text className="text-gray-500 text-xs">
          已选：
          {activeTab === 'patent'
            ? '北京/行业/装修'
            : selectedFilters.join('/')}
        </Text>
      </View>

      {/* 搜索结果统计 */}
      <View className="flex-row items-center bg-blue-100 px-4 py-2">
        <View className="flex-row items-center">
          <View className="w-5 h-5 bg-blue-500 rounded-full items-center justify-center mr-2">
            <Text className="text-white text-xs">i</Text>
          </View>
          <Text className="text-blue-500">搜索到</Text>
          <Text className="text-red-500 font-bold mx-1">{totalResults}</Text>
          <Text className="text-blue-500">个结果</Text>
        </View>
        <View className="flex-1 items-end">
          <Text className="text-gray-500 text-xs">第1/10页</Text>
        </View>
      </View>

      {/* 搜索结果列表 */}
      <FlatList
        data={mockResults}
        renderItem={renderSearchResultItem}
        keyExtractor={item => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 20 }}
        ListEmptyComponent={() => (
          <View className="items-center justify-center py-10 bg-white">
            <Text className="text-gray-400">暂无搜索结果</Text>
          </View>
        )}
      />
    </SafeAreaView>
  );
};

export default SearchResultScreen;
