import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  ScrollView,
  Image,
  Dimensions,
  TouchableOpacity,
  ImageSourcePropType,
  NativeSyntheticEvent,
  NativeScrollEvent,
} from 'react-native';

const { width: screenWidth } = Dimensions.get('window');

interface CarouselProps {
  images: Array<ImageSourcePropType>;
  autoPlay?: boolean;
  interval?: number;
  showIndicator?: boolean;
  imageHeight?: number;
  onPress?: (index: number) => void;
}

const Carousel: React.FC<CarouselProps> = ({
  images,
  autoPlay = true,
  interval = 3000,
  showIndicator = true,
  imageHeight = 180,
  onPress,
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);
  const timerRef = useRef<number | null>(null);

  // 处理滚动结束事件
  const handleScrollEnd = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    const contentOffsetX = e.nativeEvent.contentOffset.x;
    const newIndex = Math.round(contentOffsetX / screenWidth);
    setActiveIndex(newIndex);
  };

  // 滚动到指定索引
  const scrollToIndex = (index: number) => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: index * screenWidth,
        animated: true,
      });
    }
  };

  // 开始自动播放
  const startAutoPlay = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    timerRef.current = setInterval(() => {
      const nextIndex = (activeIndex + 1) % images.length;
      setActiveIndex(nextIndex);
      scrollToIndex(nextIndex);
    }, interval);
  }, [activeIndex, images.length, interval]);

  // 自动播放
  useEffect(() => {
    if (autoPlay && images.length > 1) {
      startAutoPlay();
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [autoPlay, images.length, activeIndex, startAutoPlay]);

  return (
    <View>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={handleScrollEnd}
        scrollEventThrottle={16}
        contentContainerStyle={{ width: screenWidth * images.length }}
      >
        {images.map((image, index) => (
          <TouchableOpacity
            key={index}
            activeOpacity={onPress ? 0.8 : 1}
            onPress={() => onPress && onPress(index)}
            style={{ width: screenWidth }}
          >
            <Image
              source={image}
              style={{ width: screenWidth, height: imageHeight }}
              resizeMode="cover"
            />
          </TouchableOpacity>
        ))}
      </ScrollView>

      {showIndicator && images.length > 1 && (
        <View className="flex-row absolute bottom-2 self-center">
          {images.map((_, index) => (
            <View
              key={index}
              className={`h-2 w-2 rounded-full mx-1 ${
                activeIndex === index ? 'bg-white' : 'bg-gray-400 opacity-60'
              }`}
            />
          ))}
        </View>
      )}
    </View>
  );
};

export default Carousel;
