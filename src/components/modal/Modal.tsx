import React from 'react';
import {
  Modal as RNModal,
  View,
  TouchableOpacity,
  Text,
  StyleProp,
  ViewStyle,
  TextStyle,
  TouchableWithoutFeedback,
} from 'react-native';

interface ModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  showCloseButton?: boolean;
  closeOnBackdropPress?: boolean;
  containerStyle?: StyleProp<ViewStyle>;
  contentStyle?: StyleProp<ViewStyle>;
  titleStyle?: StyleProp<TextStyle>;
  animationType?: 'none' | 'slide' | 'fade';
  position?: 'center' | 'bottom';
}

const Modal: React.FC<ModalProps> = ({
  visible,
  onClose,
  title,
  children,
  showCloseButton = true,
  closeOnBackdropPress = true,
  containerStyle,
  contentStyle,
  titleStyle,
  animationType = 'fade',
  position = 'center',
}) => {
  const handleBackdropPress = () => {
    if (closeOnBackdropPress) {
      onClose();
    }
  };

  const getPositionStyle = () => {
    switch (position) {
      case 'bottom':
        return 'justify-end';
      case 'center':
      default:
        return 'justify-center';
    }
  };

  const getContentStyle = () => {
    switch (position) {
      case 'bottom':
        return 'rounded-t-lg w-full';
      case 'center':
      default:
        return 'rounded-lg mx-5';
    }
  };

  return (
    <RNModal
      visible={visible}
      transparent
      animationType={animationType}
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <View
          className={`flex-1 bg-black/50 ${getPositionStyle()}`}
          style={containerStyle}
        >
          <TouchableWithoutFeedback>
            <View
              className={`bg-white ${getContentStyle()}`}
              style={contentStyle}
            >
              {(title || showCloseButton) && (
                <View className="flex-row items-center justify-between px-4 pt-4 pb-2">
                  {title ? (
                    <Text
                      className="text-lg font-medium text-gray-800"
                      style={titleStyle}
                    >
                      {title}
                    </Text>
                  ) : (
                    <View />
                  )}

                  {showCloseButton && (
                    <TouchableOpacity
                      onPress={onClose}
                      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                    >
                      <View className="w-6 h-6 items-center justify-center">
                        <View className="w-4 h-0.5 bg-gray-400 absolute rotate-45" />
                        <View className="w-4 h-0.5 bg-gray-400 absolute -rotate-45" />
                      </View>
                    </TouchableOpacity>
                  )}
                </View>
              )}

              <View className="px-4 pb-4">{children}</View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </RNModal>
  );
};

export default Modal;
