import { useState, useCallback } from 'react';

export interface WarmTipConfig {
  title?: string;
  content: string;
  confirmText?: string;
  onConfirm?: () => void;
  showCloseButton?: boolean;
  closeOnBackdropPress?: boolean;
  autoClose?: boolean;
  autoCloseDelay?: number;
}

export const useWarmTip = () => {
  const [visible, setVisible] = useState(false);
  const [config, setConfig] = useState<WarmTipConfig>({
    content: '',
  });

  const showTip = useCallback((tipConfig: WarmTipConfig) => {
    setConfig(tipConfig);
    setVisible(true);
  }, []);

  const hideTip = useCallback(() => {
    setVisible(false);
  }, []);

  const tipProps = {
    visible,
    onClose: hideTip,
    ...config,
  };

  return {
    showTip,
    hideTip,
    tipProps,
  };
};

// 便捷方法
export const useSimpleWarmTip = () => {
  const { showTip, hideTip, tipProps } = useWarmTip();

  const showSimpleTip = useCallback((content: string, onConfirm?: () => void) => {
    showTip({
      content,
      onConfirm,
    });
  }, [showTip]);

  const showSuccessTip = useCallback((content: string = '操作成功！') => {
    showTip({
      title: '成功',
      content,
      autoClose: true,
      autoCloseDelay: 2000,
    });
  }, [showTip]);

  const showErrorTip = useCallback((content: string = '操作失败，请重试') => {
    showTip({
      title: '错误',
      content,
      confirmText: '重试',
    });
  }, [showTip]);

  const showWarningTip = useCallback((content: string, onConfirm?: () => void) => {
    showTip({
      title: '警告',
      content,
      confirmText: '我知道了',
      onConfirm,
      closeOnBackdropPress: false,
    });
  }, [showTip]);

  return {
    showSimpleTip,
    showSuccessTip,
    showErrorTip,
    showWarningTip,
    hideTip,
    tipProps,
  };
};
