import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleProp,
  ViewStyle,
} from 'react-native';

interface HeaderProps {
  title: string;
  showBackButton?: boolean;
  onBackPress?: () => void;
  rightComponent?: React.ReactNode;
  containerStyle?: StyleProp<ViewStyle>;
  titleStyle?: StyleProp<ViewStyle>;
}

const Header: React.FC<HeaderProps> = ({
  title,
  showBackButton = true,
  onBackPress,
  rightComponent,
  containerStyle,
  titleStyle,
}) => {
  return (
    <View
      className="flex-row items-center justify-between px-4 py-3 bg-white border-b border-gray-200"
      style={containerStyle}
    >
      <View className="flex-row items-center">
        {showBackButton && (
          <TouchableOpacity
            onPress={onBackPress}
            className="mr-3 p-1"
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            {/* 返回箭头图标 */}
            <View className="w-5 h-5 justify-center items-center">
              <View className="w-2 h-2 border-l-2 border-b-2 border-gray-800 -rotate-45" />
            </View>
          </TouchableOpacity>
        )}
        <Text className="text-lg font-medium text-gray-800" style={titleStyle}>
          {title}
        </Text>
      </View>

      {rightComponent ? (
        <View>{rightComponent}</View>
      ) : (
        <View className="w-8" /> // 占位，保持标题居中
      )}
    </View>
  );
};

export default Header;
