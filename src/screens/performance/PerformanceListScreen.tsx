import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  Image,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import {
  GraphStackParamList,
  RootStackParamList,
} from '../../navigation/types';
import Header from '../../components/common/Header';
import SearchBar from '../../components/common/SearchBar';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

type Props = NativeStackScreenProps<GraphStackParamList, 'PerformanceList'>;
type RootNavigationProp = NativeStackNavigationProp<RootStackParamList>;

interface Performance {
  id: string;
  name: string;
  company: {
    id: string;
    name: string;
    role: string;
  };
  owner: {
    id?: string;
    name: string;
  };
  location: string;
  date: string;
  amount: string;
  category: string;
  status: string;
  description: string;
  tags: string[];
}

const PerformanceListScreen: React.FC<Props> = ({ navigation, route }) => {
  const { category, title } = route.params || {
    category: 'all',
    title: '业绩列表',
  };
  const rootNavigation = useNavigation<RootNavigationProp>();
  const [searchText, setSearchText] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [_selectedFilters, _setSelectedFilters] = useState<{
    [key: string]: any;
  }>({});

  // 模拟业绩数据
  const performances: Performance[] = [
    {
      id: '201',
      name: '平顶山市第一人民医院（老院区）布局服务采购项目-京物公第一标段招标',
      company: {
        id: '1',
        name: 'xxx有限责任公司（自然人投资或控股）',
        role: '总承包方',
      },
      owner: {
        id: '456',
        name: '某房地产开发有限公司',
      },
      location: '北京/行业/政府',
      date: '2022-05',
      amount: '1000万',
      category: '市政基础工程',
      status: 'D8',
      description:
        '总建筑面积约10万平方米的城市综合体项目，包含商业、办公、娱乐等多种功能',
      tags: ['商业', '综合体', '城市更新'],
    },
    {
      id: '202',
      name: '平顶山市第一人民医院（老院区）布局服务采购',
      company: {
        id: '13',
        name: 'xxx有限责任公司（自然人投资或控股）',
        role: '总承包方',
      },
      owner: {
        id: '457',
        name: '某交通投资集团',
      },
      location: '北京/行业/政府',
      date: '2021-09',
      amount: '1000万',
      category: '市政基础工程',
      status: 'D8',
      description: '全长120公里的高速公路建设项目，包含多座大型桥梁和隧道',
      tags: ['高速公路', '路桥', '交通'],
    },
  ];

  // 根据搜索文本和分类筛选业绩
  const filteredPerformances = performances.filter(performance => {
    const matchesSearch =
      searchText === '' ||
      performance.name.includes(searchText) ||
      performance.description.includes(searchText) ||
      performance.company.name.includes(searchText) ||
      performance.tags.some(tag => tag.includes(searchText));

    const matchesCategory =
      category === 'all' ||
      (category === 'building' && performance.category.includes('建筑')) ||
      (category === 'road' && performance.category.includes('公路')) ||
      (category === 'municipal' && performance.category.includes('市政')) ||
      (category === 'transportation' &&
        performance.category.includes('交通')) ||
      (category === 'water' && performance.category.includes('水利'));

    return matchesSearch && matchesCategory;
  });

  const handleSearch = () => {
    // 实现搜索逻辑
  };

  const handlePerformancePress = (id: string) => {
    rootNavigation.navigate('Detail', {
      screen: 'PerformanceDetail',
      params: { id },
    });
  };

  const handleCompanyPress = (id: string) => {
    rootNavigation.navigate('Detail', {
      screen: 'CompanyDetail',
      params: { id },
    });
  };

  const handleRefresh = () => {
    setRefreshing(true);
    // 模拟刷新数据
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const renderPerformanceItem = ({ item }: { item: Performance }) => (
    <TouchableOpacity onPress={() => handlePerformancePress(item.id)}>
      <View className="bg-white mb-3">
        <View className="px-4 py-3">
          <Text className="text-base font-normal mb-2" numberOfLines={2}>
            {item.name}
          </Text>

          <View className="flex-row mb-2">
            <View className="bg-blue-100 px-2 py-0.5 rounded mr-2">
              <Text className="text-blue-500 text-xs">市政基础工程</Text>
            </View>
            <View className="bg-yellow-100 px-2 py-0.5 rounded">
              <Text className="text-yellow-600 text-xs">D8</Text>
            </View>
          </View>

          <Text className="text-gray-500 text-xs">
            项目编号: 12345678456 项目类别: XXXXX
          </Text>
          <Text className="text-gray-500 text-xs mt-1">
            建设单位: XXXXXX 总投资: 1000万
          </Text>
        </View>

        <View className="flex-row items-center px-4 py-2 border-t border-gray-100">
          <View className="w-5 h-5 rounded-full bg-orange-500 items-center justify-center mr-1">
            <Text className="text-white text-xs">O</Text>
          </View>
          <Text className="text-gray-500 text-xs flex-1">
            建设单位: xxx有限责任公司（自然人投资或控股）
          </Text>
        </View>

        <View className="flex-row justify-end px-4 py-2">
          <TouchableOpacity
            className="flex-row items-center"
            onPress={() => handleCompanyPress(item.company.id)}
          >
            <Image
              source={require('../../assets/h1.png')}
              className="w-5 h-5 mr-1"
              resizeMode="contain"
            />
            <Text className="text-blue-500 text-xs">咨询 长沙</Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-100">
      <Header
        title={title || '业绩列表'}
        showBackButton
        onBackPress={() => navigation.goBack()}
      />

      {/* 搜索栏 */}
      <SearchBar
        value={searchText}
        onChangeText={setSearchText}
        onSearch={handleSearch}
        onClear={() => setSearchText('')}
      />

      {/* 已选筛选条件 */}
      <View className="px-4 py-2 bg-gray-50">
        <Text className="text-gray-500 text-xs">已选: 北京/行业/政府</Text>
      </View>

      {/* 筛选条件 */}
      <View className="flex-row border-b border-gray-200 py-2 px-4 bg-white">
        <TouchableOpacity className="flex-row items-center mr-4">
          <Text className="text-gray-700">地区</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-row items-center mr-4">
          <Text className="text-gray-700">项目业绩</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-row items-center mr-4">
          <Text className="text-gray-700">更多筛选</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-row items-center">
          <Text className="text-gray-700">排序名称</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
      </View>

      {/* 搜索结果统计 */}
      <View className="px-4 py-2 bg-blue-50 flex-row justify-between">
        <View className="flex-row items-center">
          <View className="w-5 h-5 rounded-full bg-blue-500 items-center justify-center mr-1">
            <Text className="text-white text-xs">i</Text>
          </View>
          <Text className="text-gray-500 text-xs">搜索到 1000 条数据</Text>
        </View>
        <Text className="text-gray-500 text-xs">第1/10页</Text>
      </View>

      {/* 业绩列表 */}
      <FlatList
        data={filteredPerformances}
        renderItem={renderPerformanceItem}
        keyExtractor={item => item.id}
        contentContainerStyle={{ padding: 16 }}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => (
          <View className="items-center justify-center py-10">
            <Text className="text-gray-400">暂无符合条件的业绩</Text>
          </View>
        )}
      />
    </SafeAreaView>
  );
};

export default PerformanceListScreen;
