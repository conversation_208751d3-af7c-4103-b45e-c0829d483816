import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Image,
  FlatList,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation/types';
import ScrollTabs from '../../components/common/ScrollTabs';
import FilterDropdown, {
  FilterOption,
  FilterMenuOption,
} from '../../components/common/FilterDropdown';
import { getFilterGroupsByTab } from '../../constants/filterOptions';

type Props = NativeStackScreenProps<RootStackParamList, 'Search'>;

interface SearchHistory {
  id: string;
  keyword: string;
}

interface HotSearch {
  id: string;
  keyword: string;
  rank: number;
  icon?: any;
}

// 搜索结果接口
interface SearchResultItem {
  id: string;
  name: string;
  type: string;
  logo?: any;
  size?: string;
  level?: string;
  strength?: string;
  technology?: string;
  legalPerson?: string;
  registeredCapital?: string;
  registrationDate?: string;
  isFavorite?: boolean;
  // 业绩特有字段
  projectNumber?: string;
  projectType?: string;
  constructionUnit?: string;
  totalInvestment?: string;
  category?: string;
  status?: string;
  // 人员特有字段
  certificates?: string[];
  companyName?: string;
  avatarColor?: string;
  avatarText?: string;
  // 资质特有字段
  qualificationCount?: number;
  qualifications?: string[];
  address?: string;
  // 专利特有字段
  applicationNumber?: string;
  applicationDate?: string;
  publicationNumber?: string;
  publicationDate?: string;
  ipcClassification?: string;
  inventors?: string[];
  applicant?: string;
  agent?: string;
}

// 移除FilterMenuOption接口定义，使用导入的FilterMenuOption

const SearchScreen: React.FC<Props> = ({ navigation }) => {
  const [keyword, setKeyword] = useState('');
  const [activeTab, setActiveTab] = useState('company');
  const [showCancelButton, setShowCancelButton] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [totalResults] = useState(1000);
  const [isLoading, setIsLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResultItem[]>([]);
  // 移除未使用的activeFilterId状态

  // 模拟搜索历史
  const searchHistories: SearchHistory[] = [
    { id: '1', keyword: '启客科技有限公司' },
    { id: '2', keyword: '北京小米' },
    { id: '3', keyword: '启客科技有限公司' },
    { id: '4', keyword: '北京广告' },
    { id: '5', keyword: '启客科技有限公司' },
  ];

  // 模拟热门搜索
  const hotSearches: HotSearch[] = [
    {
      id: '1',
      keyword: '小米科技有限公司',
      rank: 1,
      icon: require('../../assets/h1.png'),
    },
    {
      id: '2',
      keyword: '启客（北京）科技有限公司',
      rank: 2,
      icon: require('../../assets/h2.png'),
    },
    { id: '3', keyword: '北京华为信息科技有限公司', rank: 3 },
    { id: '4', keyword: '金风科技有限公司', rank: 4 },
    { id: '5', keyword: '北京xxx科技有限公司', rank: 5 },
    { id: '6', keyword: '北京xxx科技有限公司', rank: 6 },
    { id: '7', keyword: '北京xxx科技有限公司', rank: 7 },
    { id: '8', keyword: '北京xxx科技有限公司', rank: 8 },
    { id: '9', keyword: '北京xxx科技有限公司', rank: 9 },
    { id: '10', keyword: '北京xxx科技有限公司', rank: 10 },
  ];

  // 模拟搜索结果数据
  const mockResults: SearchResultItem[] = [
    {
      id: '1',
      name: '启魔方科技有限公司',
      type: 'company',
      logo: require('../../assets/h1.png'),
      size: '大型',
      level: 'A股',
      strength: '500强',
      technology: '国家高新技术',
      legalPerson: '启小柯',
      registeredCapital: '6000万',
      registrationDate: '1998-09-09',
      isFavorite: false,
    },
    {
      id: '2',
      name: '启魔方科技有限公司',
      type: 'qualification',
      logo: require('../../assets/h1.png'),
      size: '大型',
      level: 'A股',
      strength: '500强',
      technology: '国家高新技术',
      legalPerson: '启小柯',
      registeredCapital: '6000万',
      registrationDate: '1998-09-09',
      qualificationCount: 14,
      qualifications: ['承包一级', '建筑业企业资质-施工总承包-建筑工程'],
      address: '青海省西宁市东川工业园区金桥路38号',
    },
    {
      id: '3',
      name: '启魔方科技有限公司',
      type: 'qualification',
      logo: require('../../assets/h2.png'),
      size: '大型',
      level: 'A股',
      strength: '500强',
      technology: '国家高新技术',
      legalPerson: '启小柯',
      registeredCapital: '6000万',
      registrationDate: '1998-09-09',
      qualificationCount: 14,
      qualifications: ['承包一级', '建筑业企业资质-施工总承包-建筑工程'],
      address: '青海省西宁市东川工业园区金桥路38号',
    },
    {
      id: '4',
      name: '张三',
      type: 'person',
      avatarColor: 'bg-blue-500',
      avatarText: '张',
      certificates: ['二级注册建造师', '注册监理工程师', '一级注册建造师'],
      companyName: 'xxx有限责任公司（自然人投资或控股）',
    },
    {
      id: '5',
      name: '李四',
      type: 'person',
      avatarColor: 'bg-green-500',
      avatarText: '李',
      certificates: ['二级注册建造师', '注册监理工程师', '一级注册建造师'],
      companyName: 'xxx有限责任公司（自然人投资或控股）',
    },
    {
      id: '6',
      name: '平顶山市第一人民医院（老院区）布局服务采购项目-京物公第一标段招标',
      type: 'performance',
      projectNumber: '12345678456',
      projectType: 'XXXXX',
      constructionUnit: 'XXXXXX',
      totalInvestment: '1000万',
      category: '市政基础工程',
      status: 'D8',
    },
    {
      id: '7',
      name: '专利名称专利名称专利名称专利名称专利名称专利名称专利名称专利名称专利名称专利名称专利名称利名称',
      type: 'patent',
      applicationNumber: '*********',
      applicationDate: '2020-09-09',
      publicationNumber: '12345623456',
      publicationDate: '2020-09-09',
      ipcClassification: 'HD1B1/02',
      inventors: ['XXX', 'XXX'],
      applicant: 'XXX',
      agent: '北京远创理想知识产权代理事务所（普通合伙）',
    },
    {
      id: '8',
      name: '专利名称专利名称专利名称专利名称专利名称专利名称专利名称专利名称专利名称利名称',
      type: 'patent',
      applicationNumber: '*********',
      applicationDate: '2020-09-09',
      publicationNumber: '12345623456',
      publicationDate: '2020-09-09',
      ipcClassification: 'HD1B1/02',
      inventors: ['XXX', 'XXX'],
      applicant: 'XXX',
      agent: '北京远创理想知识产权代理事务所（普通合伙）',
    },
  ];

  const tabs = [
    { id: 'company', title: '公司' },
    { id: 'product', title: '产品' },
    { id: 'performance', title: '业绩' },
    { id: 'person', title: '人员' },
    { id: 'qualification', title: '资质' },
    { id: 'patent', title: '专利' },
  ];

  // 筛选条件菜单选项
  const filterMenuOptions: FilterMenuOption[] = [
    { id: 'area', title: '地区' },
    { id: 'industry', title: '行业' },
    { id: 'more', title: '更多筛选' },
    { id: 'sort', title: '排序名称' },
  ];

  const handleSearch = () => {
    if (keyword.trim()) {
      setIsLoading(true);
      setShowResults(true);

      // 模拟API请求延迟
      setTimeout(() => {
        // 根据activeTab筛选搜索结果
        const filteredResults = mockResults.filter(
          item => item.type === activeTab,
        );
        setSearchResults(filteredResults);
        setIsLoading(false);
      }, 1500);
    }
  };

  const handleClearHistory = () => {
    // 实现清除历史记录的逻辑
  };

  const handleHistoryItemPress = (item: SearchHistory) => {
    setKeyword(item.keyword);
    handleSearch();
  };

  const handleHotSearchPress = (item: HotSearch) => {
    setKeyword(item.keyword);
    handleSearch();
  };

  const handleCancel = () => {
    if (showResults) {
      setShowResults(false);
    } else {
      setKeyword('');
      setShowCancelButton(false);
      // 隐藏键盘
    }
  };

  const handleBack = () => {
    if (showResults) {
      setShowResults(false);
    } else {
      navigation.goBack();
    }
  };

  const handleClearSearch = () => {
    setKeyword('');
  };

  const handleItemPress = (item: SearchResultItem) => {
    if (item.type === 'performance') {
      navigation.navigate('Detail', {
        screen: 'PerformanceDetail',
        params: { id: item.id },
      });
    } else if (item.type === 'person') {
      navigation.navigate('Detail', {
        screen: 'PersonDetail',
        params: { id: item.id },
      });
    } else if (item.type === 'qualification') {
      navigation.navigate('Detail', {
        screen: 'QualificationDetail',
        params: { id: item.id },
      });
    } else if (item.type === 'patent') {
      navigation.navigate('Detail', {
        screen: 'PatentDetail',
        params: { id: item.id },
      });
    } else {
      navigation.navigate('Detail', {
        screen: 'CompanyDetail',
        params: { id: item.id },
      });
    }
  };

  const handleToggleFavorite = (_id: string) => {
    // 实现收藏/取消收藏逻辑
  };

  const handleTabChange = (id: string) => {
    setActiveTab(id);
    // 切换标签时关闭筛选下拉
    if (showResults) {
      setIsLoading(true);

      // 模拟API请求延迟
      setTimeout(() => {
        // 根据activeTab筛选搜索结果
        const filteredResults = mockResults.filter(item => item.type === id);
        setSearchResults(filteredResults);
        setIsLoading(false);
      }, 800);
    }
  };

  // 移除未使用的handleFilterPress函数

  const handleFilterApply = (selectedOptions: {
    [key: string]: FilterOption[];
  }) => {
    // 处理筛选条件应用
    console.log('应用筛选条件:', selectedOptions);

    // 更新已选筛选条件显示
    const newSelectedFilters: string[] = [];

    Object.entries(selectedOptions).forEach(([_, options]) => {
      options.forEach(option => {
        newSelectedFilters.push(option.name);
      });
    });

    // setSelectedFilters(
    //   newSelectedFilters.length > 0 ? newSelectedFilters : ['暂无筛选条件'],
    // );

    // 重新加载搜索结果
    if (showResults) {
      setIsLoading(true);
      setTimeout(() => {
        setSearchResults(mockResults);
        setIsLoading(false);
      }, 800);
    }
  };

  const handleFilterReset = () => {
    // 处理重置筛选条件
    // setSelectedFilters(['暂无筛选条件']);
  };

  // 根据不同类型渲染不同的结果项
  const renderResultItemByType = (item: SearchResultItem) => {
    switch (activeTab) {
      case 'company':
        return (
          <TouchableOpacity
            onPress={() => handleItemPress(item)}
            className="mb-3 bg-white p-4"
          >
            <View className="flex-row">
              <Image
                source={item.logo}
                className="w-12 h-12 rounded-md"
                resizeMode="contain"
              />
              <View className="ml-3 flex-1">
                <Text className="text-base font-medium">{item.name}</Text>
                <View className="flex-row flex-wrap mt-1">
                  <View className="bg-orange-100 rounded-sm px-1 mr-1 mb-1">
                    <Text className="text-orange-500 text-xs">{item.size}</Text>
                  </View>
                  <View className="bg-blue-100 rounded-sm px-1 mr-1 mb-1">
                    <Text className="text-blue-500 text-xs">{item.level}</Text>
                  </View>
                  <View className="bg-green-100 rounded-sm px-1 mr-1 mb-1">
                    <Text className="text-green-500 text-xs">
                      {item.strength}
                    </Text>
                  </View>
                  <View className="bg-blue-100 rounded-sm px-1 mr-1 mb-1">
                    <Text className="text-blue-500 text-xs">
                      {item.technology}
                    </Text>
                  </View>
                </View>
              </View>
              <TouchableOpacity
                onPress={() => handleToggleFavorite(item.id)}
                className="ml-2"
              >
                <Text className="text-blue-500">★</Text>
              </TouchableOpacity>
            </View>
            <View className="mt-2">
              <Text className="text-gray-500 text-xs">
                企业法人：{item.legalPerson}
              </Text>
              <Text className="text-gray-500 text-xs">
                注册资本：{item.registeredCapital}
              </Text>
              <Text className="text-gray-500 text-xs">
                注册日期：{item.registrationDate}
              </Text>
            </View>
          </TouchableOpacity>
        );
      case 'product':
        // 产品类型的结果项样式
        return (
          <TouchableOpacity
            onPress={() => handleItemPress(item)}
            className="mb-3 bg-white p-4"
          >
            <View className="flex-row">
              <Image
                source={item.logo}
                className="w-12 h-12 rounded-md"
                resizeMode="contain"
              />
              <View className="ml-3 flex-1">
                <Text className="text-base font-medium">{item.name}</Text>
                <Text className="text-gray-500 mt-1">产品类型: 软件产品</Text>
                <Text className="text-gray-500">
                  所属企业: 启魔方科技有限公司
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        );
      case 'performance':
        // 业绩类型的结果项样式 - 更新为与图片一致的样式
        return (
          <TouchableOpacity
            onPress={() => handleItemPress(item)}
            className="mb-3 bg-white"
          >
            <View className="px-4 py-3">
              <Text className="text-base font-normal mb-2" numberOfLines={2}>
                {item.name}
              </Text>

              <View className="flex-row mb-2">
                <View className="bg-blue-100 px-2 py-0.5 rounded mr-2">
                  <Text className="text-blue-500 text-xs">{item.category}</Text>
                </View>
                <View className="bg-yellow-100 px-2 py-0.5 rounded">
                  <Text className="text-yellow-600 text-xs">{item.status}</Text>
                </View>
              </View>

              <Text className="text-gray-500 text-xs">
                项目编号: {item.projectNumber} 项目类别: {item.projectType}
              </Text>
              <Text className="text-gray-500 text-xs mt-1">
                建设单位: {item.constructionUnit} 总投资: {item.totalInvestment}
              </Text>
            </View>

            <View className="flex-row items-center px-4 py-2 border-t border-gray-100">
              <View className="w-5 h-5 rounded-full bg-orange-500 items-center justify-center mr-1">
                <Text className="text-white text-xs">O</Text>
              </View>
              <Text className="text-gray-500 text-xs flex-1">
                建设单位: xxx有限责任公司（自然人投资或控股）
              </Text>
            </View>

            <View className="flex-row justify-end px-4 py-2">
              <TouchableOpacity
                className="flex-row items-center"
                onPress={() => handleItemPress(item)}
              >
                <Image
                  source={require('../../assets/h1.png')}
                  className="w-5 h-5 mr-1"
                  resizeMode="contain"
                />
                <Text className="text-blue-500 text-xs">咨询 长沙</Text>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        );
      case 'person':
        // 人员类型的结果项样式 - 更新为与图片完全一致
        return (
          <TouchableOpacity
            onPress={() => handleItemPress(item)}
            className="mb-3 bg-white"
          >
            <View className="p-4">
              <View className="flex-row items-center">
                <View
                  className={`w-12 h-12 ${
                    item.avatarColor || 'bg-blue-500'
                  } rounded-full items-center justify-center mr-3`}
                >
                  <Text className="text-white text-xl font-bold">
                    {item.avatarText || item.name.charAt(0)}
                  </Text>
                </View>
                <Text className="text-lg font-medium flex-1">{item.name}</Text>
                <View>
                  <Text className="text-gray-500">证书: 14</Text>
                  <Text className="text-gray-500">业绩: 14</Text>
                </View>
              </View>

              <View className="flex-row flex-wrap mt-3">
                {item.certificates?.map((cert, index) => (
                  <View
                    key={index}
                    className="bg-blue-50 px-3 py-1.5 rounded mr-2 mb-2"
                  >
                    <Text className="text-blue-500 text-xs">{cert}</Text>
                  </View>
                ))}
              </View>

              <View className="mt-2 bg-blue-50 p-3 rounded flex-row items-center">
                <View className="w-5 h-5 rounded-full bg-orange-500 items-center justify-center mr-2">
                  <Text className="text-white text-xs">O</Text>
                </View>
                <Text className="text-gray-500 text-xs flex-1">
                  证书所在单位: {item.companyName}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        );
      case 'qualification':
        return (
          <TouchableOpacity
            onPress={() => handleItemPress(item)}
            className="mb-3 bg-white"
          >
            <View className="p-4">
              <View className="flex-row mb-4">
                <Image
                  source={item.logo}
                  className="w-16 h-16 rounded-md"
                  resizeMode="contain"
                />
                <View className="ml-3 flex-1">
                  <Text className="text-lg font-medium text-red-500">
                    {item.name}
                  </Text>
                  <View className="flex-row flex-wrap mt-1">
                    <View className="bg-orange-100 rounded-sm px-1 mr-1 mb-1">
                      <Text className="text-orange-500 text-xs">
                        {item.size}
                      </Text>
                    </View>
                    <View className="bg-blue-100 rounded-sm px-1 mr-1 mb-1">
                      <Text className="text-blue-500 text-xs">
                        {item.level}
                      </Text>
                    </View>
                    <View className="bg-green-100 rounded-sm px-1 mr-1 mb-1">
                      <Text className="text-green-500 text-xs">
                        {item.strength}
                      </Text>
                    </View>
                    <View className="bg-blue-100 rounded-sm px-1 mr-1 mb-1">
                      <Text className="text-blue-500 text-xs">
                        {item.technology}
                      </Text>
                    </View>
                  </View>
                </View>
                <TouchableOpacity className="ml-2">
                  <Text className="text-blue-500">▼</Text>
                </TouchableOpacity>
              </View>

              <View className="flex-row justify-between py-2 border-b border-gray-100">
                <Text className="text-gray-500">企业法人</Text>
                <Text className="text-gray-700">{item.legalPerson}</Text>
              </View>

              <View className="flex-row justify-between py-2 border-b border-gray-100">
                <Text className="text-gray-500">注册资本</Text>
                <Text className="text-gray-700">{item.registeredCapital}</Text>
              </View>

              <View className="flex-row justify-between py-2 border-b border-gray-100">
                <Text className="text-gray-500">注册日期</Text>
                <Text className="text-gray-700">{item.registrationDate}</Text>
              </View>

              <View className="bg-blue-50 p-3 mt-2 rounded">
                <Text className="text-gray-500">
                  资质数量:{' '}
                  <Text className="text-red-500 font-bold">
                    {item.qualificationCount}
                  </Text>
                </Text>
                <Text className="text-gray-500 mt-1">
                  企业资质: {item.qualifications?.join('、')}
                </Text>
              </View>

              <View className="flex-row items-center mt-3">
                <Image
                  source={require('../../assets/h1.png')}
                  className="w-5 h-5 mr-2"
                  resizeMode="contain"
                />
                <Text className="text-blue-500 text-sm">{item.address}</Text>
              </View>
            </View>
          </TouchableOpacity>
        );
      case 'patent':
        return (
          <TouchableOpacity
            onPress={() => handleItemPress(item)}
            className="mb-3 bg-white p-4"
          >
            <Text className="text-base mb-3" numberOfLines={2}>
              {item.name}
            </Text>

            <View className="flex-row">
              <View className="bg-blue-100 px-3 py-1 rounded mr-2">
                <Text className="text-blue-500 text-xs">
                  申请号：{item.applicationNumber}
                </Text>
              </View>
              <View className="bg-orange-100 px-3 py-1 rounded">
                <Text className="text-orange-500 text-xs">
                  申请日期：{item.applicationDate}
                </Text>
              </View>
            </View>

            <View className="mt-3">
              <Text className="text-gray-500 text-xs">
                公告号：{item.publicationNumber}
              </Text>
              <Text className="text-gray-500 text-xs mt-1">
                申请日期：{item.publicationDate}
              </Text>
              <Text className="text-gray-500 text-xs mt-1">
                IPC分类号：{item.ipcClassification}
              </Text>
              <Text className="text-gray-500 text-xs mt-1">
                发明人：{item.inventors?.join('、')}
              </Text>
              <Text className="text-gray-500 text-xs mt-1">
                申请人：{item.applicant}
              </Text>
              <Text className="text-gray-500 text-xs mt-1">
                代理人：{item.agent}
              </Text>
            </View>
          </TouchableOpacity>
        );
      default:
        return (
          <TouchableOpacity
            onPress={() => handleItemPress(item)}
            className="mb-3 bg-white p-4"
          >
            <Text className="text-base font-medium">{item.name}</Text>
            <Text className="text-gray-500 mt-1">未知类型</Text>
          </TouchableOpacity>
        );
    }
  };

  const renderSearchResultItem = ({ item }: { item: SearchResultItem }) => {
    return renderResultItemByType(item);
  };

  // 初始化筛选按钮引用
  React.useEffect(() => {
    // 不再需要初始化按钮引用
  }, [activeTab]);

  // 搜索页面内容
  const renderSearchContent = () => (
    <>
      {/* 搜索类型标签 */}
      <View className="bg-white">
        <ScrollTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabPress={handleTabChange}
        />
      </View>

      {/* 历史搜索和热门搜索 */}
      <ScrollView className="flex-1 bg-white">
        {/* 历史搜索 */}
        {searchHistories.length > 0 && (
          <View className="p-4">
            <View className="flex-row justify-between items-center mb-2">
              <View className="flex-row items-center">
                <Image
                  source={require('../../assets/h1.png')}
                  className="w-5 h-5 mr-2"
                  resizeMode="contain"
                />
                <Text className="font-medium text-gray-700">历史查询</Text>
              </View>
              <TouchableOpacity onPress={handleClearHistory}>
                <Image
                  source={require('../../assets/h2.png')}
                  className="w-5 h-5"
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </View>

            <View>
              {searchHistories.map(item => (
                <TouchableOpacity
                  key={item.id}
                  className="py-2 border-b border-gray-100 flex-row items-center"
                  onPress={() => handleHistoryItemPress(item)}
                >
                  <Image
                    source={require('../../assets/h1.png')}
                    className="w-5 h-5 mr-2"
                    resizeMode="contain"
                  />
                  <Text className="text-gray-700">{item.keyword}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}

        {/* 热门搜索 */}
        <View className="p-4 bg-blue-50 flex-1">
          <View className="flex-row items-center mb-4">
            <Image
              source={require('../../assets/h1.png')}
              className="w-5 h-5 mr-2"
              resizeMode="contain"
            />
            <Text className="font-medium text-gray-700">热门搜索</Text>
          </View>

          <View>
            {hotSearches.map((item, index) => (
              <TouchableOpacity
                key={item.id}
                className="py-3 flex-row items-center"
                onPress={() => handleHotSearchPress(item)}
              >
                <Text
                  className={`w-6 text-center mr-2 font-bold ${
                    index < 3 ? 'text-red-500' : 'text-gray-400'
                  }`}
                >
                  {item.rank}
                </Text>
                {item.icon && (
                  <Image
                    source={item.icon}
                    className="w-6 h-6 mr-2"
                    resizeMode="contain"
                  />
                )}
                {!item.icon && index < 3 && <View className="w-6 h-6 mr-2" />}
                <Text className="text-gray-700">{item.keyword}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </>
  );

  // 搜索结果内容
  const renderResultsContent = () => (
    <View style={styles.container}>
      {/* 搜索类型标签 */}
      <View className="bg-white">
        <ScrollTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabPress={handleTabChange}
        />
      </View>

      {/* 集成的筛选组件 - 确保它在正确的位置 */}
      <FilterDropdown
        filterMenuOptions={filterMenuOptions}
        getFilterGroupsByTab={getFilterGroupsByTab}
        onApply={handleFilterApply}
        onReset={handleFilterReset}
      />

      {/* 搜索结果部分 */}
      <View style={styles.resultsContainer}>
        {/* 搜索结果统计 */}
        <View className="flex-row items-center bg-blue-100 px-4 py-2">
          <View className="flex-row items-center">
            <View className="w-5 h-5 bg-blue-500 rounded-full items-center justify-center mr-2">
              <Text className="text-white text-xs">i</Text>
            </View>
            <Text className="text-blue-500">搜索到</Text>
            <Text className="text-red-500 font-bold mx-1">{totalResults}</Text>
            <Text className="text-blue-500">个结果</Text>
          </View>
          <View className="flex-1 items-end">
            <Text className="text-gray-500 text-xs">第1/10页</Text>
          </View>
        </View>

        {/* 加载指示器或搜索结果列表 */}
        {isLoading ? (
          <View className="flex-1 items-center justify-center bg-white">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="mt-4 text-gray-500">正在搜索中...</Text>
          </View>
        ) : (
          <FlatList
            data={searchResults}
            renderItem={renderSearchResultItem}
            keyExtractor={item => item.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
            ListEmptyComponent={() => (
              <View className="items-center justify-center py-10 bg-white">
                <Text className="text-gray-400">暂无搜索结果</Text>
              </View>
            )}
          />
        )}
      </View>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-blue-50">
      {/* 搜索栏 */}
      <View className="flex-row items-center px-4 py-2 bg-white">
        {showResults && (
          <TouchableOpacity onPress={handleBack} className="mr-2">
            <Text className="text-2xl text-gray-700">←</Text>
          </TouchableOpacity>
        )}
        <View className="flex-row items-center flex-1 bg-gray-100 rounded-full px-4 py-2">
          <Image
            source={require('@assets/h1.png')}
            className="w-5 h-5 mr-2"
            resizeMode="contain"
          />
          <TextInput
            className="flex-1 h-8"
            placeholder="请输入公司名称、关键词"
            value={keyword}
            onChangeText={text => {
              setKeyword(text);
              setShowCancelButton(true);
            }}
            returnKeyType="search"
            onSubmitEditing={handleSearch}
            onFocus={() => setShowCancelButton(true)}
            editable={!isLoading}
          />
          {keyword.length > 0 && (
            <TouchableOpacity onPress={handleClearSearch} disabled={isLoading}>
              <View className="bg-gray-300 rounded-full w-4 h-4 items-center justify-center">
                <Text className="text-white text-xs">×</Text>
              </View>
            </TouchableOpacity>
          )}
        </View>
        {(showCancelButton || showResults) && (
          <TouchableOpacity
            className="ml-3 px-2"
            onPress={handleCancel}
            disabled={isLoading}
          >
            <Text className={`text-gray-700 ${isLoading ? 'opacity-50' : ''}`}>
              取消
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* 根据状态显示搜索页面或搜索结果 */}
      {showResults ? renderResultsContent() : renderSearchContent()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
    zIndex: 1,
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: 'white',
    zIndex: 1, // 确保搜索结果在筛选下拉菜单下方
  },
});

export default SearchScreen;
