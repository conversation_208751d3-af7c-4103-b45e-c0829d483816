import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { GraphStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';
import SearchBar from '../../components/common/SearchBar';
import Card from '../../components/common/Card';

type Props = NativeStackScreenProps<GraphStackParamList, 'PatentList'>;

interface Patent {
  id: string;
  name: string;
  applicationNumber: string;
  applicant: {
    id: string;
    name: string;
  };
  inventors: string[];
  applicationDate: string;
  publicationDate: string;
  status: string;
  type: string;
  abstract: string;
  tags: string[];
}

const PatentListScreen: React.FC<Props> = ({ navigation, route }) => {
  const { category, title } = route.params || {
    category: 'all',
    title: '专利列表',
  };
  const [searchText, setSearchText] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  // 模拟专利数据
  const patents: Patent[] = [
    {
      id: '401',
      name: '一种新型节能墙体结构',
      applicationNumber: 'CN202XXXXX',
      applicant: {
        id: '1',
        name: '某建筑集团有限公司',
      },
      inventors: ['张三', '李四'],
      applicationDate: '2020-05-20',
      publicationDate: '2020-11-15',
      status: '已授权',
      type: '发明专利',
      abstract:
        '本发明提供了一种新型节能墙体结构，通过特殊材料组合和结构设计，实现了更好的保温隔热效果，降低建筑能耗。',
      tags: ['建筑', '节能', '墙体'],
    },
    {
      id: '402',
      name: '一种装配式建筑连接结构',
      applicationNumber: 'CN202XXXXX',
      applicant: {
        id: '2',
        name: '某建筑设计院有限公司',
      },
      inventors: ['王五', '赵六'],
      applicationDate: '2021-03-10',
      publicationDate: '2021-09-25',
      status: '实质审查',
      type: '发明专利',
      abstract:
        '本发明涉及一种装配式建筑连接结构，提高了装配式建筑的连接稳定性和抗震性能。',
      tags: ['装配式', '连接结构', '抗震'],
    },
    {
      id: '403',
      name: '一种智能混凝土养护系统',
      applicationNumber: 'CN202XXXXX',
      applicant: {
        id: '3',
        name: '某市政工程有限公司',
      },
      inventors: ['钱七', '孙八'],
      applicationDate: '2019-12-05',
      publicationDate: '2020-06-20',
      status: '已授权',
      type: '实用新型',
      abstract:
        '本实用新型提供了一种智能混凝土养护系统，通过传感器监测混凝土湿度、温度等参数，自动控制养护环境，提高混凝土质量。',
      tags: ['混凝土', '养护', '智能系统'],
    },
    {
      id: '404',
      name: '一种桥梁支座减震结构',
      applicationNumber: 'CN202XXXXX',
      applicant: {
        id: '13',
        name: '某路桥工程有限公司',
      },
      inventors: ['周九', '吴十'],
      applicationDate: '2022-01-15',
      publicationDate: '2022-07-30',
      status: '公开',
      type: '发明专利',
      abstract:
        '本发明提供了一种桥梁支座减震结构，有效降低地震等外力对桥梁的影响，提高桥梁的安全性。',
      tags: ['桥梁', '支座', '减震'],
    },
    {
      id: '405',
      name: '一种建筑废水循环利用装置',
      applicationNumber: 'CN202XXXXX',
      applicant: {
        id: '14',
        name: '某建筑科技有限公司',
      },
      inventors: ['郑十一', '王十二'],
      applicationDate: '2021-08-12',
      publicationDate: '2022-02-28',
      status: '已授权',
      type: '实用新型',
      abstract:
        '本实用新型涉及一种建筑废水循环利用装置，可有效处理建筑施工过程中产生的废水，实现循环利用，降低环境污染。',
      tags: ['建筑废水', '循环利用', '环保'],
    },
    {
      id: '406',
      name: '一种地下工程防水结构',
      applicationNumber: 'CN202XXXXX',
      applicant: {
        id: '5',
        name: '某水利水电工程有限公司',
      },
      inventors: ['李十三', '赵十四'],
      applicationDate: '2020-11-20',
      publicationDate: '2021-05-10',
      status: '已授权',
      type: '发明专利',
      abstract:
        '本发明提供了一种地下工程防水结构，通过创新的材料组合和结构设计，有效解决了地下工程防水难题。',
      tags: ['地下工程', '防水', '结构设计'],
    },
  ];

  // 根据搜索文本和分类筛选专利
  const filteredPatents = patents.filter(patent => {
    const matchesSearch =
      searchText === '' ||
      patent.name.includes(searchText) ||
      patent.abstract.includes(searchText) ||
      patent.applicant.name.includes(searchText) ||
      patent.tags.some(tag => tag.includes(searchText));

    const matchesCategory =
      category === 'all' ||
      (category === 'invention' && patent.type.includes('发明')) ||
      (category === 'utility' && patent.type.includes('实用新型')) ||
      (category === 'design' && patent.type.includes('外观设计')) ||
      (category === 'granted' && patent.status.includes('已授权'));

    return matchesSearch && matchesCategory;
  });

  const handleSearch = () => {
    // 实现搜索逻辑
  };

  const handlePatentPress = (id: string) => {
    navigation.navigate('Detail', { screen: 'PatentDetail', params: { id } });
  };

  const handleApplicantPress = (id: string) => {
    navigation.navigate('Detail', { screen: 'CompanyDetail', params: { id } });
  };

  const handleRefresh = () => {
    setRefreshing(true);
    // 模拟刷新数据
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const renderPatentItem = ({ item }: { item: Patent }) => (
    <TouchableOpacity onPress={() => handlePatentPress(item.id)}>
      <Card className="mb-3 p-4">
        <View className="flex-row justify-between items-start">
          <View className="flex-1">
            <Text className="text-lg font-bold">{item.name}</Text>
            <TouchableOpacity
              onPress={() => handleApplicantPress(item.applicant.id)}
            >
              <Text className="text-blue-500 mt-1">{item.applicant.name}</Text>
            </TouchableOpacity>
          </View>
          <View className={`px-2 py-1 rounded ${getStatusColor(item.status)}`}>
            <Text className={`text-xs ${getStatusTextColor(item.status)}`}>
              {item.status}
            </Text>
          </View>
        </View>

        <View className="mt-2">
          <Text className="text-gray-500 text-sm">
            申请号: {item.applicationNumber}
          </Text>
          <Text className="text-gray-500 text-sm mt-1">类型: {item.type}</Text>
        </View>

        <Text className="text-gray-600 text-sm mt-2" numberOfLines={2}>
          {item.abstract}
        </Text>

        <View className="flex-row justify-between mt-2">
          <Text className="text-gray-500 text-xs">
            申请日: {item.applicationDate}
          </Text>
          <Text className="text-gray-500 text-xs">
            公开日: {item.publicationDate}
          </Text>
        </View>

        <View className="flex-row flex-wrap mt-2">
          {item.tags.map((tag, index) => (
            <View
              key={index}
              className="bg-gray-100 rounded-full px-2 py-0.5 mr-1 mb-1"
            >
              <Text className="text-gray-600 text-xs">{tag}</Text>
            </View>
          ))}
        </View>
      </Card>
    </TouchableOpacity>
  );

  // 获取状态对应的背景色
  const getStatusColor = (status: string) => {
    switch (status) {
      case '已授权':
        return 'bg-green-50';
      case '实质审查':
        return 'bg-blue-50';
      case '公开':
        return 'bg-yellow-50';
      default:
        return 'bg-gray-50';
    }
  };

  // 获取状态对应的文字颜色
  const getStatusTextColor = (status: string) => {
    switch (status) {
      case '已授权':
        return 'text-green-500';
      case '实质审查':
        return 'text-blue-500';
      case '公开':
        return 'text-yellow-600';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <Header
        title={title}
        showBackButton
        onBackPress={() => navigation.goBack()}
      />

      {/* 搜索栏 */}
      <SearchBar
        value={searchText}
        onChangeText={setSearchText}
        onSearch={handleSearch}
        onClear={() => setSearchText('')}
      />

      {/* 筛选条件 */}
      <View className="flex-row border-b border-gray-200 py-2 px-4 bg-white">
        <TouchableOpacity className="flex-row items-center mr-4">
          <Text className="text-gray-700">类型</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-row items-center mr-4">
          <Text className="text-gray-700">状态</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-row items-center mr-4">
          <Text className="text-gray-700">日期</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-row items-center">
          <Text className="text-gray-700">排序</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
      </View>

      {/* 专利列表 */}
      <FlatList
        data={filteredPatents}
        renderItem={renderPatentItem}
        keyExtractor={item => item.id}
        contentContainerStyle={{ padding: 16 }}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => (
          <View className="items-center justify-center py-10">
            <Text className="text-gray-400">暂无符合条件的专利</Text>
          </View>
        )}
      />
    </SafeAreaView>
  );
};

export default PatentListScreen;
