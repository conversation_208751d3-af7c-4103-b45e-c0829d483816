import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Image,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { DetailStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';

type Props = NativeStackScreenProps<DetailStackParamList, 'PersonDetail'>;

interface Certificate {
  id: string;
  name: string;
  professionalSealNumber: string;
  certificateNumber: string;
  registeredSpecialty: string;
  validUntil: string;
  registrationUnit: string;
}

interface Performance {
  id: string;
  name: string;
  level: string;
  location: string;
  category: string;
  constructionUnit: string;
  totalInvestment: string;
}

const PersonDetailScreen: React.FC<Props> = ({ navigation }) => {
  // 在实际应用中，应该使用route.params.id来获取对应的人员数据
  const [activeTab, setActiveTab] = useState<'certificates' | 'performances'>(
    'certificates',
  );

  // 模拟人员数据 - 实际应用中应该根据id从API获取
  const person = {
    id: '1',
    name: '李四',
    avatarColor: 'bg-green-500',
    certificates: ['二级注册建造师', '注册监理工程师', '一级注册建造师'],
    companyName: 'xxx有限责任公司（自然人投资或控股）',
    certificatesCount: 14,
    performancesCount: 14,
  };

  // 模拟证书数据
  const certificates: Certificate[] = [
    {
      id: '1',
      name: '注册监理工程师',
      professionalSealNumber: '1234562',
      certificateNumber: '45678678',
      registeredSpecialty: '公路工程',
      validUntil: '2026-09-09',
      registrationUnit: 'xxx有限责任公司（自然人投资或控股）',
    },
    {
      id: '2',
      name: '注册监理工程师',
      professionalSealNumber: '1234562',
      certificateNumber: '45678678',
      registeredSpecialty: '公路工程',
      validUntil: '2026-09-09',
      registrationUnit: 'xxx有限责任公司（自然人投资或控股）',
    },
    {
      id: '3',
      name: '注册监理工程师',
      professionalSealNumber: '1234562',
      certificateNumber: '45678678',
      registeredSpecialty: '公路工程',
      validUntil: '2026-09-09',
      registrationUnit: 'xxx有限责任公司（自然人投资或控股）',
    },
  ];

  // 模拟业绩数据
  const performances: Performance[] = [
    {
      id: '1',
      name: '稷山县城排水管网雨污分流改造工程',
      level: 'C 级',
      location: '山西省运城市稷山县',
      category: '市政基础设施工程',
      constructionUnit: '稷山县住房保障和城乡建设管理局',
      totalInvestment: '985.1万',
    },
    {
      id: '2',
      name: '稷山县城排水管网雨污分流改造工程',
      level: 'C 级',
      location: '山西省运城市稷山县',
      category: '市政基础设施工程',
      constructionUnit: '稷山县住房保障和城乡建设管理局',
      totalInvestment: '985.1万',
    },
  ];

  const handleBack = () => {
    navigation.goBack();
  };

  const handleTabPress = (tab: 'certificates' | 'performances') => {
    setActiveTab(tab);
  };

  const renderCertificateItem = ({ item }: { item: Certificate }) => (
    <View className="bg-white mb-3 p-4 rounded-md">
      <Text className="text-lg font-medium mb-4">{item.name}</Text>

      <View className="mb-3">
        <Text className="text-gray-500">
          职业印章号：{item.professionalSealNumber}
        </Text>
        <Text className="text-gray-500 mt-2">
          证书编号：{item.certificateNumber}
        </Text>
      </View>

      <View className="mb-3">
        <Text className="text-gray-500">
          注册专业：{item.registeredSpecialty}
        </Text>
        <Text className="text-gray-500 mt-2">有效期：{item.validUntil}</Text>
      </View>

      <View className="flex-row items-center mt-4">
        <Image
          source={require('@assets/h1.png')}
          className="w-6 h-6 mr-2"
          resizeMode="contain"
        />
        <Text className="text-blue-500">注册单位: {item.registrationUnit}</Text>
      </View>
    </View>
  );

  const renderPerformanceItem = ({ item }: { item: Performance }) => (
    <View className="bg-white mb-3 p-4 rounded-md">
      <Text className="text-lg font-medium mb-4">{item.name}</Text>

      <Text className="text-gray-500 mb-2">项目等级: {item.level}</Text>
      <Text className="text-gray-500 mb-2">项目属地: {item.location}</Text>
      <Text className="text-gray-500 mb-2">项目类别: {item.category}</Text>
      <Text className="text-gray-500 mb-2">
        建设单位: {item.constructionUnit}
      </Text>
      <Text className="text-gray-500">项目总投资: {item.totalInvestment}</Text>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-100">
      <Header title="注册人员详情" showBackButton onBackPress={handleBack} />

      {/* 人员基本信息 */}
      <View className="bg-white p-4 mb-2">
        <View className="flex-row items-center mb-4">
          <View
            className={`w-16 h-16 ${person.avatarColor} rounded-full items-center justify-center`}
          >
            <Text className="text-white text-2xl font-bold">
              {person.name.charAt(0)}
            </Text>
          </View>
          <Text className="text-xl font-medium ml-4">{person.name}</Text>
        </View>

        <View className="flex-row flex-wrap mb-4">
          {person.certificates.map((cert, index) => (
            <View
              key={index}
              className="bg-blue-100 px-3 py-1 rounded mr-2 mb-2"
            >
              <Text className="text-blue-500 text-sm">{cert}</Text>
            </View>
          ))}
        </View>

        <View className="bg-blue-50 p-3 rounded flex-row items-center">
          <Image
            source={require('@assets/h1.png')}
            className="w-6 h-6 mr-2"
            resizeMode="contain"
          />
          <Text className="text-gray-500 text-sm">
            证书所在单位: {person.companyName}
          </Text>
        </View>
      </View>

      {/* 标签切换 */}
      <View className="flex-row bg-white mb-2">
        <TouchableOpacity
          className={`flex-1 py-3 items-center ${
            activeTab === 'certificates' ? 'border-b-2 border-blue-500' : ''
          }`}
          onPress={() => handleTabPress('certificates')}
        >
          <Text
            className={`${
              activeTab === 'certificates' ? 'text-blue-500' : 'text-gray-500'
            }`}
          >
            专业证书{person.certificatesCount}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          className={`flex-1 py-3 items-center ${
            activeTab === 'performances' ? 'border-b-2 border-blue-500' : ''
          }`}
          onPress={() => handleTabPress('performances')}
        >
          <Text
            className={`${
              activeTab === 'performances' ? 'text-blue-500' : 'text-gray-500'
            }`}
          >
            人员业绩{person.performancesCount}
          </Text>
        </TouchableOpacity>
      </View>

      {/* 内容区域 */}
      <ScrollView className="flex-1 px-4 py-2">
        {activeTab === 'certificates' &&
          certificates.map(cert => (
            <View key={cert.id}>{renderCertificateItem({ item: cert })}</View>
          ))}

        {activeTab === 'performances' &&
          performances.map(perf => (
            <View key={perf.id}>{renderPerformanceItem({ item: perf })}</View>
          ))}
      </ScrollView>
    </SafeAreaView>
  );
};

export default PersonDetailScreen;
