import React from 'react';
import { View, Text, SafeAreaView, ScrollView } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ProfileStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';

type Props = NativeStackScreenProps<ProfileStackParamList, 'Disclaimer'>;

const DisclaimerScreen: React.FC<Props> = ({ navigation }) => {
  return (
    <SafeAreaView className="flex-1 bg-white">
      <Header
        title="免责声明"
        showBackButton
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView className="flex-1 px-4 py-4">
        <Text className="text-xl font-bold mb-4">免责声明</Text>

        <Text className="text-base mb-3">
          感谢您使用企业图谱应用。在使用本应用前，请您仔细阅读以下免责声明，以了解您的权利和义务。
        </Text>

        <View className="mb-4">
          <Text className="text-lg font-medium mb-2">1. 信息准确性</Text>
          <Text className="text-base text-gray-700 leading-6">
            本应用提供的企业信息数据来源于公开渠道和企业自主填报，我们会尽力确保信息的准确性和完整性，但不对信息的准确性、完整性和及时性作出任何保证。用户在使用相关信息前应自行核实，并自行承担使用后果。
          </Text>
        </View>

        <View className="mb-4">
          <Text className="text-lg font-medium mb-2">2. 服务可用性</Text>
          <Text className="text-base text-gray-700 leading-6">
            我们努力确保本应用的稳定运行，但不保证服务不会中断，不保证服务的及时性、安全性或无错误。由于互联网的特殊性，可能会出现因网络、通信线路、第三方网站等原因导致的服务中断或不能满足用户要求的情况。
          </Text>
        </View>

        <View className="mb-4">
          <Text className="text-lg font-medium mb-2">3. 决策责任</Text>
          <Text className="text-base text-gray-700 leading-6">
            本应用提供的信息和数据仅供参考，不构成任何投资、合作或其他商业决策建议。用户基于本应用获取的信息所做出的决策，完全基于用户的独立判断，与本应用无关，由用户自行承担相关风险和责任。
          </Text>
        </View>

        <View className="mb-4">
          <Text className="text-lg font-medium mb-2">4. 第三方链接</Text>
          <Text className="text-base text-gray-700 leading-6">
            本应用可能包含指向第三方网站或资源的链接，这些链接仅为方便用户而提供，我们对这些网站或资源的可用性、内容、产品、服务或操作不做任何保证，不对因使用或信赖第三方网站内容、产品或服务而造成的任何损失或损害承担责任。
          </Text>
        </View>

        <View className="mb-4">
          <Text className="text-lg font-medium mb-2">5. 知识产权声明</Text>
          <Text className="text-base text-gray-700 leading-6">
            本应用显示的第三方信息（如企业标志、名称等）均为各自所有者的知识产权。这些信息的展示不代表本应用与其所有者有任何关联或被授权使用这些知识产权。如有侵权，请及时通知我们，我们将尽快处理。
          </Text>
        </View>

        <View className="mb-4">
          <Text className="text-lg font-medium mb-2">6. 用户行为</Text>
          <Text className="text-base text-gray-700 leading-6">
            用户在使用本应用过程中的一切行为应符合国家法律法规和本应用的相关规定。用户不得利用本应用从事任何违法活动，不得侵犯他人合法权益。对于用户的违法或侵权行为，本应用不承担任何责任。
          </Text>
        </View>

        <View className="mb-4">
          <Text className="text-lg font-medium mb-2">7. 责任限制</Text>
          <Text className="text-base text-gray-700 leading-6">
            在法律允许的最大范围内，本应用及其关联公司、员工、代理商等不对因使用或无法使用本应用而导致的任何直接、间接、附带、特殊、衍生性或惩罚性损害赔偿承担责任，包括但不限于利润损失、商誉损失、数据丢失或其他无形损失。
          </Text>
        </View>

        <View className="mb-8">
          <Text className="text-lg font-medium mb-2">8. 声明更新</Text>
          <Text className="text-base text-gray-700 leading-6">
            我们保留随时更新或修改本免责声明的权利，更新后的声明将在应用内公布。继续使用本应用即表示您接受更新后的免责声明。建议您定期查阅本声明以了解任何变更。
          </Text>
        </View>

        <Text className="text-base text-gray-500 text-center mb-6">
          最后更新日期：2023年12月1日
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
};

export default DisclaimerScreen;
