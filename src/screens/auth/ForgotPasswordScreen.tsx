import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Alert,
  TextInput,
  StyleSheet,
  ImageBackground,
  Platform,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { AuthStackParamList } from '@navigation/types';
import NavigationHeader from '@components/common/NavigationHeader';
import Button from '@components/common/Button';
import CommonAlert from '@components/common/CommonAlert';
import PhoneIcon from '@assets/svg/phone.svg';
import { useAuth, useAuthActions } from '@stores/userStore';
import { AuthService } from '@services/authService';

type Props = NativeStackScreenProps<AuthStackParamList, 'ForgotPassword'>;

const ForgotPasswordScreen: React.FC<Props> = ({ navigation }) => {
  const [phone, setPhone] = useState('');
  const [showAlert, setShowAlert] = useState(false);

  // 获取状态和操作方法
  const { isLoading, error } = useAuth();
  const { setLoading, setError, clearError } = useAuthActions();

  // 清除错误信息
  useEffect(() => {
    if (error) {
      Alert.alert('提示', error, [
        { text: '确定', onPress: () => clearError() },
      ]);
    }
  }, [error, clearError]);

  // 验证输入
  const validateInput = (): boolean => {
    if (!phone.trim()) {
      Alert.alert('提示', '请输入手机号');
      return false;
    }

    if (phone.length !== 11) {
      Alert.alert('提示', '请输入正确的手机号');
      return false;
    }

    return true;
  };

  const handleNext = async () => {
    if (!validateInput()) return;

    try {
      setLoading(true);
      clearError();

      // 检查手机号是否已注册
      const isRegistered = await AuthService.checkPhoneRegistered(phone);
      if (!isRegistered) {
        // 显示公共提示框
        setShowAlert(true);
        return;
      }

      // 发送验证码
      const result = await AuthService.sendCode({
        phone,
        type: 'reset',
      });

      if (result && result.success) {
        // 跳转到验证码页面
        navigation.navigate('VerificationCode', {
          phone,
          type: 'reset',
        });
      } else {
        // 显示错误信息
        const errorMessage = result?.message || '发送验证码失败，请重试';
        setError(errorMessage);
      }
    } catch (err: any) {
      setError(err.message || '发送验证码失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ImageBackground
      source={require('@assets/login-bg.png')}
      style={styles.container}
      resizeMode="cover"
    >
      {/* 导航头 */}
      <NavigationHeader
        backgroundColor="transparent"
        borderBottomWidth={0}
        onBackPress={() => navigation.goBack()}
      />

      <View style={styles.content}>
        {/* 手机号输入框 */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>找回密码</Text>
          <View style={styles.inputWrapper}>
            <View style={styles.iconContainer}>
              <PhoneIcon width={14} height={18} fill="#9CA3AF" />
            </View>
            <TextInput
              placeholder="手机号"
              value={phone}
              onChangeText={setPhone}
              keyboardType="phone-pad"
              placeholderTextColor="#9CA3AF"
              style={styles.textInput}
              autoCorrect={false}
              autoCapitalize="none"
              clearButtonMode="while-editing"
            />
          </View>
        </View>

        {/* 下一步按钮 */}
        <Button
          title="下一步"
          onPress={handleNext}
          disabled={!phone.trim()}
          loading={isLoading}
          gradient={true}
        />
      </View>

      {/* 公共提示框 */}
      <CommonAlert
        visible={showAlert}
        onClose={() => setShowAlert(false)}
        title="温馨提示"
        content="该手机号未注册，请先注册账号"
        confirmText="知道了"
        onConfirm={() => setShowAlert(false)}
      />
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 26,
  },
  inputContainer: {
    marginBottom: 115,
  },
  inputLabel: {
    fontSize: 22,
    fontWeight: 600,
    color: '#070D2F',
    marginBottom: 60,
  },
  inputWrapper: {
    backgroundColor: '#ffffff',
    borderRadius: 30,
    paddingHorizontal: 24,
    height: 54,
    shadowRadius: 6,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    marginRight: 12,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#374151',
    height: 54,
    lineHeight: Platform.OS === 'ios' ? 20 : undefined,
    paddingVertical: Platform.OS === 'ios' ? 17 : 0,
    textAlignVertical: Platform.OS === 'android' ? 'center' : 'auto',
    includeFontPadding: false,
  },
});

export default ForgotPasswordScreen;
