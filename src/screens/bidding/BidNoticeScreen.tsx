import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { HomeStackParamList, RootStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';
import SearchBar from '../../components/common/SearchBar';
import Card from '../../components/common/Card';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

type Props = NativeStackScreenProps<HomeStackParamList, 'BidNotice'>;
type RootNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const BidNoticeScreen: React.FC<Props> = () => {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');

  // 使用根导航
  const navigation = useNavigation<RootNavigationProp>();

  // 招标公告数据
  const bidNotices = [
    {
      id: '1',
      title:
        '北京亿达腾飞科技有限公司北京亿达腾飞腾飞科技有限公司腾飞科技有腾飞...',
      type: '中标公告',
      amount: '20000元',
      bidder: '2个联系人',
      company: 'xxx有限责任公司（自然人投资或控股）',
      location: '湖南 长沙',
      date: '10:00',
    },
    {
      id: '2',
      title:
        '北京亿达腾飞科技有限公司北京亿达腾飞腾飞科技有限公司腾飞科技有腾飞...',
      type: '招标公告',
      amount: '20000元',
      company: 'xxx有限责任公司（自然人投资或控股）',
      agent: 'xxx有限责任公司（自然人投资或控股）',
      location: '湖南 长沙',
      date: '2025-05-05',
    },
    {
      id: '3',
      title: '智能医疗设备采购项目',
      type: '招标公告',
      amount: '150000元',
      company: '某医疗科技有限公司',
      location: '北京 海淀',
      date: '2025-05-03',
    },
    {
      id: '4',
      title: '市政道路维修工程',
      type: '中标公告',
      amount: '500000元',
      bidder: '3个联系人',
      company: '某建设工程有限公司',
      location: '上海 浦东',
      date: '2025-05-01',
    },
  ];

  // 筛选类型
  const filterTypes = [
    { id: 'all', name: '全部' },
    { id: 'bidding', name: '招标公告' },
    { id: 'winning', name: '中标公告' },
    { id: 'qualification', name: '资格预审' },
    { id: 'change', name: '变更公告' },
  ];

  const handleSearch = () => {
    navigation.navigate('BidNoticeSearch', { keyword: searchKeyword });
  };

  const handleFilter = () => {
    navigation.navigate('BidNoticeFilter');
  };

  const renderBidNoticeItem = ({ item }) => (
    <TouchableOpacity
      className="bg-white rounded-md p-4 mb-3"
      onPress={() => navigation.navigate('BidDetail', { id: item.id })}
    >
      <Text className="text-base font-medium mb-2" numberOfLines={2}>
        {item.title}
      </Text>
      <View className="flex-row mb-1">
        {item.type === '中标公告' ? (
          <View className="bg-red-100 px-2 py-0.5 rounded mr-2">
            <Text className="text-red-500 text-xs">{item.type}</Text>
          </View>
        ) : (
          <View className="bg-blue-100 px-2 py-0.5 rounded mr-2">
            <Text className="text-blue-500 text-xs">{item.type}</Text>
          </View>
        )}
        <View className="bg-yellow-50 px-2 py-0.5 rounded mr-2">
          <Text className="text-yellow-600 text-xs">行业</Text>
        </View>
        <View className="bg-yellow-50 px-2 py-0.5 rounded mr-2">
          <Text className="text-yellow-600 text-xs">{item.amount}</Text>
        </View>
        {item.bidder && (
          <View className="bg-green-50 px-2 py-0.5 rounded">
            <Text className="text-green-600 text-xs">{item.bidder}</Text>
          </View>
        )}
      </View>
      <Text className="text-gray-500 text-xs mb-1">
        中标单位: {item.company}
      </Text>
      {item.agent && (
        <Text className="text-gray-500 text-xs mb-1">
          代理单位: {item.agent}
        </Text>
      )}
      <View className="flex-row justify-between items-center mt-2">
        <View className="flex-row items-center">
          <Text className="text-blue-500 mr-1">📍</Text>
          <Text className="text-gray-500 text-xs">{item.location}</Text>
        </View>
        <Text className="text-gray-500 text-xs">{item.date}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View className="flex-1 bg-gray-100">
      <Header title="招标公告" showBackButton={true} />

      {/* 搜索栏 */}
      <SearchBar
        value={searchKeyword}
        onChangeText={setSearchKeyword}
        onSearch={handleSearch}
        onClear={() => setSearchKeyword('')}
        placeholder="输入关键词搜索招标公告"
      />

      {/* 筛选条件 */}
      <View className="flex-row bg-white py-2 px-4">
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {filterTypes.map(type => (
            <TouchableOpacity
              key={type.id}
              className={`px-3 py-1 rounded-full mr-2 ${
                activeFilter === type.id ? 'bg-blue-500' : 'bg-gray-100'
              }`}
              onPress={() => setActiveFilter(type.id)}
            >
              <Text
                className={
                  activeFilter === type.id ? 'text-white' : 'text-gray-600'
                }
              >
                {type.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
        <TouchableOpacity onPress={handleFilter}>
          <Text className="text-blue-500">筛选</Text>
        </TouchableOpacity>
      </View>

      {/* 招标公告列表 */}
      <FlatList
        data={bidNotices}
        renderItem={renderBidNoticeItem}
        keyExtractor={item => item.id}
        contentContainerClassName="p-4"
      />
    </View>
  );
};

export default BidNoticeScreen;
