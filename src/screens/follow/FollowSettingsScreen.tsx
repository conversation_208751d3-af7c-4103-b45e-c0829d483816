import React, { useState } from 'react';
import {
  View,
  Text,
  Switch,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { FollowStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';

type Props = NativeStackScreenProps<FollowStackParamList, 'FollowSettings'>;

interface SettingItem {
  id: string;
  title: string;
  description: string;
  enabled: boolean;
}

const FollowSettingsScreen: React.FC<Props> = ({ navigation }) => {
  // 通知设置
  const [notificationSettings, setNotificationSettings] = useState<
    SettingItem[]
  >([
    {
      id: 'company_update',
      title: '企业更新通知',
      description: '关注的企业有新动态时通知我',
      enabled: true,
    },
    {
      id: 'person_update',
      title: '人员更新通知',
      description: '关注的人员有新动态时通知我',
      enabled: true,
    },
    {
      id: 'project_update',
      title: '项目更新通知',
      description: '关注的项目有新进展时通知我',
      enabled: true,
    },
    {
      id: 'performance_update',
      title: '业绩更新通知',
      description: '关注的业绩有新信息时通知我',
      enabled: false,
    },
    {
      id: 'patent_update',
      title: '专利更新通知',
      description: '关注的专利有状态变更时通知我',
      enabled: false,
    },
  ]);

  // 隐私设置
  const [privacySettings, setPrivacySettings] = useState<SettingItem[]>([
    {
      id: 'public_follow',
      title: '公开我的关注',
      description: '允许其他用户查看我关注的内容',
      enabled: false,
    },
    {
      id: 'recommend_similar',
      title: '相似内容推荐',
      description: '根据我的关注推荐相似内容',
      enabled: true,
    },
  ]);

  const handleToggle = (id: string, isNotification: boolean) => {
    if (isNotification) {
      setNotificationSettings(prevSettings =>
        prevSettings.map(item =>
          item.id === id ? { ...item, enabled: !item.enabled } : item,
        ),
      );
    } else {
      setPrivacySettings(prevSettings =>
        prevSettings.map(item =>
          item.id === id ? { ...item, enabled: !item.enabled } : item,
        ),
      );
    }
  };

  const handleClearFollows = () => {
    // 实现清空关注列表的逻辑
    console.log('Clear all follows');
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <Header
        title="关注设置"
        showBackButton
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView className="flex-1">
        {/* 通知设置 */}
        <View className="mt-4">
          <Text className="text-lg font-bold px-4 py-2">通知设置</Text>
          {notificationSettings.map(item => (
            <View
              key={item.id}
              className="flex-row justify-between items-center px-4 py-3 border-b border-gray-100"
            >
              <View className="flex-1 mr-4">
                <Text className="text-base">{item.title}</Text>
                <Text className="text-gray-500 text-sm mt-1">
                  {item.description}
                </Text>
              </View>
              <Switch
                value={item.enabled}
                onValueChange={() => handleToggle(item.id, true)}
                trackColor={{ false: '#D1D5DB', true: '#3B82F6' }}
                thumbColor="#FFFFFF"
              />
            </View>
          ))}
        </View>

        {/* 隐私设置 */}
        <View className="mt-6">
          <Text className="text-lg font-bold px-4 py-2">隐私设置</Text>
          {privacySettings.map(item => (
            <View
              key={item.id}
              className="flex-row justify-between items-center px-4 py-3 border-b border-gray-100"
            >
              <View className="flex-1 mr-4">
                <Text className="text-base">{item.title}</Text>
                <Text className="text-gray-500 text-sm mt-1">
                  {item.description}
                </Text>
              </View>
              <Switch
                value={item.enabled}
                onValueChange={() => handleToggle(item.id, false)}
                trackColor={{ false: '#D1D5DB', true: '#3B82F6' }}
                thumbColor="#FFFFFF"
              />
            </View>
          ))}
        </View>

        {/* 其他操作 */}
        <View className="mt-6 px-4">
          <TouchableOpacity
            className="py-3 border border-red-500 rounded-lg items-center mb-6"
            onPress={handleClearFollows}
          >
            <Text className="text-red-500 font-medium">清空所有关注</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default FollowSettingsScreen;
