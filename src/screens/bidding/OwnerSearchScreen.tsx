import React, { useState } from 'react';
import { View, Text, FlatList, TouchableOpacity } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';
import SearchBar from '../../components/common/SearchBar';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

type Props = NativeStackScreenProps<RootStackParamList, 'OwnerSearch'>;
type RootNavigationProp = NativeStackNavigationProp<RootStackParamList>;

interface Owner {
  id: string;
  name: string;
  type: string;
  location: string;
  projectCount: number;
}

const OwnerSearchScreen: React.FC<Props> = () => {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [searchResults, setSearchResults] = useState<Owner[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 使用根导航
  const navigation = useNavigation<RootNavigationProp>();

  // 模拟业主数据
  const mockOwners: Owner[] = [
    {
      id: '1',
      name: '北京市住房和城乡建设委员会',
      type: '政府机构',
      location: '北京',
      projectCount: 156,
    },
    {
      id: '2',
      name: '上海市城市建设投资开发总公司',
      type: '国有企业',
      location: '上海',
      projectCount: 98,
    },
    {
      id: '3',
      name: '广州市轨道交通集团有限公司',
      type: '国有企业',
      location: '广州',
      projectCount: 45,
    },
    {
      id: '4',
      name: '深圳市住房和建设局',
      type: '政府机构',
      location: '深圳',
      projectCount: 87,
    },
    {
      id: '5',
      name: '中国建筑股份有限公司',
      type: '国有企业',
      location: '北京',
      projectCount: 324,
    },
  ];

  const handleSearch = () => {
    if (searchKeyword.trim()) {
      setIsLoading(true);
      // 模拟网络请求延迟
      setTimeout(() => {
        const filtered = mockOwners.filter(
          owner =>
            owner.name.includes(searchKeyword) ||
            owner.location.includes(searchKeyword),
        );
        setSearchResults(filtered);
        setIsLoading(false);
      }, 500);
    } else {
      setSearchResults([]);
    }
  };

  const renderOwnerItem = ({ item }: { item: Owner }) => (
    <TouchableOpacity
      className="bg-white rounded-md p-4 mb-3"
      onPress={() => {
        // 这里应该跳转到业主详情页，但目前没有这个页面，所以暂时不做跳转
        console.log('查看业主详情:', item.id);
      }}
    >
      <Text className="text-base font-medium mb-1">{item.name}</Text>
      <View className="flex-row items-center mb-1">
        <View className="bg-blue-50 px-2 py-0.5 rounded mr-2">
          <Text className="text-blue-500 text-xs">{item.type}</Text>
        </View>
        <Text className="text-gray-500 text-xs">
          项目数量: {item.projectCount}
        </Text>
      </View>
      <View className="flex-row items-center">
        <Text className="text-blue-500 mr-1">📍</Text>
        <Text className="text-gray-500 text-xs">{item.location}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View className="flex-1 bg-gray-100">
      <Header title="业主搜索" showBackButton={true} />

      {/* 搜索栏 */}
      <SearchBar
        value={searchKeyword}
        onChangeText={setSearchKeyword}
        onSearch={handleSearch}
        onClear={() => setSearchKeyword('')}
        placeholder="输入业主名称、地区等关键词"
        autoFocus={true}
      />

      {/* 筛选条件 */}
      <View className="flex-row justify-between bg-white py-2 px-4">
        <View className="flex-row">
          <TouchableOpacity className="flex-row items-center mr-4">
            <Text className="text-gray-700">地区</Text>
            <Text className="text-gray-400 ml-1">▼</Text>
          </TouchableOpacity>
          <TouchableOpacity className="flex-row items-center mr-4">
            <Text className="text-gray-700">类型</Text>
            <Text className="text-gray-400 ml-1">▼</Text>
          </TouchableOpacity>
          <TouchableOpacity className="flex-row items-center">
            <Text className="text-gray-700">项目数</Text>
            <Text className="text-gray-400 ml-1">▼</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* 搜索结果 */}
      {isLoading ? (
        <View className="flex-1 items-center justify-center">
          <Text className="text-gray-500">搜索中...</Text>
        </View>
      ) : searchResults.length > 0 ? (
        <FlatList
          data={searchResults}
          renderItem={renderOwnerItem}
          keyExtractor={item => item.id}
          contentContainerClassName="p-4"
        />
      ) : searchKeyword ? (
        <View className="flex-1 items-center justify-center">
          <Text className="text-gray-500">未找到相关业主</Text>
        </View>
      ) : (
        <View className="flex-1 p-4">
          <Text className="text-gray-700 font-medium mb-2">热门业主</Text>
          <FlatList
            data={mockOwners}
            renderItem={renderOwnerItem}
            keyExtractor={item => item.id}
          />
        </View>
      )}
    </View>
  );
};

export default OwnerSearchScreen;
