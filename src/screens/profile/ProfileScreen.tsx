import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ProfileStackParamList } from '../../navigation/types';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { navigateToAuth, navigate } from '../../navigation/rootNavigation';

type Props = NativeStackScreenProps<ProfileStackParamList, 'Profile'>;

interface MenuItemProps {
  title: string;
  icon: React.ReactNode;
  onPress: () => void;
  badge?: number;
}

const MenuItem: React.FC<MenuItemProps> = ({ title, icon, onPress, badge }) => {
  return (
    <TouchableOpacity
      className="flex-row items-center py-4 px-4 bg-white"
      onPress={onPress}
    >
      <View className="mr-4">{icon}</View>
      <Text className="flex-1 text-gray-800">{title}</Text>
      {badge !== undefined && (
        <Text className="text-gray-500 mr-2">({badge})</Text>
      )}
      <Icon name="chevron-right" size={20} color="#CCCCCC" />
    </TouchableOpacity>
  );
};

const ProfileScreen: React.FC<Props> = ({ navigation }) => {
  // 模拟登录状态
  const [isLoggedIn, setIsLoggedIn] = useState(true);

  // 模拟用户数据
  const user = {
    name: '耿耿',
    id: 'genggeng8866',
    avatar: require('../../assets/h1.png'),
    orderCount: 12,
    couponCount: 2,
  };

  // 菜单项
  const menuItems = [
    {
      title: '我的订单',
      icon: <Icon name="file-document-outline" size={24} color="#666666" />,
      onPress: () => navigation.navigate('Orders'),
      badge: isLoggedIn ? user.orderCount : undefined,
    },
    {
      title: '我的卡券',
      icon: <Icon name="ticket-outline" size={24} color="#666666" />,
      onPress: () => navigation.navigate('Coupons'),
      badge: isLoggedIn ? user.couponCount : undefined,
    },
    {
      title: 'Tabs组件演示',
      icon: <Icon name="tab" size={24} color="#666666" />,
      onPress: () => navigation.navigate('TabsDemo'),
    },
    {
      title: '地区选择器演示',
      icon: <Icon name="map-marker" size={24} color="#666666" />,
      onPress: () => navigation.navigate('RegionDemo'),
    },
    {
      title: '用户协议',
      icon: <Icon name="file-document-outline" size={24} color="#666666" />,
      onPress: () => navigation.navigate('UserAgreement'),
    },
    {
      title: '免责声明',
      icon: <Icon name="information-outline" size={24} color="#666666" />,
      onPress: () => navigation.navigate('Disclaimer'),
    },
    {
      title: '在线客服',
      icon: <Icon name="headphones" size={24} color="#666666" />,
      onPress: () => navigation.navigate('CustomerService'),
    },
    {
      title: '版本',
      icon: <Icon name="cellphone-information" size={24} color="#666666" />,
      onPress: () => {},
    },
  ];

  // 模拟退出登录
  const handleLogout = () => {
    setIsLoggedIn(false);
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-100">
      <ScrollView>
        {/* 用户信息区域 */}
        <View className="bg-blue-50 px-5 py-6">
          {isLoggedIn ? (
            <View className="flex-row items-center">
              <Image
                source={user.avatar}
                className="w-16 h-16 rounded-full bg-blue-100"
              />
              <View className="ml-4 flex-1">
                <Text className="text-lg font-medium">{user.name}</Text>
                <Text className="text-gray-500 mt-1">{user.id}</Text>
              </View>
              <TouchableOpacity
                className="bg-white rounded-full px-4 py-1"
                onPress={() => navigate('Settings')}
              >
                <Text className="text-gray-600">设置</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View className="flex-row items-center">
              <View className="w-16 h-16 rounded-full bg-blue-100 items-center justify-center">
                <Icon name="account" size={40} color="#6E9FFF" />
              </View>
              <View className="ml-4">
                <Text className="text-lg font-medium">点击授权登录</Text>
              </View>
            </View>
          )}
        </View>

        {/* VIP会员卡 */}
        <View className="mx-4 mt-4 mb-2 rounded-xl overflow-hidden">
          <View className="bg-blue-500 px-5 py-4 flex-row items-center">
            <Text className="text-white text-xl font-bold">VIP会员</Text>
            <Text className="text-white ml-2 opacity-80 flex-1">
              开通后享方会员，享受更多权益
            </Text>
            <TouchableOpacity className="bg-yellow-400 px-3 py-1 rounded-lg">
              <Text className="text-white font-medium">立即开通</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 菜单组 1 */}
        <View className="mx-4 mt-2 rounded-xl overflow-hidden">
          {menuItems.slice(0, 2).map((item, index) => (
            <View key={index}>
              <MenuItem
                title={item.title}
                icon={item.icon}
                onPress={item.onPress}
                badge={item.badge}
              />
              {index < 1 && <View className="h-px bg-gray-100 ml-12" />}
            </View>
          ))}
        </View>

        {/* 菜单组 2 */}
        <View className="mx-4 mt-2 rounded-xl overflow-hidden">
          {menuItems.slice(2, 6).map((item, index) => (
            <View key={index}>
              <MenuItem
                title={item.title}
                icon={item.icon}
                onPress={item.onPress}
                badge={item.badge}
              />
              {index < 3 && <View className="h-px bg-gray-100 ml-12" />}
            </View>
          ))}
        </View>

        {/* 菜单组 3 */}
        <View className="mx-4 mt-2 mb-6 rounded-xl overflow-hidden">
          <MenuItem
            title={menuItems[6].title}
            icon={menuItems[6].icon}
            onPress={menuItems[6].onPress}
            badge={menuItems[6].badge}
          />
        </View>

        {/* 登录/退出登录按钮 */}
        {isLoggedIn ? (
          <TouchableOpacity
            className="mx-4 mb-8 bg-gray-200 py-3 rounded-full items-center"
            onPress={handleLogout}
          >
            <Text className="text-gray-600 text-lg font-medium">退出登录</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            className="mx-4 mb-8 bg-blue-500 py-3 rounded-full items-center"
            onPress={() => navigateToAuth('Login')}
          >
            <Text className="text-white text-lg font-medium">登录</Text>
          </TouchableOpacity>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default ProfileScreen;
