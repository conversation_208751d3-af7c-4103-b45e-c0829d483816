import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ProfileStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';

type Props = NativeStackScreenProps<ProfileStackParamList, 'Feedback'>;

interface FeedbackType {
  id: string;
  name: string;
}

const FeedbackScreen: React.FC<Props> = ({ navigation }) => {
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [content, setContent] = useState('');
  const [contact, setContact] = useState('');

  // 反馈类型选项
  const feedbackTypes: FeedbackType[] = [
    { id: 'bug', name: '功能异常' },
    { id: 'suggestion', name: '功能建议' },
    { id: 'experience', name: '体验问题' },
    { id: 'other', name: '其他' },
  ];

  const handleTypeSelect = (id: string) => {
    setSelectedType(id);
  };

  const handleSubmit = () => {
    // 表单验证
    if (!selectedType) {
      Alert.alert('提示', '请选择反馈类型');
      return;
    }

    if (!content.trim()) {
      Alert.alert('提示', '请输入反馈内容');
      return;
    }

    // 提交反馈
    // 实际项目中需要调用API发送反馈数据
    console.log('提交反馈:', {
      type: selectedType,
      content,
      contact,
    });

    // 提交成功后显示提示并返回
    Alert.alert('提交成功', '感谢您的反馈，我们会尽快处理', [
      { text: '确定', onPress: () => navigation.goBack() },
    ]);
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <Header
        title="意见反馈"
        showBackButton
        onBackPress={() => navigation.goBack()}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView className="flex-1 px-4">
          {/* 反馈类型 */}
          <View className="mt-4">
            <Text className="text-base font-medium mb-2">反馈类型</Text>
            <View className="flex-row flex-wrap">
              {feedbackTypes.map(type => (
                <TouchableOpacity
                  key={type.id}
                  className={`mr-3 mb-3 px-4 py-2 rounded-full ${
                    selectedType === type.id ? 'bg-blue-500' : 'bg-gray-100'
                  }`}
                  onPress={() => handleTypeSelect(type.id)}
                >
                  <Text
                    className={`${
                      selectedType === type.id ? 'text-white' : 'text-gray-700'
                    }`}
                  >
                    {type.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* 反馈内容 */}
          <View className="mt-4">
            <Text className="text-base font-medium mb-2">反馈内容</Text>
            <TextInput
              className="bg-gray-50 rounded-lg p-3 min-h-[120px] text-base"
              placeholder="请详细描述您遇到的问题或建议..."
              placeholderTextColor="#9CA3AF"
              multiline
              value={content}
              onChangeText={setContent}
              textAlignVertical="top"
            />
          </View>

          {/* 联系方式 */}
          <View className="mt-4">
            <Text className="text-base font-medium mb-2">联系方式（选填）</Text>
            <TextInput
              className="bg-gray-50 rounded-lg p-3 text-base"
              placeholder="请留下您的手机号或邮箱，方便我们联系您"
              placeholderTextColor="#9CA3AF"
              value={contact}
              onChangeText={setContact}
            />
          </View>

          <View className="mt-8 mb-6">
            <TouchableOpacity
              className="bg-blue-500 py-3 rounded-lg items-center"
              onPress={handleSubmit}
            >
              <Text className="text-white font-medium text-base">提交反馈</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default FeedbackScreen;
