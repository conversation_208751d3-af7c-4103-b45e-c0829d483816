import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

export interface GridMenuItem {
  id: string;
  name: string;
  icon: React.ReactNode;
  onPress: () => void;
  color?: string;
  badge?: string;
}

interface GridMenuProps {
  data: GridMenuItem[];
  columns?: number;
  showBorder?: boolean;
}

const GridMenu: React.FC<GridMenuProps> = ({
  data,
  columns = 4,
  showBorder = false,
}) => {
  return (
    <View
      className={`flex-row flex-wrap ${
        showBorder ? 'border-b border-gray-100' : ''
      }`}
    >
      {data.map(item => (
        <TouchableOpacity
          key={item.id}
          className={`w-1/${columns} items-center py-4`}
          onPress={item.onPress}
        >
          <View className="relative">
            <View
              className={`w-12 h-12 rounded-full ${
                item.color || 'bg-blue-100'
              } items-center justify-center mb-2 shadow-sm`}
            >
              {item.icon}
            </View>
            {item.badge && (
              <View className="absolute top-0 right-0 bg-red-500 px-1 py-0.5 rounded-md">
                <Text className="text-white text-xs">{item.badge}</Text>
              </View>
            )}
          </View>
          <Text className="text-sm text-gray-800">{item.name}</Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default GridMenu;
