import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';

export interface RegionItem {
  id: string;
  name: string;
  count?: number;
  children?: RegionItem[];
}

interface RegionPickerProps {
  regions: RegionItem[];
  showCheckbox?: boolean;
  selectedRegion?: string;
  selectedRegions?: string[];
  onSelectRegion?: (
    regionId: string,
    regionName: string,
    isLeaf: boolean,
  ) => void;
  onToggleSelect?: (regionId: string, selected: boolean) => void;
}

const RegionPicker: React.FC<RegionPickerProps> = ({
  regions,
  showCheckbox = false,
  selectedRegion,
  selectedRegions = [],
  onSelectRegion,
  onToggleSelect,
}) => {
  const [activeProvince, setActiveProvince] = useState<string | null>(null);

  // 当选中的地区改变时，自动设置活跃的省份
  useEffect(() => {
    if (selectedRegion) {
      // 查找省份ID
      for (const province of regions) {
        if (province.id === selectedRegion) {
          setActiveProvince(province.id);
          return;
        }

        // 如果是城市，找到对应的省份
        if (province.children) {
          const city = province.children.find(
            city => city.id === selectedRegion,
          );
          if (city) {
            setActiveProvince(province.id);
            return;
          }
        }
      }
    }
  }, [selectedRegion, regions]);

  // 获取当前选中省份的城市列表
  const getCities = () => {
    if (!activeProvince) return [];
    const province = regions.find(p => p.id === activeProvince);
    return province?.children || [];
  };

  // 检查省份是否被选中
  const isProvinceSelected = (provinceId: string) => {
    if (!showCheckbox) return false;
    return selectedRegions.includes(provinceId);
  };

  // 检查省份是否部分选中（有部分城市被选中）
  const isProvincePartiallySelected = (provinceId: string) => {
    if (!showCheckbox) return false;

    const province = regions.find(p => p.id === provinceId);
    if (!province || !province.children) return false;

    // 检查是否有城市被选中
    const citiesSelected = province.children.some(city =>
      selectedRegions.includes(city.id),
    );

    // 如果有城市被选中，但省份本身没有被选中，则为部分选中
    return citiesSelected && !selectedRegions.includes(provinceId);
  };

  // 检查城市是否被选中
  const isCitySelected = (cityId: string) => {
    if (!showCheckbox) return false;
    return selectedRegions.includes(cityId);
  };

  // 切换省份选择状态
  const toggleProvinceSelection = (province: RegionItem) => {
    if (!showCheckbox || !onToggleSelect) return;

    const isSelected = isProvinceSelected(province.id);

    // 切换省份选择状态
    onToggleSelect(province.id, !isSelected);

    // 如果省份有城市，同时切换所有城市的选择状态
    if (province.children) {
      province.children.forEach(city => {
        onToggleSelect(city.id, !isSelected);
      });
    }
  };

  // 切换城市选择状态
  const toggleCitySelection = (cityId: string) => {
    if (!showCheckbox || !onToggleSelect) return;

    const isSelected = isCitySelected(cityId);
    onToggleSelect(cityId, !isSelected);

    // 检查当前省份的所有城市是否都被选中
    const province = regions.find(p => p.id === activeProvince);
    if (province) {
      const allCities = province.children || [];
      const cityIds = allCities.map(city => city.id);

      // 如果切换后所有城市都被选中，也选中省份
      if (!isSelected) {
        const willAllBeSelected = cityIds.every(
          id => id === cityId || selectedRegions.includes(id),
        );

        if (willAllBeSelected && !selectedRegions.includes(province.id)) {
          onToggleSelect(province.id, true);
        }
      }
      // 如果取消选择某个城市，也取消省份的选择
      else if (selectedRegions.includes(province.id)) {
        onToggleSelect(province.id, false);
      }
    }
  };

  // 选择省份或城市（单选模式）
  const handleSelectRegion = (
    regionId: string,
    regionName: string,
    isLeaf: boolean,
  ) => {
    if (!showCheckbox && onSelectRegion) {
      onSelectRegion(regionId, regionName, isLeaf);
    }
  };

  // 渲染复选框
  const renderCheckbox = (
    isSelected: boolean,
    isPartiallySelected: boolean = false,
  ) => {
    if (isSelected) {
      return (
        <View style={styles.checkboxSelected}>
          <View style={styles.checkboxInner} />
        </View>
      );
    } else if (isPartiallySelected) {
      return (
        <View style={styles.checkboxPartial}>
          <View style={styles.checkboxPartialInner} />
        </View>
      );
    } else {
      return <View style={styles.checkbox} />;
    }
  };

  return (
    <View style={styles.container}>
      {/* 省份列表 */}
      <View style={styles.provinceList}>
        <ScrollView showsVerticalScrollIndicator={false}>
          {regions.map(province => (
            <TouchableOpacity
              key={province.id}
              style={[
                styles.provinceItem,
                activeProvince === province.id && styles.activeProvinceItem,
              ]}
              onPress={() => {
                setActiveProvince(province.id);
                if (!showCheckbox) {
                  handleSelectRegion(province.id, province.name, false);
                }
              }}
            >
              <Text
                style={[
                  styles.provinceName,
                  activeProvince === province.id && styles.activeProvinceName,
                ]}
                numberOfLines={1}
              >
                {province.name}
                {province.count !== undefined && (
                  <Text style={styles.count}> ({province.count})</Text>
                )}
              </Text>

              {showCheckbox && (
                <TouchableOpacity
                  style={styles.checkboxContainer}
                  onPress={() => toggleProvinceSelection(province)}
                >
                  {renderCheckbox(
                    isProvinceSelected(province.id),
                    isProvincePartiallySelected(province.id),
                  )}
                </TouchableOpacity>
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* 城市列表 */}
      <View style={styles.cityList}>
        <ScrollView showsVerticalScrollIndicator={false}>
          {getCities().map(city => (
            <TouchableOpacity
              key={city.id}
              style={styles.cityItem}
              onPress={() => {
                if (!showCheckbox) {
                  handleSelectRegion(city.id, city.name, true);
                }
              }}
            >
              <Text
                style={[
                  styles.cityName,
                  selectedRegion === city.id && styles.activeCityName,
                  isCitySelected(city.id) && styles.selectedCityName,
                ]}
                numberOfLines={1}
              >
                {city.name}
                {city.count !== undefined && (
                  <Text style={styles.count}> ({city.count})</Text>
                )}
              </Text>

              {showCheckbox && (
                <TouchableOpacity
                  style={styles.checkboxContainer}
                  onPress={() => toggleCitySelection(city.id)}
                >
                  {renderCheckbox(isCitySelected(city.id))}
                </TouchableOpacity>
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    height: 300,
  },
  provinceList: {
    flex: 1,
    borderRightWidth: 1,
    borderRightColor: '#EEEEEE',
    backgroundColor: '#F8F9FA',
  },
  provinceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    paddingHorizontal: 12,
  },
  activeProvinceItem: {
    backgroundColor: '#FFFFFF',
  },
  provinceName: {
    fontSize: 14,
    color: '#333333',
    flex: 1,
  },
  activeProvinceName: {
    color: '#4B74FF',
    fontWeight: '500',
  },
  cityList: {
    flex: 1.5,
    backgroundColor: '#FFFFFF',
  },
  cityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    paddingHorizontal: 12,
  },
  cityName: {
    fontSize: 14,
    color: '#333333',
    flex: 1,
  },
  activeCityName: {
    color: '#4B74FF',
    fontWeight: '500',
  },
  selectedCityName: {
    color: '#4B74FF',
    fontWeight: '500',
  },
  count: {
    fontSize: 12,
    color: '#999999',
  },
  checkboxContainer: {
    marginLeft: 8,
    padding: 4,
  },
  checkbox: {
    width: 18,
    height: 18,
    borderWidth: 1,
    borderColor: '#CCCCCC',
    borderRadius: 2,
    backgroundColor: '#FFFFFF',
  },
  checkboxSelected: {
    width: 18,
    height: 18,
    borderWidth: 1,
    borderColor: '#4B74FF',
    borderRadius: 2,
    backgroundColor: '#4B74FF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxInner: {
    width: 10,
    height: 5,
    borderLeftWidth: 2,
    borderBottomWidth: 2,
    borderColor: '#FFFFFF',
    transform: [{ rotate: '-45deg' }],
    marginTop: -2,
  },
  checkboxPartial: {
    width: 18,
    height: 18,
    borderWidth: 1,
    borderColor: '#4B74FF',
    borderRadius: 2,
    backgroundColor: '#4B74FF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxPartialInner: {
    width: 10,
    height: 2,
    backgroundColor: '#FFFFFF',
  },
});

export default RegionPicker;
