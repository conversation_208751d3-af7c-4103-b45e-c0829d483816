import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Easing,
  useWindowDimensions,
  ScrollView,
  TextInput,
} from 'react-native';

export interface FilterOption {
  id: string;
  name: string;
  selected?: boolean;
  children?: FilterOption[];
  count?: number;
}

export interface FilterGroup {
  id: string;
  title: string;
  options: FilterOption[];
  multiSelect?: boolean;
  showAsCheckbox?: boolean;
  showTwoColumns?: boolean;
  showDateRange?: boolean;
}

export interface FilterMenuOption {
  id: string;
  title: string;
}

interface FilterDropdownProps {
  filterMenuOptions: FilterMenuOption[];
  getFilterGroupsByTab: (filterId: string) => FilterGroup[];
  onApply: (selectedOptions: { [key: string]: FilterOption[] }) => void;
  onReset: () => void;
  currentLocation?: string; // 添加当前位置属性，用于显示当前筛选位置
}

const FilterDropdown: React.FC<FilterDropdownProps> = ({
  filterMenuOptions,
  getFilterGroupsByTab,
  onApply,
  onReset,
  currentLocation,
}) => {
  const filterTabRef = useRef<View>(null);
  const contentRef = useRef<View>(null);
  const { height: windowHeight } = useWindowDimensions();
  const [dropdownHeight, setDropdownHeight] = useState(0);
  const [dropdownTop, setDropdownTop] = useState(0);
  const [contentHeight, setContentHeight] = useState(0);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [activeFilterId, setActiveFilterId] = useState<string>('');
  const [selectedOptions, setSelectedOptions] = useState<{
    [key: string]: FilterOption[];
  }>({});
  const [dateRange, setDateRange] = useState<{
    startDate: string;
    endDate: string;
  }>({ startDate: '', endDate: '' });

  // 使用单个动画值控制所有动画
  const animatedValue = useRef(new Animated.Value(0)).current;

  // 切换下拉菜单显示状态
  const toggleDropdown = (filterId: string) => {
    const newState = !isDropdownOpen || activeFilterId !== filterId;

    // 使用单个动画值，0表示关闭状态，1表示打开状态
    Animated.timing(animatedValue, {
      toValue: newState ? 1 : 0,
      duration: 300, // 动画持续时间
      useNativeDriver: true,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1), // 添加缓动函数
    }).start(() => {
      // 动画完成后更新状态
      if (!newState) {
        setIsDropdownOpen(false);
        setActiveFilterId('');
      } else {
        setIsDropdownOpen(true);
        setActiveFilterId(filterId);
      }
    });

    // 如果是打开状态，立即更新状态以显示内容
    if (newState) {
      setIsDropdownOpen(true);
      setActiveFilterId(filterId);
    }
  };

  // 使用插值创建背景透明度动画
  const backdropOpacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  // 使用插值创建Y轴平移动画
  const translateY = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [-contentHeight, 0], // 使用实际测量的内容高度
  });

  useEffect(() => {
    if (filterTabRef.current) {
      filterTabRef.current.measure((x, y, width, height, pageX, pageY) => {
        setDropdownHeight(windowHeight - height - pageY);
        setDropdownTop(height);
      });
    }
  }, [windowHeight]);

  // 测量内容高度
  const onContentLayout = (event: {
    nativeEvent: { layout: { height: number } };
  }) => {
    const { height } = event.nativeEvent.layout;
    if (height > 0 && height !== contentHeight) {
      setContentHeight(height);
    }
  };

  // 处理选项选择
  const handleOptionSelect = (groupId: string, option: FilterOption) => {
    const currentGroup = getFilterGroupsByTab(activeFilterId).find(
      g => g.id === groupId,
    );

    if (!currentGroup) return;

    // 深拷贝当前选中的选项
    const newSelectedOptions = { ...selectedOptions };

    if (!newSelectedOptions[groupId]) {
      newSelectedOptions[groupId] = [];
    }

    // 如果是地区筛选，省份为单选，区域为多选
    if (activeFilterId === 'area') {
      // 清除之前选择的所有省份
      newSelectedOptions[groupId] = newSelectedOptions[groupId].filter(
        item => !item.children || item.id === option.id,
      );

      // 如果已经选中了这个省份，则取消选择
      if (isOptionSelected(groupId, option.id)) {
        newSelectedOptions[groupId] = [];
      } else {
        // 添加新选择的省份，并且自动选中所有区域
        const selectedOption = { ...option, selected: true };

        // 如果有子选项（区域），则全部选中
        if (option.children && option.children.length > 0) {
          selectedOption.children = option.children.map(child => ({
            ...child,
            selected: true,
          }));
        }

        newSelectedOptions[groupId] = [selectedOption];
      }
    } else if (!currentGroup.multiSelect) {
      // 其他筛选项的单选模式
      newSelectedOptions[groupId] = [{ ...option, selected: true }];
    } else {
      // 其他筛选项的多选模式
      const existingIndex = newSelectedOptions[groupId].findIndex(
        item => item.id === option.id,
      );

      if (existingIndex >= 0) {
        // 已存在，则移除
        newSelectedOptions[groupId] = newSelectedOptions[groupId].filter(
          item => item.id !== option.id,
        );
      } else {
        // 不存在，则添加
        newSelectedOptions[groupId] = [
          ...newSelectedOptions[groupId],
          { ...option, selected: true },
        ];
      }
    }

    setSelectedOptions(newSelectedOptions);
  };

  // 处理子选项选择
  const handleChildOptionSelect = (
    groupId: string,
    parentId: string,
    childOption: FilterOption,
  ) => {
    const currentGroup = getFilterGroupsByTab(activeFilterId).find(
      g => g.id === groupId,
    );

    if (!currentGroup) return;

    // 深拷贝当前选中的选项
    const newSelectedOptions = { ...selectedOptions };

    if (!newSelectedOptions[groupId]) {
      newSelectedOptions[groupId] = [];
    }

    // 查找父选项
    const parentOptionIndex = newSelectedOptions[groupId].findIndex(
      item => item.id === parentId,
    );

    // 如果是地区筛选，区域为多选
    if (activeFilterId === 'area') {
      if (parentOptionIndex === -1) {
        // 父选项不存在，这种情况不应该发生，因为省份是单选的
        return;
      }

      const parentOption = newSelectedOptions[groupId][parentOptionIndex];

      // 确保children数组存在
      if (!parentOption.children) {
        parentOption.children = [];
      }

      // 检查子选项是否已存在
      const childIndex = parentOption.children.findIndex(
        c => c.id === childOption.id,
      );

      if (childIndex >= 0) {
        // 子选项已存在，移除它
        parentOption.children = parentOption.children.filter(
          c => c.id !== childOption.id,
        );
      } else {
        // 子选项不存在，添加它
        parentOption.children.push({ ...childOption, selected: true });
      }

      // 如果没有选中任何区域，也移除省份
      if (parentOption.children.length === 0) {
        newSelectedOptions[groupId] = [];
      }
    } else {
      // 其他筛选项的子选项处理
      // 创建带有子选项的父选项
      const parentOption = getFilterGroupsByTab(activeFilterId)
        .find(g => g.id === groupId)
        ?.options.find(o => o.id === parentId);

      if (!parentOption) return;

      // 如果父选项不存在，添加父选项
      if (parentOptionIndex === -1) {
        // 添加父选项和子选项
        newSelectedOptions[groupId].push({
          ...parentOption,
          selected: true,
          children: [{ ...childOption, selected: true }],
        });
      } else {
        // 父选项存在
        const existingParent = newSelectedOptions[groupId][parentOptionIndex];

        // 确保children数组存在
        if (!existingParent.children) {
          existingParent.children = [];
        }

        // 检查子选项是否已存在
        const childIndex = existingParent.children.findIndex(
          c => c.id === childOption.id,
        );

        if (childIndex >= 0) {
          // 子选项已存在，移除它
          existingParent.children = existingParent.children.filter(
            c => c.id !== childOption.id,
          );

          // 如果没有子选项了，也移除父选项
          if (existingParent.children.length === 0) {
            newSelectedOptions[groupId] = newSelectedOptions[groupId].filter(
              item => item.id !== parentId,
            );
          }
        } else {
          // 子选项不存在，添加它
          existingParent.children.push({ ...childOption, selected: true });
        }
      }
    }

    setSelectedOptions(newSelectedOptions);
  };

  // 检查选项是否被选中
  const isOptionSelected = (groupId: string, optionId: string): boolean => {
    return (
      selectedOptions[groupId]?.some(item => item.id === optionId) || false
    );
  };

  // 检查子选项是否被选中
  const isChildOptionSelected = (
    groupId: string,
    parentId: string,
    childId: string,
  ): boolean => {
    const parent = selectedOptions[groupId]?.find(item => item.id === parentId);
    return parent?.children?.some(child => child.id === childId) || false;
  };

  // 处理应用筛选
  const handleApplyFilter = () => {
    onApply(selectedOptions);

    // 先执行动画，再更新状态
    Animated.timing(animatedValue, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    }).start(() => {
      setIsDropdownOpen(false);
      setActiveFilterId('');
    });
  };

  // 处理重置筛选
  const handleResetFilter = () => {
    setSelectedOptions({});
    setDateRange({ startDate: '', endDate: '' });
    onReset();
  };

  // 处理日期范围变更
  const handleDateRangeChange = (type: 'start' | 'end', value: string) => {
    if (type === 'start') {
      setDateRange({ ...dateRange, startDate: value });
    } else {
      setDateRange({ ...dateRange, endDate: value });
    }
  };

  // 渲染地区筛选内容
  const renderAreaFilterContent = (filterGroup: FilterGroup) => {
    return (
      <View style={styles.filterContent}>
        <ScrollView style={styles.filterScrollView}>
          {filterGroup.showTwoColumns ? (
            <View style={styles.twoColumnsContainer}>
              <View style={styles.leftColumn}>
                {filterGroup.options.map(option => {
                  // 判断该省份是否被选中且所有区域都被选中
                  const isProvinceSelected = isOptionSelected(
                    filterGroup.id,
                    option.id,
                  );
                  const isAllSelected =
                    isProvinceSelected &&
                    option.children &&
                    option.children.length > 0 &&
                    selectedOptions[filterGroup.id]?.[0]?.children?.length ===
                      option.children.length;

                  return (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.areaOption,
                        isProvinceSelected && styles.selectedAreaOption,
                      ]}
                      onPress={() => handleOptionSelect(filterGroup.id, option)}
                    >
                      <View style={styles.areaOptionInner}>
                        <Text
                          style={[
                            styles.areaOptionText,
                            isProvinceSelected && styles.selectedAreaOptionText,
                          ]}
                        >
                          {option.name}
                        </Text>
                        {isProvinceSelected && (
                          <View style={styles.minusContainer}>
                            <Text style={styles.minusIcon}>
                              {isAllSelected ? '✓' : '-'}
                            </Text>
                          </View>
                        )}
                      </View>
                    </TouchableOpacity>
                  );
                })}
              </View>
              <View style={styles.rightColumn}>
                {filterGroup.options
                  .find(o => isOptionSelected(filterGroup.id, o.id))
                  ?.children?.map(child => (
                    <TouchableOpacity
                      key={child.id}
                      style={styles.areaChildOption}
                      onPress={() =>
                        handleChildOptionSelect(
                          filterGroup.id,
                          filterGroup.options.find(o =>
                            isOptionSelected(filterGroup.id, o.id),
                          )?.id || '',
                          child,
                        )
                      }
                    >
                      <View style={styles.areaChildOptionInner}>
                        <View
                          style={[
                            styles.checkbox,
                            isChildOptionSelected(
                              filterGroup.id,
                              filterGroup.options.find(o =>
                                isOptionSelected(filterGroup.id, o.id),
                              )?.id || '',
                              child.id,
                            ) && styles.checkboxSelected,
                          ]}
                        >
                          {isChildOptionSelected(
                            filterGroup.id,
                            filterGroup.options.find(o =>
                              isOptionSelected(filterGroup.id, o.id),
                            )?.id || '',
                            child.id,
                          ) && <Text style={styles.checkboxMark}>✓</Text>}
                        </View>
                        <Text
                          style={[
                            styles.areaChildOptionText,
                            isChildOptionSelected(
                              filterGroup.id,
                              filterGroup.options.find(o =>
                                isOptionSelected(filterGroup.id, o.id),
                              )?.id || '',
                              child.id,
                            ) && styles.selectedAreaChildOptionText,
                          ]}
                        >
                          {child.name}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  ))}
              </View>
            </View>
          ) : (
            filterGroup.options.map(option => (
              <TouchableOpacity
                key={option.id}
                style={styles.option}
                onPress={() => handleOptionSelect(filterGroup.id, option)}
              >
                <View style={styles.checkboxContainer}>
                  <View
                    style={[
                      styles.checkbox,
                      isOptionSelected(filterGroup.id, option.id) &&
                        styles.checkboxSelected,
                    ]}
                  >
                    {isOptionSelected(filterGroup.id, option.id) && (
                      <Text style={styles.checkboxMark}>✓</Text>
                    )}
                  </View>
                  <Text style={styles.optionText}>{option.name}</Text>
                </View>
              </TouchableOpacity>
            ))
          )}
        </ScrollView>
      </View>
    );
  };

  // 渲染行业筛选内容
  const renderIndustryFilterContent = (filterGroup: FilterGroup) => {
    return (
      <View style={styles.filterContent}>
        <ScrollView style={styles.filterScrollView}>
          {filterGroup.options.map(option => (
            <View key={option.id}>
              <TouchableOpacity
                style={styles.industryOption}
                onPress={() => handleOptionSelect(filterGroup.id, option)}
              >
                <View style={styles.checkboxContainer}>
                  <View
                    style={[
                      styles.checkbox,
                      isOptionSelected(filterGroup.id, option.id) &&
                        styles.checkboxSelected,
                    ]}
                  >
                    {isOptionSelected(filterGroup.id, option.id) && (
                      <Text style={styles.checkboxMark}>✓</Text>
                    )}
                  </View>
                  <Text style={styles.optionText}>{option.name}</Text>
                  {option.children && option.children.length > 0 && (
                    <Text style={styles.expandIcon}>
                      {isOptionSelected(filterGroup.id, option.id) ? '▼' : '▶'}
                    </Text>
                  )}
                </View>
              </TouchableOpacity>

              {isOptionSelected(filterGroup.id, option.id) &&
                option.children && (
                  <View style={styles.childrenContainer}>
                    {option.children.map(child => (
                      <TouchableOpacity
                        key={child.id}
                        style={styles.childOption}
                        onPress={() =>
                          handleChildOptionSelect(
                            filterGroup.id,
                            option.id,
                            child,
                          )
                        }
                      >
                        <View style={styles.checkboxContainer}>
                          <View
                            style={[
                              styles.childCheckbox,
                              isChildOptionSelected(
                                filterGroup.id,
                                option.id,
                                child.id,
                              ) && styles.childCheckboxSelected,
                            ]}
                          >
                            {isChildOptionSelected(
                              filterGroup.id,
                              option.id,
                              child.id,
                            ) && <Text style={styles.checkboxMark}>✓</Text>}
                          </View>
                          <Text style={styles.childOptionText}>
                            {child.name}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    ))}
                  </View>
                )}
            </View>
          ))}
        </ScrollView>
      </View>
    );
  };

  // 渲染更多筛选内容
  const renderMoreFilterContent = (filterGroups: FilterGroup[]) => {
    return (
      <View style={styles.filterContent}>
        <ScrollView style={styles.filterScrollView}>
          {filterGroups.map(group => (
            <View key={group.id} style={styles.filterGroup}>
              <Text style={styles.filterGroupTitle}>{group.title}</Text>

              {group.showDateRange ? (
                <View style={styles.dateRangeContainer}>
                  <View style={styles.dateInputContainer}>
                    <TextInput
                      style={styles.dateInput}
                      placeholder="请输入日期"
                      value={dateRange.startDate}
                      onChangeText={text =>
                        handleDateRangeChange('start', text)
                      }
                      keyboardType="numeric"
                    />
                  </View>
                  <Text style={styles.dateRangeSeparator}>至</Text>
                  <View style={styles.dateInputContainer}>
                    <TextInput
                      style={styles.dateInput}
                      placeholder="请输入日期"
                      value={dateRange.endDate}
                      onChangeText={text => handleDateRangeChange('end', text)}
                      keyboardType="numeric"
                    />
                  </View>
                  <Text style={styles.yearText}>年</Text>
                </View>
              ) : (
                <View style={styles.tagContainer}>
                  {group.options.map(option => (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.tagOption,
                        isOptionSelected(group.id, option.id) &&
                          styles.tagOptionSelected,
                      ]}
                      onPress={() => handleOptionSelect(group.id, option)}
                    >
                      <Text
                        style={[
                          styles.tagOptionText,
                          isOptionSelected(group.id, option.id) &&
                            styles.tagOptionTextSelected,
                        ]}
                      >
                        {option.name}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          ))}
        </ScrollView>
      </View>
    );
  };

  // 渲染排序筛选内容
  const renderSortFilterContent = (filterGroup: FilterGroup) => {
    return (
      <View style={styles.filterContent}>
        <ScrollView style={styles.filterScrollView}>
          {filterGroup.options.map(option => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.sortOption,
                isOptionSelected(filterGroup.id, option.id) &&
                  styles.sortOptionSelected,
              ]}
              onPress={() => handleOptionSelect(filterGroup.id, option)}
            >
              <Text
                style={[
                  styles.sortOptionText,
                  isOptionSelected(filterGroup.id, option.id) &&
                    styles.sortOptionTextSelected,
                ]}
              >
                {option.name}
              </Text>
              {isOptionSelected(filterGroup.id, option.id) && (
                <Text style={styles.sortCheckMark}>✓</Text>
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  // 根据活动的筛选ID渲染对应的筛选内容
  const renderFilterContent = () => {
    if (!activeFilterId) return null;

    const filterGroups = getFilterGroupsByTab(activeFilterId);

    switch (activeFilterId) {
      case 'area':
        return renderAreaFilterContent(filterGroups[0]);
      case 'industry':
        return renderIndustryFilterContent(filterGroups[0]);
      case 'more':
        return renderMoreFilterContent(filterGroups);
      case 'sort':
        return renderSortFilterContent(filterGroups[0]);
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* 筛选选项卡 */}
      <View style={styles.filterTab} ref={filterTabRef}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {currentLocation && (
            <View style={styles.currentLocationContainer}>
              <Text style={styles.currentLocationText}>{currentLocation}</Text>
            </View>
          )}
          {filterMenuOptions.map(option => (
            <TouchableOpacity
              key={option.id}
              style={styles.filterOption}
              onPress={() => toggleDropdown(option.id)}
            >
              <Text
                style={[
                  styles.filterText,
                  activeFilterId === option.id && styles.activeFilterText,
                ]}
              >
                {option.title}
              </Text>
              <Text
                style={[
                  styles.filterArrow,
                  activeFilterId === option.id && styles.activeFilterArrow,
                ]}
              >
                ▼
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {isDropdownOpen && (
        <Animated.View
          style={[
            styles.backdrop,
            {
              height: dropdownHeight,
              top: dropdownTop,
              opacity: backdropOpacity,
            },
          ]}
          onTouchEnd={() => isDropdownOpen && toggleDropdown(activeFilterId)}
        />
      )}

      {/* 下拉菜单容器 */}
      <View
        style={[
          styles.dropdown,
          { height: contentHeight },
          { pointerEvents: isDropdownOpen ? 'auto' : 'none' },
        ]}
      >
        {/* 动画内容 */}
        <Animated.View
          ref={contentRef}
          style={[
            styles.dropdownContent,
            { transform: [{ translateY: translateY }] },
          ]}
          onLayout={onContentLayout}
        >
          <View style={styles.contentInner}>
            {renderFilterContent()}

            {/* 底部按钮 */}
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={styles.resetButton}
                onPress={handleResetFilter}
              >
                <Text style={styles.resetButtonText}>重置</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.applyButton}
                onPress={handleApplyFilter}
              >
                <Text style={styles.applyButtonText}>
                  确定（已选{Object.values(selectedOptions).flat().length}个）
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    height: 40,
    zIndex: 100,
  },
  filterTab: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    display: 'flex',
    flexDirection: 'row',
    height: 40,
    zIndex: 11,
    backgroundColor: 'white',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    zIndex: 10,
  },
  dropdown: {
    position: 'absolute',
    top: 40,
    left: 0,
    right: 0,
    zIndex: 10,
    overflow: 'hidden',
  },
  dropdownContent: {
    backgroundColor: 'white',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  contentInner: {
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
  },
  filterContent: {
    maxHeight: 400,
  },
  filterScrollView: {
    maxHeight: 350,
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  resetButton: {
    flex: 1,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    marginRight: 8,
  },
  resetButtonText: {
    color: '#333',
  },
  applyButton: {
    flex: 2,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#4169E1',
    borderRadius: 4,
  },
  applyButtonText: {
    color: 'white',
  },
  option: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: '#4169E1',
    borderColor: '#4169E1',
  },
  checkboxMark: {
    color: 'white',
    fontSize: 12,
  },
  optionText: {
    fontSize: 14,
    color: '#333',
  },
  // 地区筛选样式
  twoColumnsContainer: {
    flexDirection: 'row',
    height: 350,
  },
  leftColumn: {
    width: '40%',
    backgroundColor: '#f5f5f5',
    borderRightWidth: 1,
    borderRightColor: '#e0e0e0',
  },
  rightColumn: {
    width: '60%',
    backgroundColor: 'white',
  },
  areaOption: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  areaOptionInner: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectedAreaOption: {
    backgroundColor: 'white',
    borderLeftWidth: 3,
    borderLeftColor: '#4169E1',
  },
  areaOptionText: {
    color: '#333',
    flex: 1,
    fontSize: 14,
  },
  selectedAreaOptionText: {
    color: '#4169E1',
    fontWeight: 'bold',
  },
  minusContainer: {
    width: 20,
    height: 20,
    borderRadius: 2,
    backgroundColor: '#4169E1',
    justifyContent: 'center',
    alignItems: 'center',
  },
  minusIcon: {
    color: 'white',
    fontSize: 16,
    lineHeight: 20,
    textAlign: 'center',
  },
  areaChildOption: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  areaChildOptionInner: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedAreaChildOption: {
    backgroundColor: '#f0f0ff',
  },
  areaChildOptionText: {
    color: '#333',
    flex: 1,
    marginLeft: 10,
  },
  selectedAreaChildOptionText: {
    color: '#4169E1',
  },
  // 行业筛选样式
  industryOption: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  expandIcon: {
    marginLeft: 'auto',
    fontSize: 12,
    color: '#999',
  },
  childrenContainer: {
    backgroundColor: '#f9f9f9',
  },
  childOption: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    paddingLeft: 44,
  },
  childCheckbox: {
    width: 18,
    height: 18,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  childCheckboxSelected: {
    backgroundColor: '#4169E1',
    borderColor: '#4169E1',
  },
  childOptionText: {
    fontSize: 13,
    color: '#666',
  },
  // 更多筛选样式
  filterGroup: {
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  filterGroupTitle: {
    fontSize: 15,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tagOption: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
    marginRight: 8,
    marginBottom: 8,
  },
  tagOptionSelected: {
    backgroundColor: '#e6f0ff',
    borderColor: '#4169E1',
    borderWidth: 1,
  },
  tagOptionText: {
    color: '#333',
  },
  tagOptionTextSelected: {
    color: '#4169E1',
  },
  // 日期范围样式
  dateRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  dateInputContainer: {
    flex: 2,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    height: 40,
  },
  dateInput: {
    height: 40,
    paddingHorizontal: 12,
  },
  dateRangeSeparator: {
    paddingHorizontal: 8,
  },
  yearText: {
    paddingLeft: 8,
  },
  // 排序筛选样式
  sortOption: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  sortOptionSelected: {
    backgroundColor: '#f0f0ff',
  },
  sortOptionText: {
    fontSize: 14,
    color: '#333',
  },
  sortOptionTextSelected: {
    color: '#4169E1',
  },
  sortCheckMark: {
    color: '#4169E1',
    fontWeight: 'bold',
  },
  checkContainer: {
    backgroundColor: '#4169E1',
  },
  // New styles for current location
  currentLocationContainer: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
    marginRight: 8,
  },
  currentLocationText: {
    fontSize: 12,
    color: '#555',
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  filterText: {
    fontSize: 14,
    color: '#333',
  },
  activeFilterText: {
    fontWeight: 'bold',
    color: '#4169E1',
  },
  filterArrow: {
    marginLeft: 4,
    fontSize: 12,
    color: '#999',
  },
  activeFilterArrow: {
    color: '#4169E1',
  },
});

export default FilterDropdown;
