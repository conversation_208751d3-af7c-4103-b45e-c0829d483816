import { FilterGroup } from '../components/common/FilterDropdown';

// 地区筛选选项
export const areaFilterOptions: FilterGroup = {
  id: 'area',
  title: '',
  showAsCheckbox: true,
  showTwoColumns: true,
  multiSelect: true,
  options: [
    {
      id: 'hebei',
      name: '河北省',
      children: [
        { id: 'shunyi', name: '顺义区' },
        { id: 'daxing', name: '大兴区' },
      ],
    },
    {
      id: 'henan',
      name: '河南省',
      children: [
        { id: 'quanbu', name: '全部' },
        { id: 'chaoyang', name: '朝阳区' },
      ],
    },
    {
      id: 'beijing',
      name: '北京市(3)',
      selected: true,
      children: [
        { id: 'xicheng', name: '西城区', selected: true },
        { id: 'changping', name: '昌平区', selected: true },
        { id: 'fangshan', name: '房山区', selected: true },
      ],
    },
    {
      id: 'tianjin',
      name: '天津市',
      children: [
        { id: 'heping', name: '和平区' },
        { id: 'huaxin', name: '怀柔区' },
      ],
    },
    {
      id: 'shanxi',
      name: '山西省',
      children: [],
    },
    {
      id: 'shaanxi',
      name: '陕西省',
      children: [{ id: 'haiyan', name: '海淀区' }],
    },
    {
      id: 'hunan',
      name: '湖南省',
      children: [{ id: 'fengtai', name: '丰台区' }],
    },
  ],
};

// 行业筛选选项
export const industryFilterOptions: FilterGroup = {
  id: 'industry',
  title: '',
  showAsCheckbox: true,
  multiSelect: true,
  options: [
    { id: 'agriculture', name: '农、林、牧、渔业' },
    { id: 'mining', name: '采矿业' },
    {
      id: 'manufacturing',
      name: '制造业(3)',
      selected: true,
      children: [
        { id: 'food_processing', name: '农副产品加工业' },
        { id: 'convenience_food', name: '方便食品制造' },
        { id: 'biscuit', name: '饼干及方便面' },
        { id: 'pastry', name: '蛋糕、面包糕点' },
        { id: 'convenience_food2', name: '方便食品制造' },
        { id: 'tobacco', name: '烟草行业制造', selected: true },
      ],
    },
    { id: 'agriculture2', name: '农、林、牧、渔业' },
    { id: 'mining2', name: '采矿业' },
    { id: 'agriculture3', name: '农、林、牧、渔业' },
  ],
};

// 更多筛选选项
export const moreFilterOptions: FilterGroup = {
  id: 'more',
  title: '筛选项名称',
  options: [
    { id: 'item1', name: '筛选项' },
    { id: 'item2', name: '筛选项' },
    { id: 'item3', name: '筛选项' },
    { id: 'item4', name: '筛选项' },
    { id: 'item5', name: '筛选项' },
    { id: 'item6', name: '筛选项' },
    { id: 'item7', name: '筛选项' },
  ],
  multiSelect: true,
};

// 日期范围筛选
export const dateRangeFilterOptions: FilterGroup = {
  id: 'date_range',
  title: '筛选项名称',
  showDateRange: true,
  options: [],
  multiSelect: false,
};

// 排序选项
export const sortFilterOptions: FilterGroup = {
  id: 'sort',
  title: '智能排序',
  options: [
    { id: 'intelligent', name: '智能排序', selected: true },
    { id: 'score_desc', name: '综合评分由高到低' },
    { id: 'capital_desc', name: '注册资本由高到低' },
    { id: 'date_asc', name: '成立日期从早到晚' },
    { id: 'date_desc', name: '成立日期从晚到早' },
  ],
  multiSelect: false,
};

// 根据不同选项卡获取筛选组
export const getFilterGroupsByTab = (tabId: string): FilterGroup[] => {
  switch (tabId) {
    case 'company':
      return [areaFilterOptions];
    case 'industry':
      return [industryFilterOptions];
    case 'more':
      return [moreFilterOptions, dateRangeFilterOptions];
    case 'sort':
      return [sortFilterOptions];
    default:
      return [areaFilterOptions];
  }
};
