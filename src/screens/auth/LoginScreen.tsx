import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  ImageBackground,
  Alert,
  StyleSheet,
  TextInput,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { AuthStackParamList } from '@navigation/types';
import * as RootNavigation from '@navigation/rootNavigation';
import { useAuth, useAuthActions } from '@stores/userStore';
import { AuthService } from '@services/authService';
import { Storage, StorageKeys } from '@utils/storage';
import NavigationHeader from '@components/common/NavigationHeader';
import { Tabs, TabContent } from '@components/tabs';
import type { TabItem } from '@components/tabs';
import Button from '@components/common/Button';

type Props = NativeStackScreenProps<AuthStackParamList, 'Login'>;

// 定义tabs配置
const loginTabs: TabItem[] = [
  { id: 'code', title: '验证码登录' },
  { id: 'password', title: '密码登录' },
];

const LoginScreen: React.FC<Props> = ({ navigation, route }) => {
  const initialTab = route.params?.initialTab || 'code';
  const [loginType, setLoginType] = useState<'code' | 'password'>(initialTab);
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [agreed, setAgreed] = useState(false);

  // 获取状态和操作方法
  const { isLoading, error } = useAuth();
  const { login, setLoading, setError, clearError } = useAuthActions();

  // 当路由参数变化时更新登录类型
  useEffect(() => {
    if (route.params?.initialTab) {
      setLoginType(route.params.initialTab);
    }
  }, [route.params]);

  // 初始化时加载上次登录的手机号
  useEffect(() => {
    const lastPhone = Storage.getString(StorageKeys.LAST_LOGIN_PHONE);
    if (lastPhone) {
      setPhone(lastPhone);
    }
  }, []);

  // 清除错误信息
  useEffect(() => {
    if (error) {
      Alert.alert('提示', error, [
        { text: '确定', onPress: () => clearError() },
      ]);
    }
  }, [error, clearError]);

  // 验证输入
  const validateInput = (): boolean => {
    if (!agreed) {
      Alert.alert('提示', '请先同意用户协议');
      return false;
    }

    if (!phone.trim()) {
      Alert.alert('提示', '请输入手机号');
      return false;
    }

    if (phone.length !== 11) {
      Alert.alert('提示', '请输入正确的手机号');
      return false;
    }

    if (loginType === 'password' && !password.trim()) {
      Alert.alert('提示', '请输入密码');
      return false;
    }

    return true;
  };

  // 密码登录
  const handlePasswordLogin = async () => {
    if (!validateInput()) return;

    try {
      setLoading(true);
      clearError();

      const result = await AuthService.loginWithPassword(phone, password);

      // 登录成功，更新状态
      login(result.userInfo, result.token);

      // 跳转到主页
      RootNavigation.resetToMain();
    } catch (error: any) {
      setError(error.message || '登录失败');
    } finally {
      setLoading(false);
    }
  };

  // 发送验证码
  const handleSendCode = async () => {
    if (!validateInput()) return;

    try {
      setLoading(true);
      clearError();

      const result = await AuthService.sendCode({
        phone,
        type: 'login',
      });

      if (result && result.success) {
        // 跳转到验证码页面
        navigation.navigate('VerificationCode', {
          phone,
          type: 'login',
        });
      } else {
        // 显示错误信息
        const errorMessage = result?.message || '发送验证码失败，请重试';
        setError(errorMessage);
      }
    } catch (error: any) {
      setError(error.message || '发送验证码失败');
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = () => {
    if (loginType === 'code') {
      handleSendCode();
    } else {
      handlePasswordLogin();
    }
  };

  return (
    <ImageBackground
      source={require('@assets/login-bg.png')}
      style={styles.backgroundImage}
      resizeMode="cover"
    >
      <View style={styles.safeArea}>
        {/* 导航头 */}
        <NavigationHeader
          showBackButton={true}
          onBackPress={() => navigation.goBack()}
          backgroundColor="transparent"
          borderBottomWidth={0}
          backButtonColor="#374151"
        />

        <View style={styles.container}>
          {/* Logo */}
          <View style={styles.logoContainer}>
            <Image
              source={require('@assets/logo.png')}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>

          {/* 登录方式切换 */}
          <View style={styles.tabsContainer}>
            <Tabs
              tabs={loginTabs}
              activeTab={loginType}
              onTabPress={id => setLoginType(id as 'code' | 'password')}
              style={{
                container: styles.tabsStyle,
                tab: styles.tabStyle,
                activeTab: styles.activeTabStyle,
                tabText: styles.tabTextStyle,
                activeTabText: styles.activeTabTextStyle,
                underline: styles.underlineStyle,
              }}
            />
          </View>

          {/* 验证码登录内容 */}
          <TabContent activeTab={loginType} tabId="code">
            <View style={styles.formContainer}>
              <View style={styles.inputContainer}>
                <View style={styles.inputWrapper}>
                  <TextInput
                    placeholder="手机号"
                    value={phone}
                    onChangeText={setPhone}
                    keyboardType="phone-pad"
                    style={styles.textInput}
                    placeholderTextColor="#9CA3AF"
                  />
                  {phone ? (
                    <TouchableOpacity
                      onPress={() => setPhone('')}
                      style={styles.clearButton}
                    >
                      <Image
                        source={require('@assets/clean.png')}
                        style={styles.cleanIcon}
                        resizeMode="contain"
                      />
                    </TouchableOpacity>
                  ) : null}
                </View>
                <View style={styles.tipContainer}>
                  <Text style={styles.tipText}>
                    未注册的手机号验证通过后将自动注册
                  </Text>
                </View>
              </View>
            </View>
          </TabContent>

          {/* 密码登录内容 */}
          <TabContent activeTab={loginType} tabId="password">
            <View style={styles.formContainer}>
              <View style={styles.inputContainer}>
                <View style={styles.inputWrapper}>
                  <TextInput
                    placeholder="手机号"
                    value={phone}
                    onChangeText={setPhone}
                    keyboardType="phone-pad"
                    style={styles.textInput}
                    placeholderTextColor="#9CA3AF"
                  />
                  {phone ? (
                    <TouchableOpacity
                      onPress={() => setPhone('')}
                      style={styles.clearButton}
                    >
                      <Image
                        source={require('@assets/clean.png')}
                        style={styles.cleanIcon}
                        resizeMode="contain"
                      />
                    </TouchableOpacity>
                  ) : null}
                </View>
              </View>

              <View style={styles.inputContainer}>
                <View style={styles.inputWrapper}>
                  <TextInput
                    placeholder="密码"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry
                    style={styles.textInput}
                    placeholderTextColor="#9CA3AF"
                  />
                  {password ? (
                    <TouchableOpacity
                      onPress={() => setPassword('')}
                      style={styles.clearButton}
                    >
                      <Image
                        source={require('@assets/clean.png')}
                        style={styles.cleanIcon}
                        resizeMode="contain"
                      />
                    </TouchableOpacity>
                  ) : null}
                  <TouchableOpacity
                    style={styles.forgotPasswordButton}
                    onPress={() => navigation.navigate('ForgotPassword')}
                  >
                    <Text style={styles.forgotPasswordText}>找回密码</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </TabContent>

          {/* 登录按钮 */}
          <Button
            title={loginType === 'code' ? '一键登录' : '登录'}
            onPress={handleLogin}
            disabled={isLoading}
            loading={isLoading}
            gradient={true}
            gradientColors={['#819EFF', '#4B74FF']}
            disabledGradientColors={['#9CA3AF', '#9CA3AF']}
            height={50}
            borderRadius={25}
            style={styles.loginButton}
          />

          {/* 注册按钮 */}
          <Button
            title="注册"
            onPress={() => navigation.navigate('Register')}
            type="text"
            style={styles.registerButton}
          />

          {/* 弹性空间，将协议推到底部 */}
          <View style={styles.flexSpacer} />

          {/* 用户协议 */}
          <View style={styles.agreementContainer}>
            <TouchableOpacity
              onPress={() => setAgreed(!agreed)}
              style={styles.checkboxContainer}
            >
              {agreed ? (
                <Image
                  source={require('@assets/checked.png')}
                  style={styles.checkboxImage}
                  resizeMode="contain"
                />
              ) : (
                <View style={styles.uncheckedBox} />
              )}
            </TouchableOpacity>
            <Text style={styles.agreementText}>
              已阅读并同意 <Text style={styles.linkText}>《用户协议》</Text> 和{' '}
              <Text style={styles.linkText}>《免责声明》</Text>
            </Text>
          </View>
        </View>
      </View>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingHorizontal: 16,
    marginTop: 24,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  logo: {
    width: 76,
    height: 76,
  },
  tabsContainer: {
    marginBottom: 24,
    paddingHorizontal: 40,
  },
  tabsStyle: {
    backgroundColor: 'transparent',
  },
  tabStyle: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  activeTabStyle: {},
  tabTextStyle: {
    fontSize: 18,
    color: '#070D2F',
  },
  activeTabTextStyle: {
    fontSize: 18,
    color: '#070D2F',
    fontWeight: '600',
  },
  underlineStyle: {
    backgroundColor: '#3B82F6',
  },
  formContainer: {},
  inputContainer: {
    marginBottom: 20,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 54,
    borderRadius: 27,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#374151',
    paddingVertical: 12,
    lineHeight: 20,
  },
  clearButton: {
    padding: 4,
    marginLeft: 8,
  },
  tipContainer: {
    height: 94,
    paddingHorizontal: 16,
  },
  tipText: {
    fontSize: 12,
    color: '#7C8294',
    paddingTop: 20,
  },
  forgotPasswordButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginLeft: 8,
  },
  forgotPasswordText: {
    color: '#3B82F6',
    fontSize: 14,
    fontWeight: '500',
  },
  loginButton: {
    marginBottom: 16,
  },
  registerButton: {
    marginBottom: 16,
  },
  flexSpacer: {
    flex: 1,
  },
  agreementContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingBottom: 60,
  },
  checkboxContainer: {
    marginRight: 8,
    marginTop: 2,
  },
  checkboxImage: {
    width: 16,
    height: 16,
  },
  uncheckedBox: {
    width: 16,
    height: 16,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    backgroundColor: 'transparent',
  },
  agreementText: {
    color: '#6B7280',
    fontSize: 12,
  },
  linkText: {
    color: '#3B82F6',
  },
  cleanIcon: {
    width: 20,
    height: 20,
    opacity: 0.4,
    tintColor: '#9CA3AF',
  },
});

export default LoginScreen;
