import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { DetailStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';
import Card from '../../components/common/Card';

type Props = NativeStackScreenProps<DetailStackParamList, 'ProductDetail'>;

interface ProductInfo {
  id: string;
  name: string;
  company: {
    id: string;
    name: string;
  };
  description: string;
  features: string[];
  specifications: { key: string; value: string }[];
  certifications: { name: string; date: string }[];
  relatedProducts: { id: string; name: string }[];
  contactInfo: {
    phone: string;
    email: string;
    website: string;
  };
}

const ProductDetailScreen: React.FC<Props> = ({ navigation, route }) => {
  const { id } = route.params;
  const [isFollowed, setIsFollowed] = useState(false);

  // 模拟产品详情数据
  const productInfo: ProductInfo = {
    id,
    name: '某智能建筑系统',
    company: {
      id: '123',
      name: '某建筑集团有限公司',
    },
    description:
      '某智能建筑系统是一款结合了物联网技术、人工智能和建筑工程的综合解决方案，旨在提高建筑的智能化程度、能源效率和用户体验。该系统可以实现建筑环境的智能监控、能源管理、安全管理和设备控制等功能。',
    features: [
      '智能环境监控：实时监测建筑内的温度、湿度、空气质量等环境参数',
      '能源管理：智能调节建筑用电、用水、暖通系统，提高能源利用效率',
      '安全管理：智能门禁、监控、报警系统，保障建筑安全',
      '智能控制：通过手机APP或语音远程控制建筑内的各种设备',
      '数据分析：收集并分析建筑运行数据，提供优化建议',
    ],
    specifications: [
      { key: '适用建筑类型', value: '商业写字楼、住宅、酒店、医院等' },
      {
        key: '系统组成',
        value: '中央控制系统、传感器网络、控制执行设备、用户界面',
      },
      { key: '通信协议', value: 'WiFi、ZigBee、蓝牙、以太网' },
      { key: '控制方式', value: '手机APP、网页端、语音控制、触摸屏控制面板' },
      { key: '安装要求', value: '需专业工程师进行安装和调试' },
    ],
    certifications: [
      { name: '国家智能建筑产品认证', date: '2022-04-18' },
      { name: '能源之星认证', date: '2022-02-25' },
      { name: 'ISO9001质量管理体系认证', date: '2021-11-10' },
    ],
    relatedProducts: [
      { id: '102', name: '某环保建材' },
      { id: '103', name: '某建筑设计解决方案' },
    ],
    contactInfo: {
      phone: '************',
      email: '<EMAIL>',
      website: 'www.example.com/product',
    },
  };

  const handleFollow = () => {
    setIsFollowed(!isFollowed);
    // 实现关注/取消关注逻辑
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <Header
        title="产品详情"
        showBackButton
        onBackPress={() => navigation.goBack()}
        rightComponent={
          <TouchableOpacity onPress={handleFollow}>
            <Text className={isFollowed ? 'text-red-500' : 'text-blue-500'}>
              {isFollowed ? '已关注' : '关注'}
            </Text>
          </TouchableOpacity>
        }
      />

      <ScrollView className="flex-1">
        {/* 产品基本信息 */}
        <View className="bg-gray-50 p-4">
          <Text className="text-xl font-bold">{productInfo.name}</Text>
          <TouchableOpacity
            onPress={() =>
              navigation.navigate('CompanyDetail', {
                id: productInfo.company.id,
              })
            }
          >
            <Text className="text-blue-500 text-sm mt-1">
              {productInfo.company.name}
            </Text>
          </TouchableOpacity>
        </View>

        <View className="p-4">
          {/* 产品描述 */}
          <Card className="mb-4">
            <Text className="font-bold mb-2">产品描述</Text>
            <Text className="text-gray-700 text-sm">
              {productInfo.description}
            </Text>
          </Card>

          {/* 产品特点 */}
          <Card className="mb-4">
            <Text className="font-bold mb-2">产品特点</Text>
            {productInfo.features.map((feature, index) => (
              <View key={index} className="flex-row mb-2">
                <Text className="text-blue-500 mr-2">•</Text>
                <Text className="text-gray-700 text-sm flex-1">{feature}</Text>
              </View>
            ))}
          </Card>

          {/* 产品规格 */}
          <Card className="mb-4">
            <Text className="font-bold mb-2">产品规格</Text>
            {productInfo.specifications.map((spec, index) => (
              <View key={index} className="flex-row mb-2">
                <Text className="text-gray-500 w-28 text-sm">{spec.key}:</Text>
                <Text className="text-gray-700 flex-1 text-sm">
                  {spec.value}
                </Text>
              </View>
            ))}
          </Card>

          {/* 产品认证 */}
          <Card className="mb-4">
            <Text className="font-bold mb-2">产品认证</Text>
            {productInfo.certifications.map((cert, index) => (
              <View key={index} className="flex-row mb-2">
                <Text className="text-gray-700 text-sm">{cert.name}</Text>
                <Text className="text-gray-500 text-sm ml-2">
                  ({cert.date})
                </Text>
              </View>
            ))}
          </Card>

          {/* 相关产品 */}
          <Card className="mb-4">
            <Text className="font-bold mb-2">相关产品</Text>
            {productInfo.relatedProducts.map(product => (
              <TouchableOpacity
                key={product.id}
                className="py-2 border-b border-gray-100"
                onPress={() =>
                  navigation.navigate('ProductDetail', { id: product.id })
                }
              >
                <Text className="text-blue-500">{product.name}</Text>
              </TouchableOpacity>
            ))}
          </Card>

          {/* 联系方式 */}
          <Card className="mb-4">
            <Text className="font-bold mb-2">联系方式</Text>
            <View className="flex-row mb-2">
              <Text className="text-gray-500 w-16 text-sm">电话:</Text>
              <Text className="text-gray-700 flex-1 text-sm">
                {productInfo.contactInfo.phone}
              </Text>
            </View>
            <View className="flex-row mb-2">
              <Text className="text-gray-500 w-16 text-sm">邮箱:</Text>
              <Text className="text-gray-700 flex-1 text-sm">
                {productInfo.contactInfo.email}
              </Text>
            </View>
            <View className="flex-row mb-2">
              <Text className="text-gray-500 w-16 text-sm">网站:</Text>
              <Text className="text-gray-700 flex-1 text-sm">
                {productInfo.contactInfo.website}
              </Text>
            </View>
          </Card>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ProductDetailScreen;
