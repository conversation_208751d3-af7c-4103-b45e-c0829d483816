import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  FlatList,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ProfileStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';
import Card from '../../components/common/Card';

type Props = NativeStackScreenProps<ProfileStackParamList, 'Help'>;

interface HelpCategory {
  id: string;
  title: string;
  icon: string;
}

interface HelpItem {
  id: string;
  title: string;
  content: string;
  categoryId: string;
}

const HelpScreen: React.FC<Props> = ({ navigation }) => {
  const [selectedCategory, setSelectedCategory] = useState('account');
  const [expandedItemId, setExpandedItemId] = useState<string | null>(null);

  // 帮助分类
  const categories: HelpCategory[] = [
    { id: 'account', title: '账号相关', icon: '👤' },
    { id: 'search', title: '搜索查询', icon: '🔍' },
    { id: 'follow', title: '关注功能', icon: '⭐' },
    { id: 'data', title: '数据问题', icon: '📊' },
    { id: 'other', title: '其他问题', icon: '❓' },
  ];

  // 帮助问题
  const helpItems: HelpItem[] = [
    {
      id: '1',
      title: '如何修改个人资料？',
      content:
        '您可以在"我的 > 个人资料"页面点击编辑按钮，修改您的个人信息，包括头像、姓名、职位等。完成编辑后，点击保存即可。',
      categoryId: 'account',
    },
    {
      id: '2',
      title: '如何修改登录密码？',
      content:
        '您可以在"我的 > 设置 > 账号安全"中找到"修改密码"选项，按照提示输入旧密码和新密码后提交即可完成密码修改。',
      categoryId: 'account',
    },
    {
      id: '3',
      title: '如何进行高级搜索？',
      content:
        '在搜索页面，输入关键词后点击搜索，在搜索结果页面可以看到右上角的"筛选"按钮，点击后可以设置更多的筛选条件，如地区、类别、时间范围等。',
      categoryId: 'search',
    },
    {
      id: '4',
      title: '如何保存搜索条件？',
      content:
        '在搜索结果页面，您可以点击右上角的"保存"按钮，将当前的搜索条件保存为常用搜索，下次可以在搜索历史中直接点击使用。',
      categoryId: 'search',
    },
    {
      id: '5',
      title: '如何设置关注提醒？',
      content:
        '您可以在"关注 > 设置"中找到通知设置选项，可以针对不同类型的关注内容（企业、人员、项目等）单独设置是否接收更新提醒。',
      categoryId: 'follow',
    },
    {
      id: '6',
      title: '关注的企业有更新，为什么没收到通知？',
      content:
        '请检查以下几点：1. 是否在"关注 > 设置"中开启了对应类型的通知；2. 是否在手机系统设置中允许本应用发送通知；3. 是否处于网络异常状态。如果以上都正常，可能是系统延迟，请稍后再检查。',
      categoryId: 'follow',
    },
    {
      id: '7',
      title: '为什么某些企业数据不完整？',
      content:
        '我们的数据来源于公开渠道和企业自主填报，部分企业信息可能因为更新不及时或企业未提供而不完整。您可以通过反馈功能向我们报告数据问题，我们会尽快核实并更新。',
      categoryId: 'data',
    },
    {
      id: '8',
      title: '如何导出数据？',
      content:
        '企业会员可以在查看详情页面时，点击右上角的"更多"按钮，选择"导出"功能将当前页面数据导出为Excel或PDF格式。如果您没有看到导出选项，可能是您的账号权限不足，请升级为企业会员。',
      categoryId: 'data',
    },
    {
      id: '9',
      title: '遇到bug怎么办？',
      content:
        '如果您在使用过程中遇到任何功能异常或错误，请通过"我的 > 意见反馈"功能向我们报告，请尽可能详细地描述问题发生的场景和步骤，我们会尽快修复。',
      categoryId: 'other',
    },
    {
      id: '10',
      title: '如何联系客服？',
      content:
        '您可以通过以下方式联系我们的客服团队：1. 在app内的"我的 > 意见反馈"提交问题；2. 发送邮件至*******************；3. 工作日9:00-18:00拨打客服热线：400-123-4567。',
      categoryId: 'other',
    },
  ];

  // 根据选中的分类筛选帮助项
  const filteredHelpItems = helpItems.filter(
    item => item.categoryId === selectedCategory,
  );

  const handleCategoryPress = (categoryId: string) => {
    setSelectedCategory(categoryId);
    setExpandedItemId(null); // 切换分类时收起所有展开项
  };

  const handleItemPress = (itemId: string) => {
    setExpandedItemId(expandedItemId === itemId ? null : itemId);
  };

  const renderHelpItem = ({ item }: { item: HelpItem }) => (
    <TouchableOpacity onPress={() => handleItemPress(item.id)} className="mb-3">
      <Card className="p-4">
        <View className="flex-row justify-between items-center">
          <Text className="text-base font-medium flex-1 pr-2">
            {item.title}
          </Text>
          <Text className="text-gray-400 text-lg">
            {expandedItemId === item.id ? '−' : '+'}
          </Text>
        </View>

        {expandedItemId === item.id && (
          <Text className="text-gray-600 mt-3 leading-5">{item.content}</Text>
        )}
      </Card>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-white">
      <Header
        title="帮助中心"
        showBackButton
        onBackPress={() => navigation.goBack()}
      />

      <View className="px-4 py-3">
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          className="mb-4"
        >
          {categories.map(category => (
            <TouchableOpacity
              key={category.id}
              onPress={() => handleCategoryPress(category.id)}
              className={`px-4 py-2 mr-2 rounded-full ${
                selectedCategory === category.id ? 'bg-blue-500' : 'bg-gray-100'
              }`}
            >
              <Text
                className={
                  selectedCategory === category.id
                    ? 'text-white'
                    : 'text-gray-700'
                }
              >
                {category.icon} {category.title}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        <FlatList
          data={filteredHelpItems}
          renderItem={renderHelpItem}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={() => (
            <View className="items-center justify-center py-10">
              <Text className="text-gray-400">暂无相关帮助内容</Text>
            </View>
          )}
        />
      </View>

      <View className="p-4 mt-auto">
        <Text className="text-center text-gray-500 mb-2">
          如果以上内容未能解决您的问题
        </Text>
        <TouchableOpacity
          className="bg-blue-500 py-3 rounded-lg items-center"
          onPress={() => navigation.navigate('Feedback')}
        >
          <Text className="text-white font-medium">联系客服</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default HelpScreen;
