import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Image,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ProfileStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';
import Card from '../../components/common/Card';

type Props = NativeStackScreenProps<ProfileStackParamList, 'Profile'>;

interface MenuItemProps {
  title: string;
  icon: string;
  badge?: number;
  onPress: () => void;
}

const MenuItem: React.FC<MenuItemProps> = ({ title, icon, badge, onPress }) => {
  return (
    <TouchableOpacity
      className="flex-row items-center py-3 px-4 border-b border-gray-100"
      onPress={onPress}
    >
      <Text className="mr-3 text-xl">{icon}</Text>
      <Text className="flex-1 text-gray-800">{title}</Text>
      {badge !== undefined && badge > 0 && (
        <View className="bg-red-500 rounded-full px-2 py-0.5 mr-2">
          <Text className="text-white text-xs">
            {badge > 99 ? '99+' : badge}
          </Text>
        </View>
      )}
      <Text className="text-gray-400">〉</Text>
    </TouchableOpacity>
  );
};

const ProfileHomeScreen: React.FC<Props> = ({ navigation }) => {
  // 模拟用户数据
  const user = {
    name: '张三',
    avatar: require('../../assets/h1.png'),
    phone: '138****1234',
    level: 'VIP会员',
    expiryDate: '2023-12-31',
    coupons: 5,
    follows: 28,
    potentialCustomers: 120,
    notifications: 3,
  };

  const menuSections = [
    {
      title: '我的服务',
      items: [
        {
          title: '我的订单',
          icon: '📋',
          onPress: () => navigation.navigate('Orders'),
        },
        {
          title: '优惠券',
          icon: '🎟️',
          badge: user.coupons,
          onPress: () => navigation.navigate('Coupons'),
        },
        {
          title: '潜在客户包',
          icon: '👥',
          onPress: () => navigation.navigate('PotentialCustomerList'),
        },
        {
          title: '消息通知',
          icon: '📮',
          badge: user.notifications,
          onPress: () => navigation.navigate('Notifications'),
        },
      ],
    },
    {
      title: '其他服务',
      items: [
        {
          title: '客服咨询',
          icon: '💬',
          onPress: () => navigation.navigate('CustomerService'),
        },
        {
          title: '意见反馈',
          icon: '📝',
          onPress: () => navigation.navigate('Feedback'),
        },
        {
          title: '设置',
          icon: '⚙️',
          onPress: () => navigation.navigate('Settings'),
        },
      ],
    },
  ];

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <Header title="我的" />

      <ScrollView>
        {/* 用户信息卡片 */}
        <View className="bg-white p-4 mb-2">
          <View className="flex-row items-center">
            <Image
              source={user.avatar}
              className="w-16 h-16 rounded-full bg-gray-200"
            />
            <View className="ml-4 flex-1">
              <View className="flex-row items-center">
                <Text className="text-lg font-bold">{user.name}</Text>
                <View className="ml-2 bg-blue-50 px-2 py-0.5 rounded">
                  <Text className="text-blue-500 text-xs">{user.level}</Text>
                </View>
              </View>
              <Text className="text-gray-500 mt-1">{user.phone}</Text>
              <Text className="text-gray-500 text-xs mt-1">
                有效期至: {user.expiryDate}
              </Text>
            </View>

            <TouchableOpacity
              className="bg-blue-50 px-3 py-1 rounded"
              onPress={() => navigation.navigate('EditProfile')}
            >
              <Text className="text-blue-500">编辑</Text>
            </TouchableOpacity>
          </View>

          <View className="flex-row justify-around mt-6 pt-4 border-t border-gray-100">
            <TouchableOpacity
              className="items-center"
              onPress={() => navigation.navigate('Follows')}
            >
              <Text className="text-lg font-bold">{user.follows}</Text>
              <Text className="text-gray-500 text-sm">我的关注</Text>
            </TouchableOpacity>

            <View className="h-12 border-l border-gray-100" />

            <TouchableOpacity
              className="items-center"
              onPress={() => navigation.navigate('PotentialCustomerList')}
            >
              <Text className="text-lg font-bold">
                {user.potentialCustomers}
              </Text>
              <Text className="text-gray-500 text-sm">潜在客户</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 会员专享服务 */}
        <Card className="mx-4 mb-4 p-3">
          <View className="flex-row justify-between items-center mb-3">
            <Text className="font-bold">会员专享服务</Text>
            <TouchableOpacity>
              <Text className="text-blue-500 text-sm">查看更多 〉</Text>
            </TouchableOpacity>
          </View>

          <View className="flex-row justify-around">
            <TouchableOpacity className="items-center">
              <View className="w-12 h-12 bg-blue-50 rounded-full items-center justify-center mb-1">
                <Text className="text-2xl">📊</Text>
              </View>
              <Text className="text-xs">行业报告</Text>
            </TouchableOpacity>

            <TouchableOpacity className="items-center">
              <View className="w-12 h-12 bg-blue-50 rounded-full items-center justify-center mb-1">
                <Text className="text-2xl">🏆</Text>
              </View>
              <Text className="text-xs">排行榜</Text>
            </TouchableOpacity>

            <TouchableOpacity className="items-center">
              <View className="w-12 h-12 bg-blue-50 rounded-full items-center justify-center mb-1">
                <Text className="text-2xl">📂</Text>
              </View>
              <Text className="text-xs">数据下载</Text>
            </TouchableOpacity>

            <TouchableOpacity className="items-center">
              <View className="w-12 h-12 bg-blue-50 rounded-full items-center justify-center mb-1">
                <Text className="text-2xl">📱</Text>
              </View>
              <Text className="text-xs">专属客服</Text>
            </TouchableOpacity>
          </View>
        </Card>

        {/* 菜单项 */}
        {menuSections.map((section, index) => (
          <View key={index} className="bg-white mb-2">
            <View className="px-4 py-2 bg-gray-50">
              <Text className="text-gray-500">{section.title}</Text>
            </View>

            {section.items.map((item, itemIndex) => (
              <MenuItem
                key={itemIndex}
                title={item.title}
                icon={item.icon}
                badge={item.badge}
                onPress={item.onPress}
              />
            ))}
          </View>
        ))}

        {/* 退出登录 */}
        <TouchableOpacity
          className="bg-white my-2 py-3 items-center"
          onPress={() => {
            // 实现退出登录逻辑
          }}
        >
          <Text className="text-red-500">退出登录</Text>
        </TouchableOpacity>

        <View className="p-4 items-center">
          <Text className="text-gray-400 text-xs">版本 1.0.0</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ProfileHomeScreen;
