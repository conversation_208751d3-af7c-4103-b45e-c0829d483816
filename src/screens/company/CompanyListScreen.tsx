import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { GraphStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';
import SearchBar from '../../components/common/SearchBar';
import Card from '../../components/common/Card';

type Props = NativeStackScreenProps<GraphStackParamList, 'CompanyList'>;

interface Company {
  id: string;
  name: string;
  industry: string;
  location: string;
  size: string;
  founded: string;
  description: string;
  tags: string[];
}

const CompanyListScreen: React.FC<Props> = ({ navigation, route }) => {
  const { category, title } = route.params || {
    category: 'all',
    title: '企业列表',
  };
  const [searchText, setSearchText] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  // 模拟企业数据
  const companies: Company[] = [
    {
      id: '1',
      name: '某建筑集团有限公司',
      industry: '建筑/工程',
      location: '北京市',
      size: '1000-5000人',
      founded: '2005年',
      description: '国内领先的建筑企业，专注于各类工程建设，拥有多项特级资质',
      tags: ['特级资质', '建筑工程', '市政工程'],
    },
    {
      id: '2',
      name: '某建筑设计院有限公司',
      industry: '建筑/设计',
      location: '上海市',
      size: '500-1000人',
      founded: '2008年',
      description: '专业建筑设计机构，提供建筑、规划、景观等全方位设计服务',
      tags: ['甲级设计', '建筑设计', '城市规划'],
    },
    {
      id: '3',
      name: '某市政工程有限公司',
      industry: '市政/基建',
      location: '广州市',
      size: '1000-5000人',
      founded: '2003年',
      description: '专注市政基础设施建设的综合性企业，在全国多地有项目',
      tags: ['一级资质', '市政工程', '道路桥梁'],
    },
    {
      id: '4',
      name: '某路桥建设有限公司',
      industry: '交通/建设',
      location: '深圳市',
      size: '1000-5000人',
      founded: '2007年',
      description: '专业从事公路、桥梁、隧道等交通基础设施建设的企业',
      tags: ['特级资质', '路桥工程', '隧道工程'],
    },
    {
      id: '5',
      name: '某水利水电工程有限公司',
      industry: '水利/电力',
      location: '武汉市',
      size: '500-1000人',
      founded: '2010年',
      description: '专业从事水利水电工程建设的企业，拥有多项专业资质',
      tags: ['一级资质', '水利水电', '大型项目'],
    },
    {
      id: '6',
      name: '某装饰工程有限公司',
      industry: '装饰/装修',
      location: '杭州市',
      size: '100-500人',
      founded: '2012年',
      description: '提供高端建筑装饰和室内设计服务的专业企业',
      tags: ['一级资质', '建筑装饰', '室内设计'],
    },
    {
      id: '7',
      name: '某建材有限公司',
      industry: '建材/制造',
      location: '成都市',
      size: '100-500人',
      founded: '2009年',
      description: '专业生产建筑材料的企业，产品广泛应用于各类工程项目',
      tags: ['建筑材料', '环保产品', '创新技术'],
    },
    {
      id: '8',
      name: '某工程咨询有限公司',
      industry: '咨询/服务',
      location: '南京市',
      size: '100-500人',
      founded: '2011年',
      description: '提供工程咨询、项目管理、造价咨询等专业服务的企业',
      tags: ['工程咨询', '项目管理', '造价咨询'],
    },
  ];

  // 根据搜索文本和分类筛选企业
  const filteredCompanies = companies.filter(company => {
    const matchesSearch =
      searchText === '' ||
      company.name.includes(searchText) ||
      company.description.includes(searchText) ||
      company.tags.some(tag => tag.includes(searchText));

    const matchesCategory =
      category === 'all' ||
      (category === 'construction' && company.industry.includes('建筑')) ||
      (category === 'design' && company.industry.includes('设计')) ||
      (category === 'municipal' && company.industry.includes('市政')) ||
      (category === 'materials' && company.industry.includes('建材'));

    return matchesSearch && matchesCategory;
  });

  const handleSearch = () => {
    // 实现搜索逻辑
  };

  const handleCompanyPress = (id: string) => {
    navigation.navigate('Detail', { screen: 'CompanyDetail', params: { id } });
  };

  const handleRefresh = () => {
    setRefreshing(true);
    // 模拟刷新数据
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const renderCompanyItem = ({ item }: { item: Company }) => (
    <TouchableOpacity onPress={() => handleCompanyPress(item.id)}>
      <Card className="mb-3 p-4">
        <View className="flex-row justify-between items-start">
          <View className="flex-1">
            <Text className="text-lg font-bold">{item.name}</Text>
            <Text className="text-gray-500 mt-1">
              {item.industry} | {item.location}
            </Text>
            <Text className="text-gray-500 text-sm">
              {item.size} | 成立于{item.founded}
            </Text>
          </View>
          <TouchableOpacity className="bg-blue-50 px-2 py-1 rounded">
            <Text className="text-blue-500 text-xs">关注</Text>
          </TouchableOpacity>
        </View>

        <Text className="text-gray-600 text-sm mt-2" numberOfLines={2}>
          {item.description}
        </Text>

        <View className="flex-row flex-wrap mt-2">
          {item.tags.map((tag, index) => (
            <View
              key={index}
              className="bg-gray-100 rounded-full px-2 py-0.5 mr-1 mb-1"
            >
              <Text className="text-gray-600 text-xs">{tag}</Text>
            </View>
          ))}
        </View>
      </Card>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-white">
      <Header
        title={title}
        showBackButton
        onBackPress={() => navigation.goBack()}
      />

      {/* 搜索栏 */}
      <SearchBar
        value={searchText}
        onChangeText={setSearchText}
        onSearch={handleSearch}
        onClear={() => setSearchText('')}
      />

      {/* 筛选条件 */}
      <View className="flex-row border-b border-gray-200 py-2 px-4 bg-white">
        <TouchableOpacity className="flex-row items-center mr-4">
          <Text className="text-gray-700">地区</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-row items-center mr-4">
          <Text className="text-gray-700">行业</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-row items-center mr-4">
          <Text className="text-gray-700">规模</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-row items-center">
          <Text className="text-gray-700">排序</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
      </View>

      {/* 企业列表 */}
      <FlatList
        data={filteredCompanies}
        renderItem={renderCompanyItem}
        keyExtractor={item => item.id}
        contentContainerStyle={{ padding: 16 }}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => (
          <View className="items-center justify-center py-10">
            <Text className="text-gray-400">暂无符合条件的企业</Text>
          </View>
        )}
      />
    </SafeAreaView>
  );
};

export default CompanyListScreen;
