{"name": "mof", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "gen": "alova gen -f", "test": "jest"}, "dependencies": {"@alova/scene-react": "^1.6.2", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native/new-app-screen": "0.80.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@shopify/react-native-skia": "^2.1.1", "@wuba/react-native-echarts": "^2.0.3", "alova": "^3.3.4", "echarts": "^5.6.0", "nativewind": "^4.1.23", "react": "19.1.0", "react-native": "0.80.1", "react-native-gesture-handler": "^2.27.2", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv": "^2.12.2", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.2.0", "tailwindcss": "^3.4.17", "zrender": "^5.5.0", "zustand": "^5.0.6"}, "devDependencies": {"@alova/wormhole": "^1.0.8", "@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native/babel-preset": "0.80.1", "@react-native/eslint-config": "0.80.1", "@react-native/metro-config": "0.80.1", "@react-native/typescript-config": "0.80.1", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.1.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-svg-transformer": "^1.5.1", "react-test-renderer": "19.1.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "overrides": {"tslib": "^2.6.1"}}