/**
 * TabIcon 组件
 * 用于底部导航栏的图标显示
 */

import React from 'react';
import { Image, StyleSheet } from 'react-native';

interface TabIconProps {
  /** 图标名称 */
  name: 'home' | 'graph' | 'follow' | 'profile';
  /** 是否为激活状态 */
  focused: boolean;
  /** 图标大小 */
  size?: number;
}

const TabIcon: React.FC<TabIconProps> = ({ name, focused, size = 24 }) => {
  const getIconSource = () => {
    const suffix = focused ? 'active' : 'inactive';
    
    switch (name) {
      case 'home':
        return focused 
          ? require('@assets/tabbar/home_active.png')
          : require('@assets/tabbar/home_inactive.png');
      case 'graph':
        return focused 
          ? require('@assets/tabbar/graph_active.png')
          : require('@assets/tabbar/graph_inactive.png');
      case 'follow':
        return focused 
          ? require('@assets/tabbar/follow_active.png')
          : require('@assets/tabbar/follow_inactive.png');
      case 'profile':
        return focused 
          ? require('@assets/tabbar/profile_active.png')
          : require('@assets/tabbar/profile_inactive.png');
      default:
        return require('@assets/tabbar/home_inactive.png');
    }
  };

  return (
    <Image
      source={getIconSource()}
      style={[styles.icon, { width: size, height: size }]}
      resizeMode="contain"
    />
  );
};

const styles = StyleSheet.create({
  icon: {
    width: 24,
    height: 24,
  },
});

export default TabIcon;
