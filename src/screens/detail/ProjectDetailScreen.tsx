import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { DetailStackParamList } from '@navigation/types';
import Header from '@components/common/Header';
import ScrollTabs from '@components/common/ScrollTabs';
import Card from '@components/common/Card';

type Props = NativeStackScreenProps<DetailStackParamList, 'ProjectDetail'>;

interface Project {
  id: string;
  name: string;
  images?: string[];
  category: string;
  status: string;
  startDate: string;
  endDate?: string;
  budget: string;
  location: string;
  description: string;
  owner: {
    id: string;
    name: string;
    type: string;
  };
  stage: string;
  progress: number;
  milestones: Milestone[];
  participants: Participant[];
  risks: Risk[];
  updates: Update[];
}

interface Milestone {
  id: string;
  name: string;
  date: string;
  status: 'completed' | 'in_progress' | 'upcoming';
  description?: string;
}

interface Participant {
  id: string;
  name: string;
  role: string;
  joinDate: string;
  responsibility: string;
}

interface Risk {
  id: string;
  type: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  likelihood: 'high' | 'medium' | 'low';
  mitigation?: string;
}

interface Update {
  id: string;
  date: string;
  title: string;
  content: string;
  author: string;
}

const ProjectDetailScreen: React.FC<Props> = ({ navigation, route }) => {
  const { id } = route.params;
  const [activeTab, setActiveTab] = useState('basic');
  const [isFollowing, setIsFollowing] = useState(false);
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentImage, setCurrentImage] = useState<number>(0);

  const tabs = [
    { id: 'basic', title: '基本信息' },
    { id: 'progress', title: '进度情况' },
    { id: 'participants', title: '参与方' },
    { id: 'risks', title: '风险' },
    { id: 'updates', title: '动态' },
  ];

  useEffect(() => {
    // 模拟获取项目数据
    const fetchProjectData = () => {
      setLoading(true);
      // 模拟网络请求
      setTimeout(() => {
        // 模拟项目数据
        const mockProject: Project = {
          id: id,
          name: '某市政道路改造工程',
          images: [
            'https://randomuser.me/api/portraits/men/1.jpg',
            'https://randomuser.me/api/portraits/men/2.jpg',
          ],
          category: '市政工程',
          status: '在建',
          startDate: '2023-03-15',
          endDate: '2024-09-30',
          budget: '1.2亿元',
          location: '北京市海淀区某街道',
          description:
            '该项目是对现有道路进行改造升级，包括道路拓宽、地下管网更新、绿化带建设、交通标识系统升级等内容，总长度约5公里。项目采用智能交通系统，提升道路通行效率和安全性。',
          owner: {
            id: '456',
            name: '北京市海淀区交通局',
            type: '政府单位',
          },
          stage: '主体施工阶段',
          progress: 45,
          milestones: [
            {
              id: 'm1',
              name: '项目启动',
              date: '2023-03-15',
              status: 'completed',
              description: '完成项目启动会议，确定项目组织结构和管理制度',
            },
            {
              id: 'm2',
              name: '设计完成',
              date: '2023-06-20',
              status: 'completed',
              description: '完成项目详细设计和施工图设计',
            },
            {
              id: 'm3',
              name: '施工准备',
              date: '2023-07-10',
              status: 'completed',
              description: '完成施工现场准备，设备进场',
            },
            {
              id: 'm4',
              name: '主体施工',
              date: '2023-08-01',
              status: 'in_progress',
              description: '道路主体施工和地下管网更新',
            },
            {
              id: 'm5',
              name: '智能系统安装',
              date: '2024-03-15',
              status: 'upcoming',
              description: '智能交通系统安装调试',
            },
            {
              id: 'm6',
              name: '项目竣工',
              date: '2024-09-30',
              status: 'upcoming',
              description: '项目竣工验收',
            },
          ],
          participants: [
            {
              id: 'p1',
              name: '某建设集团有限公司',
              role: '总承包方',
              joinDate: '2023-03-15',
              responsibility: '负责项目总体实施和管理',
            },
            {
              id: 'p2',
              name: '某设计院有限公司',
              role: '设计单位',
              joinDate: '2023-03-15',
              responsibility: '负责项目设计和技术支持',
            },
            {
              id: 'p3',
              name: '某监理公司',
              role: '监理单位',
              joinDate: '2023-03-20',
              responsibility: '负责项目质量和进度监控',
            },
            {
              id: 'p4',
              name: '某智能交通系统公司',
              role: '专业分包',
              joinDate: '2023-05-10',
              responsibility: '负责智能交通系统设计和实施',
            },
          ],
          risks: [
            {
              id: 'r1',
              type: '技术风险',
              description: '地下管网情况与设计预期不符',
              impact: 'high',
              likelihood: 'medium',
              mitigation: '增加前期勘察深度，制定应急预案',
            },
            {
              id: 'r2',
              type: '进度风险',
              description: '雨季施工受阻',
              impact: 'medium',
              likelihood: 'high',
              mitigation: '合理安排施工计划，准备防雨设施',
            },
            {
              id: 'r3',
              type: '质量风险',
              description: '道路基础质量不达标',
              impact: 'high',
              likelihood: 'low',
              mitigation: '加强质量控制和检测频率',
            },
            {
              id: 'r4',
              type: '安全风险',
              description: '交通繁忙路段施工安全隐患',
              impact: 'high',
              likelihood: 'medium',
              mitigation: '完善安全管理制度，增加安全警示',
            },
          ],
          updates: [
            {
              id: 'u1',
              date: '2023-11-28',
              title: '完成第三段道路基础施工',
              content:
                '项目第三段道路基础施工已按计划完成，质量检测合格。下一阶段将进行路面铺设工作。',
              author: '项目经理',
            },
            {
              id: 'u2',
              date: '2023-11-15',
              title: '召开第四次项目协调会',
              content:
                '今日召开第四次项目协调会，解决了地下管网与既有设施的交叉问题，调整了施工方案。',
              author: '项目总监',
            },
            {
              id: 'u3',
              date: '2023-11-05',
              title: '智能交通设备采购完成',
              content:
                '项目所需智能交通设备已完成采购，预计下月到场。技术团队已开始准备安装方案。',
              author: '采购经理',
            },
          ],
        };

        setProject(mockProject);
        setLoading(false);
      }, 1000);
    };

    fetchProjectData();
  }, [id]);

  const handleTabPress = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleToggleFollow = () => {
    setIsFollowing(!isFollowing);
  };

  const handleViewParticipant = (participantId: string) => {
    navigation.navigate('CompanyDetail', { id: participantId });
  };

  const handleViewOwner = () => {
    if (project) {
      navigation.navigate('CompanyDetail', { id: project.owner.id });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case '已完工':
        return 'bg-green-50';
      case '在建':
        return 'bg-blue-50';
      case '规划中':
        return 'bg-yellow-50';
      case '暂停':
        return 'bg-red-50';
      default:
        return 'bg-gray-50';
    }
  };

  const getStatusTextColor = (status: string) => {
    switch (status) {
      case '已完工':
        return 'text-green-500';
      case '在建':
        return 'text-blue-500';
      case '规划中':
        return 'text-yellow-600';
      case '暂停':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const getMilestoneStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'in_progress':
        return 'bg-blue-500';
      case 'upcoming':
        return 'bg-gray-300';
      default:
        return 'bg-gray-300';
    }
  };

  const getMilestoneTextColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-500';
      case 'in_progress':
        return 'text-blue-500';
      case 'upcoming':
        return 'text-gray-500';
      default:
        return 'text-gray-500';
    }
  };

  const getRiskImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'text-red-500';
      case 'medium':
        return 'text-yellow-600';
      case 'low':
        return 'text-green-500';
      default:
        return 'text-gray-500';
    }
  };

  if (loading || !project) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <Header
          title="项目详情"
          showBackButton
          onBackPress={() => navigation.goBack()}
        />
        <View className="flex-1 items-center justify-center">
          <Text className="text-gray-500">加载中...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const handleNextImage = () => {
    if (project.images && currentImage < project.images.length - 1) {
      setCurrentImage(currentImage + 1);
    }
  };

  const handlePrevImage = () => {
    if (currentImage > 0) {
      setCurrentImage(currentImage - 1);
    }
  };

  const renderBasicInfo = () => (
    <View className="px-4 py-3">
      {/* 项目图片 */}
      {project.images && project.images.length > 0 && (
        <Card className="mb-4 overflow-hidden">
          <View className="relative">
            <Image
              source={{ uri: project.images[currentImage] }}
              className="w-full h-56"
              resizeMode="cover"
            />
            {project.images.length > 1 && (
              <View className="absolute bottom-2 right-2 flex-row">
                <TouchableOpacity
                  className="bg-black bg-opacity-50 w-8 h-8 rounded-full items-center justify-center mr-2"
                  onPress={handlePrevImage}
                  disabled={currentImage === 0}
                >
                  <Text className="text-white">←</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  className="bg-black bg-opacity-50 w-8 h-8 rounded-full items-center justify-center"
                  onPress={handleNextImage}
                  disabled={currentImage === project.images.length - 1}
                >
                  <Text className="text-white">→</Text>
                </TouchableOpacity>
              </View>
            )}
            <View className="absolute bottom-2 left-2 bg-black bg-opacity-50 px-2 py-1 rounded">
              <Text className="text-white text-xs">
                {currentImage + 1}/{project.images.length}
              </Text>
            </View>
          </View>
        </Card>
      )}

      {/* 项目描述 */}
      <Card className="mb-4 p-4">
        <Text className="text-lg font-bold mb-2">项目描述</Text>
        <Text className="text-gray-600 leading-5">{project.description}</Text>
      </Card>

      {/* 基本信息 */}
      <Card className="mb-4 p-4">
        <Text className="text-lg font-bold mb-3">基本信息</Text>

        <View className="flex-row mb-2">
          <Text className="text-gray-500 w-24">项目类别</Text>
          <Text className="text-gray-700 flex-1">{project.category}</Text>
        </View>

        <View className="flex-row mb-2">
          <Text className="text-gray-500 w-24">项目状态</Text>
          <Text className={`${getStatusTextColor(project.status)} flex-1`}>
            {project.status}
          </Text>
        </View>

        <View className="flex-row mb-2">
          <Text className="text-gray-500 w-24">当前阶段</Text>
          <Text className="text-gray-700 flex-1">{project.stage}</Text>
        </View>

        <View className="flex-row mb-2">
          <Text className="text-gray-500 w-24">项目预算</Text>
          <Text className="text-gray-700 flex-1">{project.budget}</Text>
        </View>

        <View className="flex-row mb-2">
          <Text className="text-gray-500 w-24">开始日期</Text>
          <Text className="text-gray-700 flex-1">{project.startDate}</Text>
        </View>

        {project.endDate && (
          <View className="flex-row mb-2">
            <Text className="text-gray-500 w-24">计划完成</Text>
            <Text className="text-gray-700 flex-1">{project.endDate}</Text>
          </View>
        )}

        <View className="flex-row mb-4">
          <Text className="text-gray-500 w-24">项目地点</Text>
          <Text className="text-gray-700 flex-1">{project.location}</Text>
        </View>

        <Text className="text-gray-500 mb-2">
          项目进度 ({project.progress}%)
        </Text>
        <View className="w-full h-2 bg-gray-200 rounded-full">
          <View
            className="h-2 bg-blue-500 rounded-full"
            style={{ width: `${project.progress}%` }}
          />
        </View>
      </Card>

      {/* 业主信息 */}
      <Card className="mb-4 p-4">
        <Text className="text-lg font-bold mb-3">业主信息</Text>
        <TouchableOpacity onPress={handleViewOwner}>
          <View className="flex-row">
            <Text className="text-gray-500 w-24">业主名称</Text>
            <Text className="text-blue-500 flex-1">{project.owner.name}</Text>
          </View>
          <View className="flex-row mt-2">
            <Text className="text-gray-500 w-24">业主类型</Text>
            <Text className="text-gray-700 flex-1">{project.owner.type}</Text>
          </View>
        </TouchableOpacity>
      </Card>
    </View>
  );

  const renderProgress = () => (
    <View className="px-4 py-3">
      <Card className="mb-4 p-4">
        <Text className="text-lg font-bold mb-4">项目进度</Text>

        <View className="flex-row mb-4">
          <View className="w-full h-2 bg-gray-200 rounded-full">
            <View
              className="h-2 bg-blue-500 rounded-full"
              style={{ width: `${project.progress}%` }}
            />
          </View>
        </View>

        <View className="flex-row justify-between mb-4">
          <Text className="text-gray-500">0%</Text>
          <Text className="text-blue-500 font-bold">{project.progress}%</Text>
          <Text className="text-gray-500">100%</Text>
        </View>

        <Text className="font-bold mb-2">当前阶段: {project.stage}</Text>
      </Card>

      <Card className="p-4">
        <Text className="text-lg font-bold mb-3">里程碑</Text>

        <View className="relative">
          {/* 竖线 */}
          <View className="absolute left-2 top-2 bottom-0 w-0.5 bg-gray-200" />

          {project.milestones.map(milestone => (
            <View key={milestone.id} className="flex-row mb-6 relative">
              <View
                className={`w-4 h-4 rounded-full ${getMilestoneStatusColor(
                  milestone.status,
                )} mt-1`}
              />

              <View className="ml-4 flex-1">
                <View className="flex-row justify-between">
                  <Text className="font-medium">{milestone.name}</Text>
                  <Text
                    className={`${getMilestoneTextColor(milestone.status)}`}
                  >
                    {milestone.status === 'completed'
                      ? '已完成'
                      : milestone.status === 'in_progress'
                      ? '进行中'
                      : '未开始'}
                  </Text>
                </View>
                <Text className="text-gray-500 text-xs mt-1">
                  {milestone.date}
                </Text>
                {milestone.description && (
                  <Text className="text-gray-600 mt-1 text-sm">
                    {milestone.description}
                  </Text>
                )}
              </View>
            </View>
          ))}
        </View>
      </Card>
    </View>
  );

  const renderParticipants = () => (
    <View className="px-4 py-3">
      <Text className="text-lg font-bold mb-3">参与单位</Text>

      {project.participants.map(participant => (
        <TouchableOpacity
          key={participant.id}
          onPress={() => handleViewParticipant(participant.id)}
          className="mb-3"
        >
          <Card className="p-4">
            <View className="flex-row justify-between items-start">
              <View className="flex-1">
                <Text className="text-base font-medium">
                  {participant.name}
                </Text>
                <Text className="text-blue-500 mt-1">{participant.role}</Text>
                <Text className="text-gray-500 text-xs mt-1">
                  加入时间: {participant.joinDate}
                </Text>
              </View>
            </View>

            <Text className="text-gray-600 text-sm mt-2">
              {participant.responsibility}
            </Text>
          </Card>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderRisks = () => (
    <View className="px-4 py-3">
      <Text className="text-lg font-bold mb-3">项目风险</Text>

      {project.risks.map(risk => (
        <Card key={risk.id} className="mb-3 p-4">
          <View className="flex-row justify-between">
            <Text className="font-medium">{risk.type}</Text>
            <View className="flex-row">
              <Text className="text-gray-500 mr-2">影响:</Text>
              <Text className={getRiskImpactColor(risk.impact)}>
                {risk.impact === 'high'
                  ? '高'
                  : risk.impact === 'medium'
                  ? '中'
                  : '低'}
              </Text>
            </View>
          </View>

          <Text className="text-gray-600 mt-2">{risk.description}</Text>

          {risk.mitigation && (
            <View className="mt-2 pt-2 border-t border-gray-100">
              <Text className="text-gray-500">缓解措施:</Text>
              <Text className="text-gray-600 mt-1">{risk.mitigation}</Text>
            </View>
          )}
        </Card>
      ))}
    </View>
  );

  const renderUpdates = () => (
    <View className="px-4 py-3">
      <Text className="text-lg font-bold mb-3">项目动态</Text>

      {project.updates.map(update => (
        <Card key={update.id} className="mb-3 p-4">
          <View className="flex-row justify-between mb-1">
            <Text className="font-medium">{update.title}</Text>
            <Text className="text-gray-500 text-xs">{update.date}</Text>
          </View>

          <Text className="text-gray-600 mt-2">{update.content}</Text>

          <Text className="text-gray-500 text-xs mt-2 text-right">
            更新人: {update.author}
          </Text>
        </Card>
      ))}
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-white">
      <Header
        title="项目详情"
        showBackButton
        onBackPress={() => navigation.goBack()}
        rightComponent={
          <TouchableOpacity onPress={handleToggleFollow}>
            <Text className={isFollowing ? 'text-gray-500' : 'text-blue-500'}>
              {isFollowing ? '已关注' : '关注'}
            </Text>
          </TouchableOpacity>
        }
      />

      {/* 项目名称及状态 */}
      <View className="p-4 border-b border-gray-200 bg-white">
        <View className="flex-row justify-between items-start">
          <View className="flex-1">
            <Text className="text-xl font-bold">{project.name}</Text>
            <Text className="text-gray-500 mt-1">{project.category}</Text>
          </View>
          <View
            className={`px-3 py-1 rounded ${getStatusColor(project.status)}`}
          >
            <Text
              className={`font-medium ${getStatusTextColor(project.status)}`}
            >
              {project.status}
            </Text>
          </View>
        </View>
      </View>

      <ScrollTabs
        tabs={tabs}
        activeTab={activeTab}
        onTabPress={handleTabPress}
      />

      <ScrollView className="flex-1">
        {activeTab === 'basic' && renderBasicInfo()}
        {activeTab === 'progress' && renderProgress()}
        {activeTab === 'participants' && renderParticipants()}
        {activeTab === 'risks' && renderRisks()}
        {activeTab === 'updates' && renderUpdates()}
      </ScrollView>
    </SafeAreaView>
  );
};

export default ProjectDetailScreen;
