import React from 'react';
import { View, Text, TouchableOpacity, SafeAreaView } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ProfileStackParamList } from '../../navigation/types';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { navigateToAuth } from '../../navigation/rootNavigation';

type Props = NativeStackScreenProps<ProfileStackParamList, 'Settings'>;

interface SettingItemProps {
  title: string;
  icon: React.ReactNode;
  onPress: () => void;
}

const SettingItem: React.FC<SettingItemProps> = ({ title, icon, onPress }) => {
  return (
    <TouchableOpacity
      className="flex-row items-center py-4 px-4 bg-white"
      onPress={onPress}
    >
      <View className="mr-4">{icon}</View>
      <Text className="flex-1 text-gray-800">{title}</Text>
      <Icon name="chevron-right" size={20} color="#CCCCCC" />
    </TouchableOpacity>
  );
};

const SettingsScreen: React.FC<Props> = ({ navigation }) => {
  // 设置项
  const settingItems = [
    {
      title: '修改名称',
      icon: <Icon name="account-edit-outline" size={24} color="#666666" />,
      onPress: () => navigation.navigate('EditName'),
    },
    {
      title: '修改密码',
      icon: <Icon name="lock-outline" size={24} color="#666666" />,
      onPress: () => navigateToAuth('ResetPassword', { phone: '', code: '' }),
    },
    {
      title: '更换绑定手机',
      icon: <Icon name="cellphone" size={24} color="#666666" />,
      onPress: () =>
        navigateToAuth('VerificationCode', { phone: '', type: 'reset' }),
    },
  ];

  return (
    <SafeAreaView className="flex-1 bg-gray-100">
      {/* 标题栏 */}
      <View className="bg-white flex-row items-center p-4 border-b border-gray-200">
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Icon name="arrow-left" size={24} color="#333" />
        </TouchableOpacity>
        <Text className="flex-1 text-center text-lg font-medium">设置</Text>
        <View style={{ width: 24 }} />
      </View>

      {/* 设置项列表 */}
      <View className="mx-4 mt-4 rounded-xl overflow-hidden">
        {settingItems.map((item, index) => (
          <View key={index}>
            <SettingItem
              title={item.title}
              icon={item.icon}
              onPress={item.onPress}
            />
            {index < settingItems.length - 1 && (
              <View className="h-px bg-gray-100 ml-12" />
            )}
          </View>
        ))}
      </View>
    </SafeAreaView>
  );
};

export default SettingsScreen;
