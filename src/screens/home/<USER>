import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Platform,
  Image,
  ImageBackground,
  StyleSheet,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { HomeStackParamList, RootStackParamList } from '../../navigation/types';
import Card from '../../components/common/Card';
import SearchBar from '../../components/common/SearchBar';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

type Props = NativeStackScreenProps<HomeStackParamList, 'Home'>;
type RootNavigationProp = NativeStackNavigationProp<RootStackParamList>;

// 添加工具项的类型定义
interface ToolItem {
  id: string;
  name: string;
  icon: any; // 改为any类型以支持require()
  onPress: () => void;
  color?: string;
  badge?: string;
}

const HomeScreen: React.FC<Props> = () => {
  const [activeTab, setActiveTab] = useState('company'); // 'company' 或 'bidding'
  const [activeDataTab, setActiveDataTab] = useState('package'); // 'package' 或 'ranking'

  // 使用根导航
  const navigation = useNavigation<RootNavigationProp>();

  // 行业标签
  const industryTags = [
    { id: '1', name: '招投' },
    { id: '2', name: '装修' },
    { id: '3', name: '体检' },
    { id: '4', name: '银行' },
    { id: '5', name: '医保' },
    { id: '6', name: '智慧医院' },
  ];

  // 企业查询工具数据
  const companyToolItems: ToolItem[] = [
    {
      id: '1',
      name: '查产品',
      icon: require('@assets/home/<USER>'),
      onPress: () => navigation.navigate('SearchResult', { type: 'product' }),
    },
    {
      id: '2',
      name: '查业绩',
      icon: require('@assets/home/<USER>'),
      onPress: () =>
        navigation.navigate('SearchResult', { type: 'performance' }),
    },
    {
      id: '3',
      name: '查人员',
      icon: require('@assets/home/<USER>'),
      onPress: () => navigation.navigate('SearchResult', { type: 'person' }),
      badge: '即将上线',
    },
    {
      id: '4',
      name: '查资质',
      icon: require('@assets/home/<USER>'),
      onPress: () =>
        navigation.navigate('SearchResult', { type: 'qualification' }),
    },
    {
      id: '5',
      name: '查专利',
      icon: require('@assets/home/<USER>'),
      onPress: () => navigation.navigate('SearchResult', { type: 'patent' }),
    },
    {
      id: '6',
      name: '更多工具',
      icon: require('@assets/home/<USER>'),
      onPress: () => {},
      badge: '即将上线',
    },
  ];

  // 标讯工具数据
  const biddingToolItems: ToolItem[] = [
    {
      id: '1',
      name: '查标讯',
      icon: require('@assets/home/<USER>'),
      onPress: () => navigation.navigate('BidNotice'),
    },
    {
      id: '2',
      name: '查业主',
      icon: require('@assets/home/<USER>'),
      onPress: () => navigation.navigate('OwnerSearch'),
    },
    {
      id: '3',
      name: '查渠道',
      icon: require('@assets/home/<USER>'),
      onPress: () => {},
    },
    {
      id: '4',
      name: '查代理',
      icon: require('@assets/home/<USER>'),
      onPress: () => {},
    },
  ];

  // 潜客数据包
  const dataPackages = [
    {
      id: '1',
      title: '失信被执行人企业',
      description: '有失信被执行人的企业',
      count: '100w+',
      type: 'danger',
    },
    {
      id: '2',
      title: '失信被执行人企业',
      description: '有失信被执行人的企业',
      count: '100w+',
      type: 'danger',
    },
    {
      id: '3',
      title: '失信被执行人企业',
      description: '有失信被执行人的企业',
      count: '100w+',
      type: 'danger',
    },
    {
      id: '4',
      title: '失信被执行人企业',
      description: '有失信被执行人的企业',
      count: '100w+',
      type: 'danger',
    },
  ];

  // 企业榜单数据
  const companyRankings = [
    {
      id: '1',
      title: '科技型中小企业',
      count: '10000',
      description:
        '在《国家重点支持的高新技术领域》内，持续进行研究开发与技术成果转化，形成企业核心自主知识产权，并以此为基础...',
      color: 'bg-blue-100',
      tag: '科技型',
    },
    {
      id: '2',
      title: '科技型中小企业',
      count: '10000',
      description:
        '在《国家重点支持的高新技术领域》内，持续进行研究开发与技术成果转化，形成企业核心自主知识产权，并以此为基础...',
      color: 'bg-green-100',
      tag: '科技型',
    },
    {
      id: '3',
      title: '科技型中小企业',
      count: '10000',
      description:
        '在《国家重点支持的高新技术领域》内，持续进行研究开发与技术成果转化，形成企业核心自主知识产权，并以此为基础...',
      color: 'bg-yellow-100',
      tag: '科技型',
    },
    {
      id: '4',
      title: '科技型中小企业',
      count: '10000',
      description:
        '在《国家重点支持的高新技术领域》内，持续进行研究开发与技术成果转化，形成企业核心自主知识产权，并以此为基础...',
      color: 'bg-orange-100',
      tag: '科技型',
    },
  ];

  // 最新标讯数据
  const latestBiddings = [
    {
      id: '1',
      title:
        '北京亿达腾飞科技有限公司北京亿达腾飞腾飞科技有限公司腾飞科技有腾飞...',
      type: '中标公告',
      amount: '20000元',
      bidder: '2个联系人',
      company: 'xxx有限责任公司（自然人投资或控股）',
      location: '湖南 长沙',
      date: '10:00',
    },
    {
      id: '2',
      title:
        '北京亿达腾飞科技有限公司北京亿达腾飞腾飞科技有限公司腾飞科技有腾飞...',
      type: '招标公告',
      amount: '20000元',
      company: 'xxx有限责任公司（自然人投资或控股）',
      agent: 'xxx有限责任公司（自然人投资或控股）',
      location: '湖南 长沙',
      date: '2025-05-05',
    },
  ];

  const renderDataContent = () => {
    if (activeDataTab === 'package') {
      // 潜客数据包内容
      return (
        <View className="mx-4 mb-4">
          {dataPackages.map(item => (
            <TouchableOpacity
              key={item.id}
              className="bg-white rounded-lg p-4 mb-3 shadow-sm"
            >
              <View className="flex-row justify-between items-start">
                <View className="flex-1">
                  <Text className="text-lg font-bold text-gray-800 mb-1">
                    {item.title}
                  </Text>
                  <Text className="text-gray-500 text-sm mb-2">
                    {item.description}
                  </Text>
                  <Text className="text-gray-600 text-sm">
                    企业数量:{' '}
                    <Text className="text-red-500 font-bold">{item.count}</Text>
                  </Text>
                </View>
                <View className="ml-4">
                  <Text className="text-gray-400 text-xl">{'>'}</Text>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      );
    } else {
      // 企业榜单内容
      return (
        <View className="mx-4 mb-4">
          {companyRankings.map(item => (
            <TouchableOpacity
              key={item.id}
              className="bg-white rounded-md p-4 mb-3"
            >
              <View className="flex-row items-center mb-2">
                <View
                  className={`${item.color} w-8 h-8 rounded-md items-center justify-center mr-2`}
                >
                  <Text>{item.tag}</Text>
                </View>
                <Text className="text-lg font-bold">{item.title}</Text>
              </View>
              <Text className="text-gray-600 mb-1">
                企业数量:{' '}
                <Text className="text-blue-500 font-bold">{item.count}</Text>
              </Text>
              <Text className="text-gray-500 text-sm" numberOfLines={2}>
                {item.description}
              </Text>
              <View className="absolute right-4 top-1/2 -translate-y-2">
                <Text className="text-gray-400">{'>'}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      );
    }
  };

  // 渲染工具区域
  const renderToolsSection = () => {
    const toolItems = activeTab === 'company' ? companyToolItems : biddingToolItems;

    return (
      <ImageBackground
        source={require('@assets/home/<USER>')}
        style={styles.toolsContainer}
        resizeMode="cover"
      >
        <View style={styles.toolsGrid}>
          {toolItems.map(item => (
            <TouchableOpacity
              key={item.id}
              style={styles.toolItem}
              onPress={item.onPress}
            >
              <View style={styles.toolIconContainer}>
                <Image source={item.icon} style={styles.toolIcon} resizeMode="contain" />
                {item.badge && (
                  <View style={styles.badge}>
                    <Text style={styles.badgeText}>{item.badge}</Text>
                  </View>
                )}
              </View>
              <Text style={styles.toolName}>{item.name}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </ImageBackground>
    );
  };

  // 渲染数据切换区域
  const renderDataTabsSection = () => {
    return (
      <ImageBackground
        source={require('@assets/home/<USER>')}
        style={styles.dataTabsContainer}
        resizeMode="cover"
      >
        <View style={styles.dataTabsWrapper}>
          <TouchableOpacity
            style={[
              styles.dataTab,
              activeDataTab === 'package' && styles.activeDataTab
            ]}
            onPress={() => setActiveDataTab('package')}
          >
            <Text style={[
              styles.dataTabText,
              activeDataTab === 'package' && styles.activeDataTabText
            ]}>
              潜客数据包
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.dataTab,
              activeDataTab === 'ranking' && styles.activeDataTab
            ]}
            onPress={() => setActiveDataTab('ranking')}
          >
            <Text style={[
              styles.dataTabText,
              activeDataTab === 'ranking' && styles.activeDataTabText
            ]}>
              企业榜单
            </Text>
          </TouchableOpacity>
        </View>
      </ImageBackground>
    );
  };

          {/* 根据选中的标签显示相应内容 */}
          {renderDataContent()}

          {/* 查看更多按钮 */}
          <TouchableOpacity className="mx-4 mb-6 bg-white rounded-full py-4 shadow-sm">
            <Text className="text-center text-gray-600 font-medium">
              查看更多 {'>'}
            </Text>
          </TouchableOpacity>
        </>
      );
    } else {
      return (
        <>
          {/* 标讯工具区域 */}
          <Card className="mx-4 my-3 p-3">
            <View className="mb-2">
              <Text className="text-base font-bold text-gray-700">
                标讯查询工具
              </Text>
            </View>
            <View className="flex-row flex-wrap">
              {biddingToolItems.map(item => (
                <TouchableOpacity
                  key={item.id}
                  className="w-1/4 items-center py-2"
                  onPress={item.onPress}
                >
                  <View className="relative">
                    <View
                      className={`w-12 h-12 rounded-full ${item.color} items-center justify-center mb-1`}
                    >
                      <Text className="text-xl">{item.icon}</Text>
                    </View>
                    {item.badge && (
                      <View className="absolute top-0 right-0 bg-red-500 px-1 rounded-md">
                        <Text className="text-white text-xs">{item.badge}</Text>
                      </View>
                    )}
                  </View>
                  <Text className="text-xs text-gray-800">{item.name}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </Card>

          {/* 最新标讯 */}
          <View className="mx-4 mb-2">
            <View className="flex-row justify-between items-center mb-2">
              <Text className="text-lg font-bold">最新标讯</Text>
              <View className="flex-row">
                <TouchableOpacity className="mr-2">
                  <Text className="text-gray-500">信息类型 ▼</Text>
                </TouchableOpacity>
                <TouchableOpacity>
                  <Text className="text-gray-500">地区 ▼</Text>
                </TouchableOpacity>
              </View>
            </View>

            {latestBiddings.map(item => (
              <TouchableOpacity
                key={item.id}
                className="bg-white rounded-md p-4 mb-3"
              >
                <Text className="text-base font-medium mb-2" numberOfLines={2}>
                  {item.title}
                </Text>
                <View className="flex-row mb-1">
                  {item.type === '中标公告' ? (
                    <View className="bg-red-100 px-2 py-0.5 rounded mr-2">
                      <Text className="text-red-500 text-xs">{item.type}</Text>
                    </View>
                  ) : (
                    <View className="bg-blue-100 px-2 py-0.5 rounded mr-2">
                      <Text className="text-blue-500 text-xs">{item.type}</Text>
                    </View>
                  )}
                  <View className="bg-yellow-50 px-2 py-0.5 rounded mr-2">
                    <Text className="text-yellow-600 text-xs">行业</Text>
                  </View>
                  <View className="bg-yellow-50 px-2 py-0.5 rounded mr-2">
                    <Text className="text-yellow-600 text-xs">
                      {item.amount}
                    </Text>
                  </View>
                  {item.bidder && (
                    <View className="bg-green-50 px-2 py-0.5 rounded">
                      <Text className="text-green-600 text-xs">
                        {item.bidder}
                      </Text>
                    </View>
                  )}
                </View>
                <Text className="text-gray-500 text-xs mb-1">
                  中标单位: {item.company}
                </Text>
                {item.agent && (
                  <Text className="text-gray-500 text-xs mb-1">
                    代理单位: {item.agent}
                  </Text>
                )}
                <View className="flex-row justify-between items-center mt-2">
                  <View className="flex-row items-center">
                    <Text className="text-blue-500 mr-1">📍</Text>
                    <Text className="text-gray-500 text-xs">
                      {item.location}
                    </Text>
                  </View>
                  <Text className="text-gray-500 text-xs">{item.date}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>

          {/* 查看更多按钮 */}
          <TouchableOpacity className="mx-4 mb-6 bg-white rounded-full py-3">
            <Text className="text-center text-gray-500">查看更多 {'>'}</Text>
          </TouchableOpacity>
        </>
      );
    }
  };

  // 获取状态栏高度
  const statusBarHeight =
    Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0;

  return (
    <ScrollView className="flex-1 bg-gray-100" bounces={false}>
      <StatusBar barStyle="light-content" backgroundColor="#3B82F6" />

      {/* 顶部蓝色区域 */}
      <View
        className="bg-blue-500 pb-3 px-4"
        style={{ paddingTop: statusBarHeight + 10 }}
      >
        {/* 搜索框 */}
        <SearchBar
          value=""
          onChangeText={() => {}}
          onSearch={() => {}}
          placeholder={
            activeTab === 'company'
              ? '请输入名称、关键词'
              : '请输入项目名称、项目编号等关键词'
          }
          onPressSearchBar={() => {
            if (activeTab === 'company') {
              navigation.navigate('CompanySearch');
            } else {
              navigation.navigate('BidNoticeSearch', { keyword: '' });
            }
          }}
        />

        <View className="flex-row justify-center mt-4">
          <TouchableOpacity
            className={`mx-4 pb-1 ${
              activeTab === 'company' ? 'border-b-2 border-white' : ''
            }`}
            onPress={() => setActiveTab('company')}
          >
            <Text className="text-white text-lg font-medium">查企业</Text>
          </TouchableOpacity>
          <TouchableOpacity
            className={`mx-4 pb-1 ${
              activeTab === 'bidding' ? 'border-b-2 border-white' : ''
            }`}
            onPress={() => setActiveTab('bidding')}
          >
            <Text className="text-white text-lg font-medium">查标讯</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* 行业标签区域 */}
      <View className="bg-white">
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          className="px-4 py-3"
        >
          {industryTags.map((tag, index) => (
            <TouchableOpacity
              key={tag.id}
              className={`mr-3 px-4 py-2 rounded-full ${
                index === 0 ? 'bg-orange-100' : 'bg-gray-100'
              }`}
            >
              <Text
                className={`text-sm font-medium ${
                  index === 0 ? 'text-orange-600' : 'text-gray-600'
                }`}
              >
                {tag.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* 主要内容区域 */}
      {renderMainContent()}
    </ScrollView>
  );
};

export default HomeScreen;
