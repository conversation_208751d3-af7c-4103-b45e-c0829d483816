import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleProp,
  ViewStyle,
  TextStyle,
} from 'react-native';

interface ListItemProps {
  title: string;
  subtitle?: string;
  leftComponent?: React.ReactNode;
  rightComponent?: React.ReactNode;
  onPress?: () => void;
  containerStyle?: StyleProp<ViewStyle>;
  titleStyle?: StyleProp<TextStyle>;
  subtitleStyle?: StyleProp<TextStyle>;
  showBorder?: boolean;
  disabled?: boolean;
}

const ListItem: React.FC<ListItemProps> = ({
  title,
  subtitle,
  leftComponent,
  rightComponent,
  onPress,
  containerStyle,
  titleStyle,
  subtitleStyle,
  showBorder = true,
  disabled = false,
}) => {
  const renderContent = () => (
    <View
      className={`flex-row items-center py-3 px-4 ${
        showBorder ? 'border-b border-gray-200' : ''
      }`}
      style={containerStyle}
    >
      {leftComponent && <View className="mr-3">{leftComponent}</View>}

      <View className="flex-1 justify-center">
        <Text
          className="text-base text-gray-800 font-medium"
          numberOfLines={1}
          style={titleStyle}
        >
          {title}
        </Text>

        {subtitle && (
          <Text
            className="text-sm text-gray-500 mt-1"
            numberOfLines={2}
            style={subtitleStyle}
          >
            {subtitle}
          </Text>
        )}
      </View>

      {rightComponent && (
        <View className="ml-2 items-end">{rightComponent}</View>
      )}
    </View>
  );

  if (onPress) {
    return (
      <TouchableOpacity
        onPress={onPress}
        disabled={disabled}
        activeOpacity={0.7}
      >
        {renderContent()}
      </TouchableOpacity>
    );
  }

  return renderContent();
};

export default ListItem;
