import React from 'react';
import { View, Text, SafeAreaView, ScrollView } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ProfileStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';

type Props = NativeStackScreenProps<ProfileStackParamList, 'UserAgreement'>;

const UserAgreementScreen: React.FC<Props> = ({ navigation }) => {
  return (
    <SafeAreaView className="flex-1 bg-white">
      <Header
        title="用户协议"
        showBackButton
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView className="flex-1 px-4 py-4">
        <Text className="text-xl font-bold mb-4">用户协议</Text>

        <Text className="text-base mb-3">
          欢迎使用企业图谱应用！在使用我们的服务之前，请您仔细阅读以下用户协议。
        </Text>

        <View className="mb-4">
          <Text className="text-lg font-medium mb-2">1. 协议的接受</Text>
          <Text className="text-base text-gray-700 leading-6">
            通过使用企业图谱应用及相关服务，您确认已阅读、理解并同意接受本协议的所有条款和条件。如您不同意本协议的任何内容，请勿使用本应用及相关服务。
          </Text>
        </View>

        <View className="mb-4">
          <Text className="text-lg font-medium mb-2">2. 服务内容</Text>
          <Text className="text-base text-gray-700 leading-6">
            企业图谱应用主要提供企业信息查询、企业关系可视化、行业动态追踪等服务。我们保留在不通知的情况下变更、中断或终止部分或全部服务的权利。
          </Text>
        </View>

        <View className="mb-4">
          <Text className="text-lg font-medium mb-2">3. 用户账号</Text>
          <Text className="text-base text-gray-700 leading-6">
            用户在使用本应用时需要注册账号并保持信息的真实、准确和完整。用户对账号的所有活动负责，应妥善保管账号密码，防止被盗用或滥用。
          </Text>
        </View>

        <View className="mb-4">
          <Text className="text-lg font-medium mb-2">4. 用户行为规范</Text>
          <Text className="text-base text-gray-700 leading-6">
            用户在使用本应用时，应遵守中华人民共和国相关法律法规，不得利用本应用从事违法活动，不得发布违法或不良信息，不得侵犯他人知识产权或其他合法权益。
          </Text>
        </View>

        <View className="mb-4">
          <Text className="text-lg font-medium mb-2">5. 数据使用</Text>
          <Text className="text-base text-gray-700 leading-6">
            本应用提供的企业数据来源于公开渠道和自主填报，仅供参考，不作为商业决策的唯一依据。用户应自行核实数据的准确性和完整性。
          </Text>
        </View>

        <View className="mb-4">
          <Text className="text-lg font-medium mb-2">6. 知识产权</Text>
          <Text className="text-base text-gray-700 leading-6">
            本应用及相关内容（包括但不限于文字、图片、音频、视频、软件、程序、版面设计）的知识产权归本公司所有。未经授权，用户不得复制、修改、传播或用于商业目的。
          </Text>
        </View>

        <View className="mb-4">
          <Text className="text-lg font-medium mb-2">7. 隐私保护</Text>
          <Text className="text-base text-gray-700 leading-6">
            我们重视用户隐私保护，收集和使用用户信息将遵循我们的隐私政策。请参阅我们的隐私政策了解详细信息。
          </Text>
        </View>

        <View className="mb-4">
          <Text className="text-lg font-medium mb-2">8. 免责声明</Text>
          <Text className="text-base text-gray-700 leading-6">
            本应用不保证服务不中断或无错误，不对因网络、设备等原因导致的服务中断或数据丢失承担责任。本应用提供的信息仅供参考，不构成任何形式的建议。
          </Text>
        </View>

        <View className="mb-4">
          <Text className="text-lg font-medium mb-2">9. 协议修改</Text>
          <Text className="text-base text-gray-700 leading-6">
            我们保留随时修改本协议的权利，修改后的协议将在应用内公布。继续使用本应用即视为接受修改后的协议。
          </Text>
        </View>

        <View className="mb-8">
          <Text className="text-lg font-medium mb-2">10. 适用法律</Text>
          <Text className="text-base text-gray-700 leading-6">
            本协议的订立、执行和解释及争议的解决均适用中华人民共和国法律。如发生争议，双方应友好协商解决；协商不成的，任何一方均有权将争议提交至本公司所在地有管辖权的人民法院诉讼解决。
          </Text>
        </View>

        <Text className="text-base text-gray-500 text-center mb-6">
          最后更新日期：2023年12月1日
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
};

export default UserAgreementScreen;
