import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  Image,
  ActivityIndicator,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation/types';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import FilterDropdown, {
  FilterOption,
  FilterMenuOption,
  FilterGroup,
} from '../../components/common/FilterDropdown';

type Props = NativeStackScreenProps<RootStackParamList, 'BidNoticeSearch'>;
type RootNavigationProp = NativeStackNavigationProp<RootStackParamList>;

interface SearchHistory {
  id: string;
  keyword: string;
}

interface HotSearch {
  id: string;
  keyword: string;
  rank: number;
  icon?: any;
}

interface BidNotice {
  id: string;
  title: string;
  type: string;
  amount: string;
  company: string;
  location: string;
  date: string;
  bidder?: string;
  agent?: string;
}

const BidNoticeSearchScreen: React.FC<Props> = ({ route }) => {
  const [searchKeyword, setSearchKeyword] = useState(
    route.params?.keyword || '',
  );
  const [searchResults, setSearchResults] = useState<BidNotice[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [showCancelButton, setShowCancelButton] = useState(false);
  const [totalResults] = useState(1000);
  const [activeTab, setActiveTab] = useState('标讯');

  // 使用根导航
  const navigation = useNavigation<RootNavigationProp>();

  // 标签选项
  const tabs = useMemo(
    () => [
      { id: 'bidding', title: '标讯' },
      { id: 'owner', title: '业主' },
      { id: 'channel', title: '渠道' },
      { id: 'agent', title: '代理' },
    ],
    [],
  );

  // 模拟搜索历史
  const searchHistories: SearchHistory[] = useMemo(
    () => [
      { id: '1', keyword: '启客科技有限公司' },
      { id: '2', keyword: '北京小米' },
      { id: '3', keyword: '启客科技有限公司' },
      { id: '4', keyword: '北京广告' },
      { id: '5', keyword: '启客科技有限公司' },
    ],
    [],
  );

  // 模拟热门搜索
  const hotSearches: HotSearch[] = useMemo(
    () => [
      {
        id: '1',
        keyword: '小米科技有限公司',
        rank: 1,
        icon: require('../../assets/h1.png'),
      },
      {
        id: '2',
        keyword: '启客（北京）科技有限公司',
        rank: 2,
        icon: require('../../assets/h2.png'),
      },
      { id: '3', keyword: '北京华为信息科技有限公司', rank: 3 },
      { id: '4', keyword: '金风科技有限公司', rank: 4 },
      { id: '5', keyword: '北京xxx科技有限公司', rank: 5 },
      { id: '6', keyword: '北京xxx科技有限公司', rank: 6 },
      { id: '7', keyword: '北京xxx科技有限公司', rank: 7 },
      { id: '8', keyword: '北京xxx科技有限公司', rank: 8 },
      { id: '9', keyword: '北京xxx科技有限公司', rank: 9 },
      { id: '10', keyword: '北京xxx科技有限公司', rank: 10 },
    ],
    [],
  );

  // 模拟搜索结果数据
  const mockResults: BidNotice[] = useMemo(
    () => [
      {
        id: '1',
        title: '北京亿达腾飞科技有限公司招标公告',
        type: '招标公告',
        amount: '20000元',
        company: 'xxx有限责任公司（自然人投资或控股）',
        location: '北京',
        date: '2025-05-05',
      },
      {
        id: '2',
        title: '智能医疗设备采购项目',
        type: '招标公告',
        amount: '150000元',
        company: '某医疗科技有限公司',
        location: '北京 海淀',
        date: '2025-05-03',
      },
      {
        id: '3',
        title: '市政道路维修工程中标公告',
        type: '中标公告',
        amount: '500000元',
        bidder: '3个联系人',
        company: '某建设工程有限公司',
        location: '上海 浦东',
        date: '2025-05-01',
      },
      {
        id: '4',
        title: '校园网络设备更新招标',
        type: '招标公告',
        amount: '80000元',
        company: '某教育科技有限公司',
        location: '广州 天河',
        date: '2025-04-28',
      },
      {
        id: '5',
        title: '医院信息系统建设项目中标公告',
        type: '中标公告',
        amount: '300000元',
        bidder: '2个联系人',
        company: '某信息技术有限公司',
        agent: '某招标代理有限公司',
        location: '深圳 南山',
        date: '2025-04-25',
      },
    ],
    [],
  );

  // 筛选条件菜单选项
  const filterMenuOptions: FilterMenuOption[] = useMemo(
    () => [
      { id: 'area', title: '地区' },
      { id: 'industry', title: '行业' },
      { id: 'type', title: '类型' },
      { id: 'sort', title: '排序' },
    ],
    [],
  );

  const handleSearch = useCallback(() => {
    if (searchKeyword.trim()) {
      setIsLoading(true);
      setShowResults(true);

      // 模拟网络请求延迟
      setTimeout(() => {
        const filtered = mockResults.filter(
          item =>
            item.title.includes(searchKeyword) ||
            item.company.includes(searchKeyword),
        );
        setSearchResults(filtered);
        setIsLoading(false);
      }, 800);
    } else {
      setSearchResults([]);
    }
  }, [searchKeyword, mockResults]);

  useEffect(() => {
    if (searchKeyword.trim()) {
      handleSearch();
    }
  }, [searchKeyword, handleSearch]);

  const handleClearHistory = () => {
    // 实现清除历史记录的逻辑
  };

  const handleHistoryItemPress = (item: SearchHistory) => {
    setSearchKeyword(item.keyword);
    handleSearch();
  };

  const handleHotSearchPress = (item: HotSearch) => {
    setSearchKeyword(item.keyword);
    handleSearch();
  };

  const handleCancel = () => {
    if (showResults) {
      setShowResults(false);
    } else {
      setSearchKeyword('');
      setShowCancelButton(false);
      // 隐藏键盘
    }
  };

  const handleBack = () => {
    if (showResults) {
      setShowResults(false);
    } else {
      navigation.goBack();
    }
  };

  const handleClearSearch = () => {
    setSearchKeyword('');
  };

  const handleFilterApply = (selectedOptions: {
    [key: string]: FilterOption[];
  }) => {
    // 处理筛选条件应用
    console.log('应用筛选条件:', selectedOptions);

    // 重新加载搜索结果
    if (showResults) {
      setIsLoading(true);
      setTimeout(() => {
        setSearchResults(mockResults);
        setIsLoading(false);
      }, 800);
    }
  };

  const handleFilterReset = () => {
    // 处理重置筛选条件
  };

  const handleTabPress = (tab: string) => {
    setActiveTab(tab);
  };

  const renderSearchResultItem = ({ item }: { item: BidNotice }) => (
    <TouchableOpacity
      className="bg-white rounded-md p-4 mb-3"
      onPress={() => navigation.navigate('BidDetail', { id: item.id })}
    >
      <Text className="text-base font-medium mb-2" numberOfLines={2}>
        {item.title}
      </Text>
      <View className="flex-row mb-1">
        {item.type === '中标公告' ? (
          <View className="bg-red-100 px-2 py-0.5 rounded mr-2">
            <Text className="text-red-500 text-xs">{item.type}</Text>
          </View>
        ) : (
          <View className="bg-blue-100 px-2 py-0.5 rounded mr-2">
            <Text className="text-blue-500 text-xs">{item.type}</Text>
          </View>
        )}
        <View className="bg-yellow-50 px-2 py-0.5 rounded mr-2">
          <Text className="text-yellow-600 text-xs">行业</Text>
        </View>
        <View className="bg-yellow-50 px-2 py-0.5 rounded mr-2">
          <Text className="text-yellow-600 text-xs">{item.amount}</Text>
        </View>
        {item.bidder && (
          <View className="bg-green-50 px-2 py-0.5 rounded">
            <Text className="text-green-600 text-xs">{item.bidder}</Text>
          </View>
        )}
      </View>
      <Text className="text-gray-500 text-xs mb-1">
        中标单位: {item.company}
      </Text>
      {item.agent && (
        <Text className="text-gray-500 text-xs mb-1">
          代理单位: {item.agent}
        </Text>
      )}
      <View className="flex-row justify-between items-center mt-2">
        <View className="flex-row items-center">
          <Text className="text-blue-500 mr-1">📍</Text>
          <Text className="text-gray-500 text-xs">{item.location}</Text>
        </View>
        <Text className="text-gray-500 text-xs">{item.date}</Text>
      </View>
    </TouchableOpacity>
  );

  // 搜索页面内容
  const renderSearchContent = () => (
    <>
      {/* 标签栏 */}
      <View className="flex-row border-b border-gray-200">
        {tabs.map(tab => (
          <TouchableOpacity
            key={tab.id}
            className={`flex-1 items-center py-3 ${
              activeTab === tab.title ? 'border-b-2 border-blue-500' : ''
            }`}
            onPress={() => handleTabPress(tab.title)}
          >
            <Text
              className={`${
                activeTab === tab.title ? 'text-blue-500' : 'text-gray-600'
              }`}
            >
              {tab.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* 历史搜索和热门搜索 */}
      <ScrollView className="flex-1 bg-white">
        {/* 历史搜索 */}
        {searchHistories.length > 0 && (
          <View className="p-4">
            <View className="flex-row justify-between items-center mb-2">
              <View className="flex-row items-center">
                <Text className="text-gray-500 mr-2">🕒</Text>
                <Text className="font-medium text-gray-700">历史查询</Text>
              </View>
              <TouchableOpacity onPress={handleClearHistory}>
                <Image
                  source={require('../../assets/h2.png')}
                  className="w-5 h-5"
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </View>

            <View className="flex-row flex-wrap">
              {searchHistories.map(item => (
                <TouchableOpacity
                  key={item.id}
                  className="bg-gray-100 rounded-md px-3 py-2 mr-2 mb-2"
                  onPress={() => handleHistoryItemPress(item)}
                >
                  <Text className="text-gray-700">{item.keyword}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}

        {/* 热门搜索 */}
        <View className="p-4 bg-blue-50 flex-1">
          <View className="flex-row items-center mb-4">
            <Text className="text-red-500 mr-2">🔥</Text>
            <Text className="font-medium text-gray-700">热门搜索</Text>
          </View>

          <View>
            {hotSearches.map((item, index) => (
              <TouchableOpacity
                key={item.id}
                className="py-3 flex-row items-center"
                onPress={() => handleHotSearchPress(item)}
              >
                <View
                  className={`w-6 h-6 rounded-full items-center justify-center mr-2 ${
                    index < 3
                      ? index === 0
                        ? 'bg-yellow-500'
                        : index === 1
                        ? 'bg-gray-300'
                        : 'bg-amber-700'
                      : 'bg-gray-200'
                  }`}
                >
                  <Text
                    className={`text-center font-bold ${
                      index < 3 ? 'text-white' : 'text-gray-500'
                    }`}
                  >
                    {index + 1}
                  </Text>
                </View>
                {item.icon && (
                  <Image
                    source={item.icon}
                    className="w-6 h-6 mr-2"
                    resizeMode="contain"
                  />
                )}
                <Text className="text-gray-700">{item.keyword}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </>
  );

  // 搜索结果内容
  const renderResultsContent = () => (
    <View style={styles.container}>
      {/* 集成的筛选组件 */}
      <FilterDropdown
        filterMenuOptions={filterMenuOptions}
        getFilterGroupsByTab={(filterId: string) => {
          // 根据标签ID返回不同的筛选组
          const filterGroups: Record<string, FilterGroup[]> = {
            area: [
              {
                id: 'area',
                title: '地区',
                options: [
                  { id: 'beijing', name: '北京' },
                  { id: 'shanghai', name: '上海' },
                  { id: 'guangdong', name: '广东' },
                  { id: 'jiangsu', name: '江苏' },
                  { id: 'zhejiang', name: '浙江' },
                ],
                multiSelect: false,
                showTwoColumns: true,
              },
            ],
            industry: [
              {
                id: 'industry',
                title: '行业',
                options: [
                  { id: 'construction', name: '建筑' },
                  { id: 'medical', name: '医疗' },
                  { id: 'education', name: '教育' },
                  { id: 'it', name: '信息技术' },
                ],
                multiSelect: true,
              },
            ],
            type: [
              {
                id: 'type',
                title: '类型',
                options: [
                  { id: 'bidding', name: '招标公告' },
                  { id: 'winning', name: '中标公告' },
                  { id: 'qualification', name: '资格预审' },
                  { id: 'change', name: '变更公告' },
                ],
                multiSelect: true,
              },
            ],
            sort: [
              {
                id: 'sort',
                title: '排序',
                options: [
                  { id: 'time_desc', name: '发布时间从新到旧' },
                  { id: 'time_asc', name: '发布时间从旧到新' },
                  { id: 'amount_desc', name: '金额从高到低' },
                  { id: 'amount_asc', name: '金额从低到高' },
                ],
                multiSelect: false,
              },
            ],
          };
          return filterGroups[filterId] || [];
        }}
        onApply={handleFilterApply}
        onReset={handleFilterReset}
      />

      {/* 搜索结果部分 */}
      <View style={styles.resultsContainer}>
        {/* 搜索结果统计 */}
        <View className="flex-row items-center bg-blue-100 px-4 py-2">
          <View className="flex-row items-center">
            <View className="w-5 h-5 bg-blue-500 rounded-full items-center justify-center mr-2">
              <Text className="text-white text-xs">i</Text>
            </View>
            <Text className="text-blue-500">搜索到</Text>
            <Text className="text-red-500 font-bold mx-1">{totalResults}</Text>
            <Text className="text-blue-500">个结果</Text>
          </View>
          <View className="flex-1 items-end">
            <Text className="text-gray-500 text-xs">第1/10页</Text>
          </View>
        </View>

        {/* 加载指示器或搜索结果列表 */}
        {isLoading ? (
          <View className="flex-1 items-center justify-center bg-white">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="mt-4 text-gray-500">正在搜索中...</Text>
          </View>
        ) : (
          <FlatList
            data={searchResults}
            renderItem={renderSearchResultItem}
            keyExtractor={item => item.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20, padding: 16 }}
            ListEmptyComponent={() => (
              <View className="items-center justify-center py-10 bg-white">
                <Text className="text-gray-400">暂无搜索结果</Text>
              </View>
            )}
          />
        )}
      </View>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* 搜索栏 */}
      <View className="flex-row items-center px-4 py-2 bg-white">
        {showResults && (
          <TouchableOpacity onPress={handleBack} className="mr-2">
            <Text className="text-2xl text-gray-700">←</Text>
          </TouchableOpacity>
        )}
        <View className="flex-row items-center flex-1 bg-gray-100 rounded-full px-4 py-2">
          <Text className="text-gray-400 mr-2">🔍</Text>
          <TextInput
            className="flex-1 h-8"
            placeholder="请输入公司名称、关键词"
            value={searchKeyword}
            onChangeText={text => {
              setSearchKeyword(text);
              setShowCancelButton(true);
            }}
            returnKeyType="search"
            onSubmitEditing={handleSearch}
            onFocus={() => setShowCancelButton(true)}
            autoFocus={true}
            editable={!isLoading}
          />
          {searchKeyword.length > 0 && (
            <TouchableOpacity onPress={handleClearSearch} disabled={isLoading}>
              <View className="bg-gray-300 rounded-full w-4 h-4 items-center justify-center">
                <Text className="text-white text-xs">×</Text>
              </View>
            </TouchableOpacity>
          )}
        </View>
        {(showCancelButton || showResults) && (
          <TouchableOpacity
            className="ml-3 px-2"
            onPress={handleCancel}
            disabled={isLoading}
          >
            <Text className={`text-gray-700 ${isLoading ? 'opacity-50' : ''}`}>
              取消
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* 根据状态显示搜索页面或搜索结果 */}
      {showResults ? renderResultsContent() : renderSearchContent()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
    zIndex: 1,
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: 'white',
    zIndex: 1, // 确保搜索结果在筛选下拉菜单下方
  },
});

export default BidNoticeSearchScreen;
