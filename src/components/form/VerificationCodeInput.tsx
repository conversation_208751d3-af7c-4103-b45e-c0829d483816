import React, { useState, useRef, useEffect } from 'react';
import { View, TextInput, ViewStyle, StyleProp, Keyboard } from 'react-native';

interface VerificationCodeInputProps {
  codeLength?: number;
  onCodeFilled?: (code: string) => void;
  autoFocus?: boolean;
  secureTextEntry?: boolean;
  containerStyle?: StyleProp<ViewStyle>;
  disabled?: boolean;
}

const VerificationCodeInput: React.FC<VerificationCodeInputProps> = ({
  codeLength = 6,
  onCodeFilled,
  autoFocus = true,
  secureTextEntry = false,
  containerStyle,
  disabled = false,
}) => {
  const [code, setCode] = useState<string[]>(Array(codeLength).fill(''));
  const [focusedIndex, setFocusedIndex] = useState<number>(0);

  const inputRefs = useRef<Array<TextInput | null>>([]);

  useEffect(() => {
    // 初始化输入框引用
    inputRefs.current = inputRefs.current.slice(0, codeLength);

    // 如果设置了自动聚焦，则聚焦第一个输入框
    if (autoFocus && inputRefs.current[0]) {
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 100);
    }
  }, [codeLength, autoFocus]);

  useEffect(() => {
    // 当所有输入框都填满时，触发onCodeFilled回调
    const allFilled = code.every(digit => digit !== '');
    if (allFilled && onCodeFilled) {
      onCodeFilled(code.join(''));
      Keyboard.dismiss();
    }
  }, [code, onCodeFilled]);

  const handleChangeText = (text: string, index: number) => {
    // 处理粘贴的情况
    if (text.length > 1) {
      // 如果粘贴的文本长度大于1，则尝试填充所有输入框
      const pastedCode = text.split('').slice(0, codeLength);
      const newCode = [...code];

      pastedCode.forEach((digit, idx) => {
        if (index + idx < codeLength) {
          newCode[index + idx] = digit;
        }
      });

      setCode(newCode);

      // 聚焦到最后一个输入框或填充后的下一个空输入框
      const nextEmptyIndex = newCode.findIndex(digit => digit === '');
      if (nextEmptyIndex !== -1) {
        inputRefs.current[nextEmptyIndex]?.focus();
      } else {
        inputRefs.current[codeLength - 1]?.focus();
        Keyboard.dismiss();
      }
    } else {
      // 处理单个字符输入
      const newCode = [...code];
      newCode[index] = text;
      setCode(newCode);

      // 如果输入了字符，自动聚焦到下一个输入框
      if (text && index < codeLength - 1) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyPress = (e: any, index: number) => {
    // 处理删除键
    if (e.nativeEvent.key === 'Backspace' && !code[index] && index > 0) {
      // 如果当前输入框为空且按下删除键，则聚焦到前一个输入框并清空它
      const newCode = [...code];
      newCode[index - 1] = '';
      setCode(newCode);
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleFocus = (index: number) => {
    setFocusedIndex(index);
  };

  const handleBlur = () => {
    setFocusedIndex(-1);
  };

  const getCellStyles = (index: number) => {
    const isFocused = focusedIndex === index;

    // 修改为方形白色输入框
    let className =
      'w-12 h-12 bg-white rounded-md justify-center items-center mx-1 border border-solid border-[#C1CFFF]';

    if (isFocused) {
      className = `${className}   border-blue-500`;
    }

    return className;
  };

  return (
    <View
      className="flex-row justify-center items-center"
      style={containerStyle}
    >
      {Array(codeLength)
        .fill(0)
        .map((_, index) => (
          <View key={index} className={getCellStyles(index)}>
            <TextInput
              ref={ref => {
                inputRefs.current[index] = ref;
              }}
              className="text-center text-xl w-full h-full text-gray-800"
              keyboardType="number-pad"
              maxLength={1}
              selectTextOnFocus
              value={code[index]}
              onChangeText={text => handleChangeText(text, index)}
              onKeyPress={e => handleKeyPress(e, index)}
              onFocus={() => handleFocus(index)}
              onBlur={handleBlur}
              secureTextEntry={secureTextEntry}
              editable={!disabled}
              caretHidden
            />
          </View>
        ))}
    </View>
  );
};

export default VerificationCodeInput;
