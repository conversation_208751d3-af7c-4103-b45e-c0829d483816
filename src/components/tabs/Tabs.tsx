import React, { useRef, useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Animated,
  LayoutChangeEvent,
  Dimensions,
  StyleSheet,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

export interface TabItem {
  id: string;
  title: string;
  badge?: number;
}

export interface TabsProps {
  tabs: TabItem[];
  activeTab: string;
  onTabPress: (id: string) => void;
  scrollable?: boolean;
  underlineWidth?: number;
  underlineHeight?: number;
  useGradient?: boolean;
  gradientColors?: string[];
  underlineColor?: string;
  showBorderBottom?: boolean;
  borderBottomColor?: string;
  style?: {
    container?: object;
    tab?: object;
    activeTab?: object;
    tabText?: object;
    activeTabText?: object;
    badge?: object;
    underline?: object;
  };
}

// 定义静态样式
const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
  },
  containerFixed: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  containerScrollable: {
    flexDirection: 'row',
  },
  tab: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  tabFixed: {
    flex: 1,
    alignItems: 'center',
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabText: {
    color: '#4b5563',
  },
  activeTabText: {
    color: '#3b82f6',
  },
  badge: {
    marginLeft: 4,
    backgroundColor: '#ef4444',
    borderRadius: 9999,
    paddingHorizontal: 6,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
  },
  underline: {
    position: 'absolute',
    bottom: 0,
    overflow: 'hidden',
    borderRadius: 5,
  },
  gradient: {
    width: '100%',
    height: '100%',
    borderRadius: 5,
  },
});

const Tabs: React.FC<TabsProps> = ({
  tabs,
  activeTab,
  onTabPress,
  scrollable = false,
  underlineWidth = 26,
  underlineHeight = 4,
  useGradient = false,
  gradientColors = ['#FFFFFF', '#4B74FF'],
  underlineColor = '#3B82F6',
  showBorderBottom = false,
  borderBottomColor = '#e5e7eb',
  style = {},
}) => {
  const Container = scrollable ? ScrollView : View;
  const containerProps = scrollable
    ? {
        horizontal: true,
        showsHorizontalScrollIndicator: false,
      }
    : {};

  // 滚动视图引用
  const scrollViewRef = useRef<ScrollView>(null);

  // 存储每个标签的布局信息
  const tabLayouts = useRef<{
    [key: string]: { x: number; width: number; centerX: number };
  }>({});

  // 下划线动画值
  const underlineAnimation = useRef(new Animated.Value(0)).current;

  // 容器宽度
  const [containerWidth, setContainerWidth] = useState(
    Dimensions.get('window').width,
  );

  // 处理容器布局变化
  const handleContainerLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setContainerWidth(width);
  };

  // 处理标签布局变化
  const handleTabLayout = (tabId: string, event: LayoutChangeEvent) => {
    const { x, width } = event.nativeEvent.layout;
    const centerX = x + width / 2;
    tabLayouts.current[tabId] = { x, width, centerX };

    // 如果是当前活动标签，立即更新下划线位置
    if (tabId === activeTab) {
      updateUnderlinePosition(tabId);
    }
  };

  // 更新下划线位置
  const updateUnderlinePosition = useCallback(
    (tabId: string) => {
      const layout = tabLayouts.current[tabId];
      if (!layout) return;

      // 计算下划线位置（居中）
      const toValue = layout.centerX - (underlineWidth || 20) / 2;

      // 动画过渡到新位置
      Animated.timing(underlineAnimation, {
        toValue,
        duration: 300,
        useNativeDriver: true,
      }).start();
    },
    [underlineAnimation, underlineWidth],
  );

  // 滚动到选中的标签
  const scrollToTab = useCallback(
    (tabId: string) => {
      if (!scrollable || !scrollViewRef.current) return;

      const layout = tabLayouts.current[tabId];
      if (!layout) return;

      // 计算滚动位置，使标签居中
      const scrollToX = Math.max(0, layout.centerX - containerWidth / 2);

      // 滚动到目标位置
      scrollViewRef.current.scrollTo({
        x: scrollToX,
        animated: true,
      });
    },
    [scrollable, containerWidth],
  );

  // 当激活的标签改变时
  useEffect(() => {
    // 更新下划线位置
    updateUnderlinePosition(activeTab);

    // 滚动到选中的标签
    scrollToTab(activeTab);
  }, [activeTab, containerWidth, updateUnderlinePosition, scrollToTab]);

  // 渲染下划线
  const renderUnderline = () => {
    return (
      <Animated.View
        style={[
          styles.underline,
          style.underline,
          {
            width: underlineWidth,
            height: underlineHeight,
            borderRadius: Math.min(underlineHeight / 2, 5),
            transform: [
              {
                translateX: underlineAnimation,
              },
            ],
          },
        ]}
      >
        {useGradient ? (
          <LinearGradient
            colors={gradientColors}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={[
              styles.gradient,
              { borderRadius: Math.min(underlineHeight / 2, 5) },
            ]}
          />
        ) : (
          <View
            style={[
              styles.gradient,
              {
                backgroundColor: underlineColor,
                borderRadius: Math.min(underlineHeight / 2, 5),
              },
            ]}
          />
        )}
      </Animated.View>
    );
  };

  // 动态计算容器样式
  const getContainerStyle = () => {
    const borderStyle = showBorderBottom
      ? {
          borderBottomWidth: 1,
          borderBottomColor: borderBottomColor,
        }
      : {
          borderBottomWidth: 0,
        };

    return [
      styles.container,
      scrollable ? styles.containerScrollable : styles.containerFixed,
      borderStyle,
      style.container,
    ];
  };

  return (
    <View onLayout={handleContainerLayout}>
      <Container
        {...containerProps}
        ref={scrollable ? scrollViewRef : undefined}
        style={getContainerStyle()}
      >
        {tabs.map(tab => {
          const isActive = activeTab === tab.id;

          return (
            <TouchableOpacity
              key={tab.id}
              style={[
                styles.tab,
                !scrollable && styles.tabFixed,
                style.tab,
                isActive && style.activeTab,
              ]}
              onPress={() => onTabPress(tab.id)}
              onLayout={e => handleTabLayout(tab.id, e)}
            >
              <View style={styles.tabContent}>
                <Text
                  style={[
                    styles.tabText,
                    style.tabText,
                    isActive && styles.activeTabText,
                    isActive && style.activeTabText,
                  ]}
                >
                  {tab.title}
                </Text>
                {tab.badge !== undefined && tab.badge > 0 && (
                  <View style={[styles.badge, style.badge]}>
                    <Text style={styles.badgeText}>
                      {tab.badge > 99 ? '99+' : tab.badge}
                    </Text>
                  </View>
                )}
              </View>
            </TouchableOpacity>
          );
        })}

        {renderUnderline()}
      </Container>
    </View>
  );
};

export default Tabs;
