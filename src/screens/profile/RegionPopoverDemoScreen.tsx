import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Header from '../../components/common/Header';
import Popover from '../../components/common/Popover';
import { RegionPicker, RegionItem } from '../../components/region';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

type Props = {};

// 模拟地区数据
const mockRegions: RegionItem[] = [
  {
    id: 'henan',
    name: '河南省',
    children: [
      { id: 'henan_all', name: '全部' },
      { id: 'henan_zhengzhou', name: '郑州市' },
      { id: 'henan_luoyang', name: '洛阳市' },
    ],
  },
  {
    id: 'beijing',
    name: '北京市',
    count: 3,
    children: [
      { id: 'beijing_all', name: '全部' },
      { id: 'beijing_xicheng', name: '西城区' },
      { id: 'beijing_changping', name: '昌平区' },
      { id: 'beijing_huairou', name: '怀柔区' },
    ],
  },
  {
    id: 'tianjin',
    name: '天津市',
    children: [
      { id: 'tianjin_all', name: '全部' },
      { id: 'tianjin_heping', name: '和平区' },
      { id: 'tianjin_xiqing', name: '西青区' },
    ],
  },
  {
    id: 'hebei',
    name: '河北省',
    children: [
      { id: 'hebei_all', name: '全部' },
      { id: 'hebei_shijiazhuang', name: '石家庄市' },
      { id: 'hebei_tangshan', name: '唐山市' },
      { id: 'hebei_fangshan', name: '房山区' },
    ],
  },
  {
    id: 'shanxi',
    name: '山西省',
    children: [
      { id: 'shanxi_all', name: '全部' },
      { id: 'shanxi_taiyuan', name: '太原市' },
      { id: 'shanxi_datong', name: '大同市' },
    ],
  },
];

const RegionPopoverDemoScreen: React.FC<Props> = () => {
  const navigation = useNavigation();
  const [popoverVisible, setPopoverVisible] = useState(false);
  const [multiSelectPopoverVisible, setMultiSelectPopoverVisible] =
    useState(false);

  // 单选模式
  const [selectedRegion, setSelectedRegion] = useState<string | undefined>();
  const [selectedRegionName, setSelectedRegionName] =
    useState<string>('请选择地区');

  // 多选模式
  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);
  const [selectedRegionNames, setSelectedRegionNames] = useState<string[]>([]);

  // 打开单选地区选择器
  const openRegionPicker = () => {
    setPopoverVisible(true);
  };

  // 关闭单选地区选择器
  const closeRegionPicker = () => {
    setPopoverVisible(false);
  };

  // 打开多选地区选择器
  const openMultiSelectRegionPicker = () => {
    setMultiSelectPopoverVisible(true);
  };

  // 关闭多选地区选择器
  const closeMultiSelectRegionPicker = () => {
    setMultiSelectPopoverVisible(false);
  };

  // 处理单选地区选择
  const handleRegionSelect = (
    regionId: string,
    regionName: string,
    isLeaf: boolean,
  ) => {
    // 只有选择叶子节点时才自动关闭选择器
    if (isLeaf) {
      setSelectedRegion(regionId);
      setSelectedRegionName(regionName);
      closeRegionPicker();
    }
  };

  // 处理多选地区选择
  const handleToggleSelect = (regionId: string, selected: boolean) => {
    if (selected) {
      // 添加到选中列表
      setSelectedRegions(prev => [...prev, regionId]);

      // 查找地区名称
      let regionName = '';
      for (const province of mockRegions) {
        if (province.id === regionId) {
          regionName = province.name;
          break;
        }

        const city = province.children?.find(city => city.id === regionId);
        if (city) {
          regionName = city.name;
          break;
        }
      }

      if (regionName) {
        setSelectedRegionNames(prev => [...prev, regionName]);
      }
    } else {
      // 从选中列表移除
      setSelectedRegions(prev => prev.filter(id => id !== regionId));

      // 查找地区名称索引
      let regionName = '';
      for (const province of mockRegions) {
        if (province.id === regionId) {
          regionName = province.name;
          break;
        }

        const city = province.children?.find(city => city.id === regionId);
        if (city) {
          regionName = city.name;
          break;
        }
      }

      if (regionName) {
        setSelectedRegionNames(prev =>
          prev.filter(name => name !== regionName),
        );
      }
    }
  };

  // 确认多选
  const confirmMultiSelect = () => {
    closeMultiSelectRegionPicker();
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="地区选择器与Popover组合"
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>单选模式</Text>
          <Text style={styles.description}>
            点击选择地区，弹出Popover，选择后自动关闭
          </Text>

          <TouchableOpacity
            style={styles.selectButton}
            onPress={openRegionPicker}
          >
            <Text style={styles.selectButtonText}>{selectedRegionName}</Text>
            <Icon name="chevron-down" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>多选模式</Text>
          <Text style={styles.description}>
            点击选择地区，弹出Popover，可以选择多个地区
          </Text>

          <TouchableOpacity
            style={styles.selectButton}
            onPress={openMultiSelectRegionPicker}
          >
            <Text style={styles.selectButtonText}>
              {selectedRegionNames.length > 0
                ? `已选择 ${selectedRegionNames.length} 个地区`
                : '选择地区'}
            </Text>
            <Icon name="chevron-down" size={20} color="#FFFFFF" />
          </TouchableOpacity>

          {selectedRegionNames.length > 0 && (
            <View style={styles.resultContainer}>
              <Text style={styles.resultTitle}>已选择地区：</Text>
              <View style={styles.tagsContainer}>
                {selectedRegionNames.map((name, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{name}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>使用说明</Text>
          <Text style={styles.description}>
            1. RegionPicker 组件可以与 Popover 或其他组件组合使用
          </Text>
          <Text style={styles.description}>
            2. 单选模式下，选择叶子节点后自动关闭选择器
          </Text>
          <Text style={styles.description}>
            3. 多选模式下，可以选择多个地区，点击确定按钮关闭选择器
          </Text>
          <Text style={styles.description}>
            4. 组件支持联动选择，点击省份后显示对应的城市列表
          </Text>
        </View>
      </ScrollView>

      {/* 单选地区选择器 */}
      <Popover
        visible={popoverVisible}
        onClose={closeRegionPicker}
        position="bottom"
      >
        <View style={styles.popoverHeader}>
          <Text style={styles.popoverTitle}>选择地区</Text>
          <TouchableOpacity onPress={closeRegionPicker}>
            <Icon name="close" size={24} color="#666666" />
          </TouchableOpacity>
        </View>

        <RegionPicker
          regions={mockRegions}
          selectedRegion={selectedRegion}
          onSelectRegion={handleRegionSelect}
        />
      </Popover>

      {/* 多选地区选择器 */}
      <Popover
        visible={multiSelectPopoverVisible}
        onClose={closeMultiSelectRegionPicker}
        position="bottom"
      >
        <View style={styles.popoverHeader}>
          <Text style={styles.popoverTitle}>选择地区（多选）</Text>
          <TouchableOpacity onPress={closeMultiSelectRegionPicker}>
            <Icon name="close" size={24} color="#666666" />
          </TouchableOpacity>
        </View>

        <RegionPicker
          regions={mockRegions}
          showCheckbox={true}
          selectedRegions={selectedRegions}
          onToggleSelect={handleToggleSelect}
        />

        <View style={styles.popoverFooter}>
          <TouchableOpacity
            style={styles.confirmButton}
            onPress={confirmMultiSelect}
          >
            <Text style={styles.confirmButtonText}>
              确定（已选 {selectedRegions.length} 个）
            </Text>
          </TouchableOpacity>
        </View>
      </Popover>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F7FA',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333333',
  },
  description: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
    lineHeight: 20,
  },
  selectButton: {
    backgroundColor: '#4B74FF',
    borderRadius: 6,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  selectButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  resultContainer: {
    marginTop: 20,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 10,
    color: '#333333',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    backgroundColor: '#F0F7FF',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E6EFFD',
  },
  tagText: {
    color: '#4B74FF',
    fontSize: 14,
  },
  popoverHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  popoverTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  popoverFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  confirmButton: {
    backgroundColor: '#4B74FF',
    borderRadius: 6,
    padding: 12,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default RegionPopoverDemoScreen;
