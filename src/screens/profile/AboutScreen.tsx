import React from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Linking,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ProfileStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';
import Card from '../../components/common/Card';

type Props = NativeStackScreenProps<ProfileStackParamList, 'About'>;

interface Feature {
  id: string;
  title: string;
  description: string;
  icon: string;
}

const AboutScreen: React.FC<Props> = ({ navigation }) => {
  const appVersion = '1.0.0';

  // 功能特点
  const features: Feature[] = [
    {
      id: '1',
      title: '企业信息一站式查询',
      description: '涵盖企业基本信息、资质、业绩、专利、人员等多维度数据',
      icon: '🏢',
    },
    {
      id: '2',
      title: '工程建设领域专注',
      description: '针对建筑、市政、交通等工程建设领域提供精准数据服务',
      icon: '🏗️',
    },
    {
      id: '3',
      title: '企业图谱关系可视化',
      description: '直观展示企业间的投资、合作、竞争等关系网络',
      icon: '🔍',
    },
    {
      id: '4',
      title: '实时监控动态更新',
      description: '关注企业动态，第一时间获取资质变更、业绩发布等信息',
      icon: '📊',
    },
  ];

  const handleOpenPrivacyPolicy = () => {
    // 打开隐私政策页面
    Linking.openURL('https://example.com/privacy-policy');
  };

  const handleOpenUserAgreement = () => {
    // 打开用户协议页面
    Linking.openURL('https://example.com/user-agreement');
  };

  const handleCheckUpdate = () => {
    // 检查更新逻辑
    // 实际项目中需要调用检查更新的API
    console.log('检查更新');
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <Header
        title="关于我们"
        showBackButton
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView className="flex-1 px-4">
        {/* 应用信息 */}
        <View className="items-center mt-6 mb-8">
          <Image
            source={require('@assets/h1.png')}
            className="w-20 h-20 rounded-xl mb-2"
            resizeMode="contain"
          />
          <Text className="text-xl font-bold">企业图谱</Text>
          <Text className="text-gray-500 mt-1">版本 {appVersion}</Text>
        </View>

        {/* 应用介绍 */}
        <Card className="p-4 mb-6">
          <Text className="text-base mb-4">
            企业图谱是一款专注于工程建设领域的企业信息查询平台，致力于为工程建设领域的从业者提供全面、专业、精准的企业信息数据服务。
          </Text>
          <Text className="text-base">
            通过企业图谱，您可以轻松获取建筑、市政、交通等工程建设领域企业的全面信息，包括企业资质、业绩、专利、人员等多维度数据，帮助您更好地了解市场动态，把握商业机会。
          </Text>
        </Card>

        {/* 功能特点 */}
        <View className="mb-6">
          <Text className="text-lg font-bold mb-4">功能特点</Text>
          {features.map(feature => (
            <View key={feature.id} className="flex-row mb-4">
              <Text className="text-2xl mr-3">{feature.icon}</Text>
              <View className="flex-1">
                <Text className="font-medium text-base">{feature.title}</Text>
                <Text className="text-gray-600 mt-1">
                  {feature.description}
                </Text>
              </View>
            </View>
          ))}
        </View>

        {/* 联系信息 */}
        <View className="mb-6">
          <Text className="text-lg font-bold mb-4">联系我们</Text>
          <Card className="p-4">
            <View className="mb-3">
              <Text className="text-gray-600 mb-1">客服邮箱</Text>
              <Text className="text-base"><EMAIL></Text>
            </View>
            <View className="mb-3">
              <Text className="text-gray-600 mb-1">客服热线</Text>
              <Text className="text-base">400-123-4567</Text>
            </View>
            <View>
              <Text className="text-gray-600 mb-1">公司地址</Text>
              <Text className="text-base">北京市朝阳区某某大厦12层</Text>
            </View>
          </Card>
        </View>

        {/* 法律信息 */}
        <View className="mb-8">
          <Text className="text-lg font-bold mb-4">法律信息</Text>
          <View className="flex-row justify-between mb-3">
            <TouchableOpacity
              className="bg-gray-100 py-3 px-6 rounded-lg flex-1 mr-2 items-center"
              onPress={handleOpenPrivacyPolicy}
            >
              <Text className="text-gray-700">隐私政策</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className="bg-gray-100 py-3 px-6 rounded-lg flex-1 ml-2 items-center"
              onPress={handleOpenUserAgreement}
            >
              <Text className="text-gray-700">用户协议</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 检查更新 */}
        <View className="mb-8 items-center">
          <TouchableOpacity
            className="bg-blue-500 py-3 px-8 rounded-lg"
            onPress={handleCheckUpdate}
          >
            <Text className="text-white font-medium">检查更新</Text>
          </TouchableOpacity>
        </View>

        {/* 版权信息 */}
        <View className="mb-8 items-center">
          <Text className="text-gray-400 text-sm text-center">
            © 2023 某某科技有限公司 版权所有
          </Text>
          <Text className="text-gray-400 text-sm text-center mt-1">
            All Rights Reserved
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default AboutScreen;
