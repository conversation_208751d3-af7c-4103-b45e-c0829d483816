import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ScrollView,
  SafeAreaView,
  Image,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { GraphStackParamList } from '@navigation/types';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

type Props = NativeStackScreenProps<GraphStackParamList, 'Graph'>;

const GraphScreen: React.FC<Props> = ({ navigation }) => {
  // 热搜标签
  const hotSearchTags = ['小米', '百度'];

  // 渲染热搜标签
  const renderHotSearchTags = () => {
    return (
      <View className="flex-row mt-2 px-4">
        <View className="bg-orange-500 px-2 py-1 rounded-sm mr-2">
          <Text className="text-white text-xs">热搜</Text>
        </View>
        {hotSearchTags.map((tag, index) => (
          <TouchableOpacity key={index} className="mr-4">
            <Text className="text-white">{tag}</Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  // 渲染企业卡片
  const renderCompanyCard = (name: string, percent: string) => {
    return (
      <View className="bg-white rounded-md p-2 mr-2 mb-2">
        <Text className="text-xs">{name}</Text>
        <Text className="text-xs text-gray-400">{percent}</Text>
      </View>
    );
  };

  // 渲染图谱部分
  const renderGraphSection = (
    title: string,
    description: string,
    companies?: { name: string; percent: string }[],
    image?: any,
    imageBackground?: string,
    showDiagram?: boolean,
    isCompanyGraph?: boolean,
  ) => {
    return (
      <View className="bg-white p-4 mb-4 rounded-md">
        <Text className="text-lg font-bold mb-1">{title}</Text>
        <Text className="text-sm text-gray-600 mb-3">{description}</Text>

        {showDiagram && (
          <View className="flex-row mb-3">
            {companies && companies.length > 0 && (
              <View className="flex-1 flex-row flex-wrap">
                {companies.map((company, _index) =>
                  renderCompanyCard(company.name, company.percent),
                )}
              </View>
            )}
            {image && (
              <View
                className="w-20 h-20 rounded-md items-center justify-center"
                style={{ backgroundColor: imageBackground || '#4169E1' }}
              >
                <Image
                  source={image}
                  className="w-14 h-14"
                  resizeMode="contain"
                />
              </View>
            )}
          </View>
        )}

        <TouchableOpacity
          className="bg-blue-500 py-2 rounded-md items-center"
          style={{ alignSelf: 'flex-end', width: 80 }}
          onPress={() => {
            if (isCompanyGraph) {
              // 在根导航中，先导航到Detail导航器，然后导航到CompanyGraph页面
              navigation.getParent()?.navigate('Detail', {
                screen: 'CompanyGraph',
                params: { id: '123' }, // 这里可以传入一个示例ID，或者从搜索框获取的企业ID
              });
            } else {
              // 其他图谱的查询处理
              console.log(`查询${title}`);
            }
          }}
        >
          <Text className="text-white">查询</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-100">
      {/* 蓝色背景标题 */}
      <View className="bg-blue-500 pt-4 pb-6">
        <Text className="text-white text-xl font-medium text-center mb-4">
          企业图谱
        </Text>

        {/* 搜索框 */}
        <View className="mx-4 bg-white rounded-full flex-row items-center px-4 py-2">
          <Icon name="magnify" size={20} color="#999" />
          <TextInput
            className="flex-1 ml-2 text-base"
            placeholder="请输入企业名称"
            placeholderTextColor="#999"
          />
        </View>

        {/* 热搜标签 */}
        {renderHotSearchTags()}
      </View>

      <ScrollView className="flex-1 px-4 pt-4">
        {/* 企业招投标图谱 */}
        {renderGraphSection(
          '企业招投标图谱',
          '挖掘企业与项目之间的商业关系',
          [
            { name: '北京xx有限公司', percent: '中标率：47%' },
            { name: '北京xx有限公司', percent: '中标率：67%' },
            { name: '北京xx有限公司', percent: '中标率：23%' },
          ],
          undefined,
          undefined,
          true,
        )}

        {/* 股权穿透图 */}
        {renderGraphSection(
          '股权穿透图',
          '精准分析企业背后的股权结构',
          [
            { name: '刘总', percent: '持股：50%' },
            { name: '刘总', percent: '持股：50%' },
          ],
          undefined,
          undefined,
          true,
        )}

        {/* 企业关系图谱 */}
        {renderGraphSection(
          '企业关系图谱',
          '完整的企业关系网',
          undefined,
          require('@assets/h1.png'),
          '#FF6347',
          true,
          true, // 标记为企业关系图谱
        )}

        {/* 投融资关系图谱 */}
        {renderGraphSection(
          '投融资关系图谱',
          '洞察企业投融资链路',
          undefined,
          undefined,
          undefined,
          true,
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default GraphScreen;
