import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Tabs, TabContent, TabItem } from '../../components/tabs';
import Header from '../../components/common/Header';

type Props = {};

const TabsDemoScreen: React.FC<Props> = () => {
  const navigation = useNavigation();
  const [activeBasicTab, setActiveBasicTab] = useState('tab1');
  const [activeScrollableTab, setActiveScrollableTab] = useState('scroll1');
  const [activeBadgeTab, setActiveBadgeTab] = useState('badge1');
  const [activeCustomTab, setActiveCustomTab] = useState('custom1');
  const [activeWidthTab, setActiveWidthTab] = useState('width1');
  const [activeLongScrollTab, setActiveLongScrollTab] = useState('long1');
  const [activeAutoScrollTab, setActiveAutoScrollTab] = useState('auto1');
  const [autoScrollIndex, setAutoScrollIndex] = useState(0);

  // 基础标签页
  const basicTabs: TabItem[] = [
    { id: 'tab1', title: '标签一' },
    { id: 'tab2', title: '标签二' },
    { id: 'tab3', title: '标签三' },
  ];

  // 可滚动标签页
  const scrollableTabs: TabItem[] = [
    { id: 'scroll1', title: '标签一' },
    { id: 'scroll2', title: '标签二' },
    { id: 'scroll3', title: '标签三' },
    { id: 'scroll4', title: '标签四' },
    { id: 'scroll5', title: '标签五' },
    { id: 'scroll6', title: '标签六' },
    { id: 'scroll7', title: '标签七' },
  ];

  // 长内容滚动标签页（用于展示滚动动画）
  const longScrollTabs: TabItem[] = [
    { id: 'long1', title: '第一项' },
    { id: 'long2', title: '第二项' },
    { id: 'long3', title: '第三项' },
    { id: 'long4', title: '第四项' },
    { id: 'long5', title: '第五项' },
    { id: 'long6', title: '第六项' },
    { id: 'long7', title: '第七项' },
    { id: 'long8', title: '第八项' },
    { id: 'long9', title: '第九项' },
    { id: 'long10', title: '第十项' },
    { id: 'long11', title: '第十一项' },
    { id: 'long12', title: '第十二项' },
  ];

  // 自动滚动标签页（用于展示下划线动画）
  const autoScrollTabs = useMemo(
    () => [
      { id: 'auto1', title: '标签1' },
      { id: 'auto2', title: '标签2' },
      { id: 'auto3', title: '较长的标签3' },
      { id: 'auto4', title: '标签4' },
      { id: 'auto5', title: '非常长的标签5' },
      { id: 'auto6', title: '标签6' },
      { id: 'auto7', title: '标签7' },
      { id: 'auto8', title: '标签8' },
      { id: 'auto9', title: '标签9' },
      { id: 'auto10', title: '标签10' },
    ],
    [],
  );

  // 带徽章的标签页
  const badgeTabs: TabItem[] = [
    { id: 'badge1', title: '未读消息', badge: 5 },
    { id: 'badge2', title: '已读消息', badge: 0 },
    { id: 'badge3', title: '全部消息', badge: 120 },
  ];

  // 自定义样式标签页
  const customTabs: TabItem[] = [
    { id: 'custom1', title: '推荐' },
    { id: 'custom2', title: '最新' },
    { id: 'custom3', title: '热门' },
  ];

  // 不同下划线宽度的标签页
  const widthTabs: TabItem[] = [
    { id: 'width1', title: '窄下划线' },
    { id: 'width2', title: '中等下划线' },
    { id: 'width3', title: '宽下划线' },
  ];

  // 自动滚动效果
  useEffect(() => {
    const interval = setInterval(() => {
      const nextIndex = (autoScrollIndex + 1) % autoScrollTabs.length;
      setAutoScrollIndex(nextIndex);
      setActiveAutoScrollTab(autoScrollTabs[nextIndex].id);
    }, 1500);

    return () => clearInterval(interval);
  }, [autoScrollIndex, autoScrollTabs]);

  // 自定义样式
  const customStyle = {
    container: { backgroundColor: '#f0f9ff' },
    tab: { borderRadius: 4, marginHorizontal: 4 },
    activeTab: { backgroundColor: '#dbeafe' },
    activeTabText: { color: '#2563eb', fontWeight: 'bold' },
    underline: { backgroundColor: '#2563eb' },
  };

  // 快速跳转按钮
  const renderQuickJumpButtons = () => {
    return (
      <View className="flex-row flex-wrap justify-center p-4 bg-gray-50">
        {longScrollTabs.map(tab => (
          <TouchableOpacity
            key={tab.id}
            className={`m-1 px-3 py-1.5 rounded-full ${
              activeLongScrollTab === tab.id ? 'bg-blue-500' : 'bg-gray-200'
            }`}
            onPress={() => setActiveLongScrollTab(tab.id)}
          >
            <Text
              className={
                activeLongScrollTab === tab.id ? 'text-white' : 'text-gray-700'
              }
            >
              {tab.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-100">
      <Header title="Tabs 组件演示" onBackPress={() => navigation.goBack()} />
      <ScrollView className="flex-1">
        <View className="p-4 mt-6">
          <Text className="text-lg font-bold mb-2">自动滚动演示</Text>
          <Text className="text-gray-500 mb-4">
            下划线自动滚动动画效果，每个标签下划线居中显示
          </Text>
        </View>

        <Tabs
          tabs={autoScrollTabs}
          activeTab={activeAutoScrollTab}
          onTabPress={setActiveAutoScrollTab}
          scrollable
          underlineWidth={30}
          style={{
            underline: { backgroundColor: '#8b5cf6', height: 3 },
          }}
        />

        <TabContent
          activeTab={activeAutoScrollTab}
          tabId={autoScrollTabs[autoScrollIndex].id}
        >
          <View className="p-4 bg-purple-50">
            <Text>当前选中: {autoScrollTabs[autoScrollIndex].title}</Text>
            <Text className="mt-2 text-gray-500">
              下划线会自动滚动并居中显示在标签下方，无论标签宽度如何
            </Text>
          </View>
        </TabContent>

        <View className="p-4">
          <Text className="text-lg font-bold mb-2">基础标签页</Text>
          <Text className="text-gray-500 mb-4">
            固定宽度的标签页，均分容器宽度，带动画下划线
          </Text>
        </View>

        <Tabs
          tabs={basicTabs}
          activeTab={activeBasicTab}
          onTabPress={setActiveBasicTab}
        />

        <TabContent activeTab={activeBasicTab} tabId="tab1">
          <View className="p-4">
            <Text>这是标签一的内容</Text>
          </View>
        </TabContent>

        <TabContent activeTab={activeBasicTab} tabId="tab2">
          <View className="p-4">
            <Text>这是标签二的内容</Text>
          </View>
        </TabContent>

        <TabContent activeTab={activeBasicTab} tabId="tab3">
          <View className="p-4">
            <Text>这是标签三的内容</Text>
          </View>
        </TabContent>

        <View className="p-4 mt-6">
          <Text className="text-lg font-bold mb-2">可滚动标签页</Text>
          <Text className="text-gray-500 mb-4">
            当标签数量较多时，可以横向滚动查看
          </Text>
        </View>

        <Tabs
          tabs={scrollableTabs}
          activeTab={activeScrollableTab}
          onTabPress={setActiveScrollableTab}
          scrollable
        />

        {scrollableTabs.map(tab => (
          <TabContent
            key={tab.id}
            activeTab={activeScrollableTab}
            tabId={tab.id}
          >
            <View className="p-4">
              <Text>这是{tab.title}的内容</Text>
            </View>
          </TabContent>
        ))}

        <View className="p-4 mt-6">
          <Text className="text-lg font-bold mb-2">长内容滚动标签页</Text>
          <Text className="text-gray-500 mb-4">
            标签数量更多，点击标签时会自动滚动并居中显示选中的标签
          </Text>
        </View>

        <Tabs
          tabs={longScrollTabs}
          activeTab={activeLongScrollTab}
          onTabPress={setActiveLongScrollTab}
          scrollable
          underlineWidth={30}
        />

        {renderQuickJumpButtons()}

        {longScrollTabs.map(tab => (
          <TabContent
            key={tab.id}
            activeTab={activeLongScrollTab}
            tabId={tab.id}
          >
            <View className="p-4">
              <Text>这是{tab.title}的内容</Text>
              <Text className="mt-2 text-gray-500">
                点击上方的快速跳转按钮，可以看到标签栏自动滚动并居中显示选中的标签
              </Text>
            </View>
          </TabContent>
        ))}

        <View className="p-4 mt-6">
          <Text className="text-lg font-bold mb-2">带徽章的标签页</Text>
          <Text className="text-gray-500 mb-4">
            标签上可以显示未读数量等徽章
          </Text>
        </View>

        <Tabs
          tabs={badgeTabs}
          activeTab={activeBadgeTab}
          onTabPress={setActiveBadgeTab}
        />

        {badgeTabs.map(tab => (
          <TabContent key={tab.id} activeTab={activeBadgeTab} tabId={tab.id}>
            <View className="p-4">
              <Text>这是{tab.title}的内容</Text>
              {tab.badge !== undefined && tab.badge > 0 && (
                <Text className="mt-2 text-gray-500">
                  您有 {tab.badge} {tab.badge > 99 ? '条以上' : '条'} 未读消息
                </Text>
              )}
            </View>
          </TabContent>
        ))}

        <View className="p-4 mt-6">
          <Text className="text-lg font-bold mb-2">自定义样式标签页</Text>
          <Text className="text-gray-500 mb-4">可以自定义标签页的样式</Text>
        </View>

        <Tabs
          tabs={customTabs}
          activeTab={activeCustomTab}
          onTabPress={setActiveCustomTab}
          style={customStyle}
        />

        {customTabs.map(tab => (
          <TabContent key={tab.id} activeTab={activeCustomTab} tabId={tab.id}>
            <View className="p-4">
              <Text>这是{tab.title}内容区域</Text>
            </View>
          </TabContent>
        ))}

        <View className="p-4 mt-6">
          <Text className="text-lg font-bold mb-2">不同下划线宽度</Text>
          <Text className="text-gray-500 mb-4">可以自定义下划线的宽度</Text>
        </View>

        <View className="mb-4">
          <Tabs
            tabs={widthTabs.slice(0, 1)}
            activeTab={activeWidthTab}
            onTabPress={setActiveWidthTab}
            underlineWidth={15}
            style={{
              underline: { backgroundColor: '#ef4444' },
            }}
          />
        </View>

        <View className="mb-4">
          <Tabs
            tabs={widthTabs.slice(1, 2)}
            activeTab={activeWidthTab}
            onTabPress={setActiveWidthTab}
            underlineWidth={30}
            style={{
              underline: { backgroundColor: '#f59e0b' },
            }}
          />
        </View>

        <View className="mb-4">
          <Tabs
            tabs={widthTabs.slice(2, 3)}
            activeTab={activeWidthTab}
            onTabPress={setActiveWidthTab}
            underlineWidth={50}
            style={{
              underline: { backgroundColor: '#10b981' },
            }}
          />
        </View>

        {widthTabs.map(tab => (
          <TabContent key={tab.id} activeTab={activeWidthTab} tabId={tab.id}>
            <View className="p-4">
              <Text>这是{tab.title}的内容</Text>
            </View>
          </TabContent>
        ))}

        <View className="h-20" />
      </ScrollView>
    </SafeAreaView>
  );
};

export default TabsDemoScreen;
