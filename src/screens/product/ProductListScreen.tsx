import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { GraphStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';
import SearchBar from '../../components/common/SearchBar';
import Card from '../../components/common/Card';

type Props = NativeStackScreenProps<GraphStackParamList, 'ProductList'>;

interface Product {
  id: string;
  name: string;
  company: {
    id: string;
    name: string;
  };
  category: string;
  introduction: string;
  features: string[];
  tags: string[];
}

const ProductListScreen: React.FC<Props> = ({ navigation, route }) => {
  const { category, title } = route.params || {
    category: 'all',
    title: '产品列表',
  };
  const [searchText, setSearchText] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  // 模拟产品数据
  const products: Product[] = [
    {
      id: '101',
      name: '智能建筑系统',
      company: {
        id: '10',
        name: '某科技有限公司',
      },
      category: '智能化系统',
      introduction:
        '基于物联网技术的智能建筑管理系统，可实现对建筑环境、能源、安全等多方面的智能监控和管理',
      features: ['智能环境监控', '能源管理', '安全管理', '设备远程控制'],
      tags: ['智能', '物联网', '节能'],
    },
    {
      id: '102',
      name: '节能建筑材料',
      company: {
        id: '11',
        name: '某新材料有限公司',
      },
      category: '建筑材料',
      introduction:
        '高性能保温隔热建筑材料，具有优异的保温隔热性能，能有效降低建筑能耗',
      features: ['高效保温', '防火阻燃', '环保无害', '使用寿命长'],
      tags: ['节能', '环保', '新材料'],
    },
    {
      id: '103',
      name: '建筑抗震系统',
      company: {
        id: '12',
        name: '某工程技术有限公司',
      },
      category: '建筑结构系统',
      introduction:
        '提高建筑抗震性能的结构系统，采用先进的减震隔震技术，有效提升建筑物的抗震能力',
      features: ['高效减震', '适应性强', '安装便捷', '维护成本低'],
      tags: ['抗震', '安全', '结构'],
    },
    {
      id: '104',
      name: '绿色建筑设计方案',
      company: {
        id: '2',
        name: '某建筑设计院有限公司',
      },
      category: '建筑设计',
      introduction:
        '符合绿色建筑标准的设计解决方案，从规划、设计到材料选择等各个环节考虑环保和可持续性',
      features: ['生态环保', '资源节约', '健康舒适', '可持续发展'],
      tags: ['绿色建筑', '可持续', '设计方案'],
    },
    {
      id: '105',
      name: '建筑信息模型系统',
      company: {
        id: '13',
        name: '某软件科技有限公司',
      },
      category: '软件系统',
      introduction:
        'BIM系统软件，支持建筑全生命周期的信息管理，提高设计、施工和运维效率',
      features: ['三维可视化', '协同设计', '碰撞检测', '施工模拟'],
      tags: ['BIM', '信息化', '管理'],
    },
    {
      id: '106',
      name: '装配式建筑系统',
      company: {
        id: '14',
        name: '某建工科技有限公司',
      },
      category: '建筑工法',
      introduction:
        '高效的装配式建筑解决方案，通过工厂预制和现场拼装，大幅提高建造效率和质量',
      features: ['工厂化生产', '标准化设计', '装配化施工', '信息化管理'],
      tags: ['装配式', '高效', '标准化'],
    },
  ];

  // 根据搜索文本和分类筛选产品
  const filteredProducts = products.filter(product => {
    const matchesSearch =
      searchText === '' ||
      product.name.includes(searchText) ||
      product.introduction.includes(searchText) ||
      product.tags.some(tag => tag.includes(searchText));

    const matchesCategory =
      category === 'all' ||
      (category === 'intelligent' && product.category.includes('智能')) ||
      (category === 'material' && product.category.includes('材料')) ||
      (category === 'design' && product.category.includes('设计')) ||
      (category === 'software' && product.category.includes('软件'));

    return matchesSearch && matchesCategory;
  });

  const handleSearch = () => {
    // 实现搜索逻辑
  };

  const handleProductPress = (id: string) => {
    navigation.navigate('Detail', { screen: 'ProductDetail', params: { id } });
  };

  const handleCompanyPress = (id: string) => {
    navigation.navigate('Detail', { screen: 'CompanyDetail', params: { id } });
  };

  const handleRefresh = () => {
    setRefreshing(true);
    // 模拟刷新数据
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const renderProductItem = ({ item }: { item: Product }) => (
    <TouchableOpacity onPress={() => handleProductPress(item.id)}>
      <Card className="mb-3 p-4">
        <View className="flex-row justify-between items-start">
          <View className="flex-1">
            <Text className="text-lg font-bold">{item.name}</Text>
            <TouchableOpacity
              onPress={() => handleCompanyPress(item.company.id)}
            >
              <Text className="text-blue-500 mt-1">{item.company.name}</Text>
            </TouchableOpacity>
            <Text className="text-gray-500 text-sm mt-1">
              分类: {item.category}
            </Text>
          </View>
        </View>

        <Text className="text-gray-600 text-sm mt-2" numberOfLines={2}>
          {item.introduction}
        </Text>

        <View className="flex-row flex-wrap mt-2">
          {item.features.slice(0, 3).map((feature, index) => (
            <View
              key={index}
              className="bg-blue-50 rounded-full px-2 py-0.5 mr-1 mb-1"
            >
              <Text className="text-blue-500 text-xs">{feature}</Text>
            </View>
          ))}
          {item.features.length > 3 && (
            <Text className="text-gray-400 text-xs ml-1 mt-0.5">
              等{item.features.length}项特点
            </Text>
          )}
        </View>

        <View className="flex-row flex-wrap mt-2">
          {item.tags.map((tag, index) => (
            <View
              key={index}
              className="bg-gray-100 rounded-full px-2 py-0.5 mr-1 mb-1"
            >
              <Text className="text-gray-600 text-xs">{tag}</Text>
            </View>
          ))}
        </View>
      </Card>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-white">
      <Header
        title={title}
        showBackButton
        onBackPress={() => navigation.goBack()}
      />

      {/* 搜索栏 */}
      <SearchBar
        value={searchText}
        onChangeText={setSearchText}
        onSearch={handleSearch}
        onClear={() => setSearchText('')}
      />

      {/* 筛选条件 */}
      <View className="flex-row border-b border-gray-200 py-2 px-4 bg-white">
        <TouchableOpacity className="flex-row items-center mr-4">
          <Text className="text-gray-700">分类</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-row items-center mr-4">
          <Text className="text-gray-700">特点</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-row items-center">
          <Text className="text-gray-700">排序</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
      </View>

      {/* 产品列表 */}
      <FlatList
        data={filteredProducts}
        renderItem={renderProductItem}
        keyExtractor={item => item.id}
        contentContainerStyle={{ padding: 16 }}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => (
          <View className="items-center justify-center py-10">
            <Text className="text-gray-400">暂无符合条件的产品</Text>
          </View>
        )}
      />
    </SafeAreaView>
  );
};

export default ProductListScreen;
