import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { GraphStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';
import SearchBar from '../../components/common/SearchBar';
import Card from '../../components/common/Card';

type Props = NativeStackScreenProps<GraphStackParamList, 'PersonList'>;

interface Person {
  id: string;
  name: string;
  title: string;
  company: {
    id: string;
    name: string;
    position: string;
  };
  certifications: string[];
  experience: string;
  education: string;
  projects: number;
  patents: number;
  description: string;
  tags: string[];
}

const PersonListScreen: React.FC<Props> = ({ navigation, route }) => {
  const { category, title } = route.params || {
    category: 'all',
    title: '人员列表',
  };
  const [searchText, setSearchText] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  // 模拟人员数据
  const persons: Person[] = [
    {
      id: '301',
      name: '张工程',
      title: '高级工程师',
      company: {
        id: '1',
        name: '某建筑集团有限公司',
        position: '技术总监',
      },
      certifications: ['一级建造师', '注册结构工程师'],
      experience: '15年',
      education: '清华大学 土木工程 硕士',
      projects: 12,
      patents: 3,
      description:
        '从事建筑工程领域多年，具有丰富的项目管理和技术经验，主导过多个大型建筑项目',
      tags: ['建筑工程', '项目管理', '结构设计'],
    },
    {
      id: '302',
      name: '李设计',
      title: '建筑师',
      company: {
        id: '2',
        name: '某建筑设计院有限公司',
        position: '首席设计师',
      },
      certifications: ['一级注册建筑师', '城市规划师'],
      experience: '12年',
      education: '同济大学 建筑学 硕士',
      projects: 18,
      patents: 2,
      description:
        '专注于建筑设计和城市规划，设计风格独特，多个作品获得行业奖项',
      tags: ['建筑设计', '城市规划', '绿色建筑'],
    },
    {
      id: '303',
      name: '王项目',
      title: '项目经理',
      company: {
        id: '3',
        name: '某市政工程有限公司',
        position: '项目经理',
      },
      certifications: ['一级建造师', 'PMP项目管理专业人士'],
      experience: '10年',
      education: '华南理工大学 工程管理 学士',
      projects: 15,
      patents: 0,
      description:
        '擅长项目管理和团队协作，主持过多个市政工程项目，项目管理能力突出',
      tags: ['项目管理', '市政工程', '团队协作'],
    },
    {
      id: '304',
      name: '赵技术',
      title: '技术专家',
      company: {
        id: '14',
        name: '某建筑科技有限公司',
        position: '研发总监',
      },
      certifications: ['高级工程师', '注册岩土工程师'],
      experience: '18年',
      education: '浙江大学 岩土工程 博士',
      projects: 8,
      patents: 12,
      description:
        '建筑技术领域的专家，主要研究方向为建筑节能和智能建筑，拥有多项专利',
      tags: ['建筑技术', '研发创新', '专利'],
    },
    {
      id: '305',
      name: '钱水利',
      title: '水利工程师',
      company: {
        id: '5',
        name: '某水利水电工程有限公司',
        position: '技术主管',
      },
      certifications: ['注册水利水电工程师', '水工结构工程师'],
      experience: '14年',
      education: '武汉大学 水利工程 硕士',
      projects: 10,
      patents: 1,
      description: '专注于水利水电工程设计和施工，参与过多个大型水利枢纽工程',
      tags: ['水利工程', '水电工程', '水工结构'],
    },
    {
      id: '306',
      name: '孙质量',
      title: '质量工程师',
      company: {
        id: '1',
        name: '某建筑集团有限公司',
        position: '质量总监',
      },
      certifications: ['注册质量工程师', '安全工程师'],
      experience: '11年',
      education: '重庆大学 土木工程 学士',
      projects: 20,
      patents: 0,
      description: '负责工程质量控制和安全管理，对工程质量标准和规范有深入理解',
      tags: ['质量控制', '安全管理', '标准规范'],
    },
  ];

  // 根据搜索文本和分类筛选人员
  const filteredPersons = persons.filter(person => {
    const matchesSearch =
      searchText === '' ||
      person.name.includes(searchText) ||
      person.description.includes(searchText) ||
      person.company.name.includes(searchText) ||
      person.tags.some(tag => tag.includes(searchText)) ||
      person.certifications.some(cert => cert.includes(searchText));

    const matchesCategory =
      category === 'all' ||
      (category === 'engineer' &&
        (person.title.includes('工程师') || person.title.includes('技术'))) ||
      (category === 'architect' &&
        (person.title.includes('建筑师') || person.title.includes('设计'))) ||
      (category === 'manager' &&
        (person.title.includes('经理') || person.title.includes('总监'))) ||
      (category === 'expert' &&
        (person.title.includes('专家') || person.tags.includes('研发创新')));

    return matchesSearch && matchesCategory;
  });

  const handleSearch = () => {
    // 实现搜索逻辑
  };

  const handlePersonPress = (id: string) => {
    navigation.navigate('Detail', { screen: 'PersonDetail', params: { id } });
  };

  const handleCompanyPress = (id: string) => {
    navigation.navigate('Detail', { screen: 'CompanyDetail', params: { id } });
  };

  const handleRefresh = () => {
    setRefreshing(true);
    // 模拟刷新数据
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const renderPersonItem = ({ item }: { item: Person }) => (
    <TouchableOpacity onPress={() => handlePersonPress(item.id)}>
      <Card className="mb-3 p-4">
        <View className="flex-row justify-between items-start">
          <View className="flex-1">
            <Text className="text-lg font-bold">{item.name}</Text>
            <Text className="text-gray-500 mt-1">
              {item.title} | {item.experience}
            </Text>
            <TouchableOpacity
              onPress={() => handleCompanyPress(item.company.id)}
            >
              <Text className="text-blue-500 mt-1">
                {item.company.name} - {item.company.position}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <Text className="text-gray-600 text-sm mt-2" numberOfLines={2}>
          {item.description}
        </Text>

        <View className="mt-2">
          <Text className="text-gray-500 text-xs">
            教育背景: {item.education}
          </Text>
        </View>

        <View className="flex-row mt-2">
          {item.certifications.map((cert, index) => (
            <View
              key={index}
              className="bg-blue-50 rounded-full px-2 py-0.5 mr-1 mb-1"
            >
              <Text className="text-blue-500 text-xs">{cert}</Text>
            </View>
          ))}
        </View>

        <View className="flex-row justify-between mt-2">
          <Text className="text-gray-500 text-xs">项目: {item.projects}个</Text>
          <Text className="text-gray-500 text-xs">专利: {item.patents}项</Text>
        </View>

        <View className="flex-row flex-wrap mt-2">
          {item.tags.map((tag, index) => (
            <View
              key={index}
              className="bg-gray-100 rounded-full px-2 py-0.5 mr-1 mb-1"
            >
              <Text className="text-gray-600 text-xs">{tag}</Text>
            </View>
          ))}
        </View>
      </Card>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-white">
      <Header
        title={title}
        showBackButton
        onBackPress={() => navigation.goBack()}
      />

      {/* 搜索栏 */}
      <SearchBar
        value={searchText}
        onChangeText={setSearchText}
        onSearch={handleSearch}
        onClear={() => setSearchText('')}
      />

      {/* 筛选条件 */}
      <View className="flex-row border-b border-gray-200 py-2 px-4 bg-white">
        <TouchableOpacity className="flex-row items-center mr-4">
          <Text className="text-gray-700">职称</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-row items-center mr-4">
          <Text className="text-gray-700">资质</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-row items-center mr-4">
          <Text className="text-gray-700">经验</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-row items-center">
          <Text className="text-gray-700">排序</Text>
          <Text className="text-gray-400 ml-1">▼</Text>
        </TouchableOpacity>
      </View>

      {/* 人员列表 */}
      <FlatList
        data={filteredPersons}
        renderItem={renderPersonItem}
        keyExtractor={item => item.id}
        contentContainerStyle={{ padding: 16 }}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => (
          <View className="items-center justify-center py-10">
            <Text className="text-gray-400">暂无符合条件的人员</Text>
          </View>
        )}
      />
    </SafeAreaView>
  );
};

export default PersonListScreen;
