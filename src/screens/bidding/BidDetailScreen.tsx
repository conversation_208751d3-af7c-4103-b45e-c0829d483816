import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';

type Props = NativeStackScreenProps<RootStackParamList, 'BidDetail'>;

const BidDetailScreen: React.FC<Props> = ({ navigation, route }) => {
  const { id } = route.params;

  // 模拟招标详情数据
  const bidDetail = {
    id: id,
    title: '北京亿达腾飞科技有限公司招标公告',
    type: '招标公告',
    amount: '20000元',
    publishDate: '2025-05-05',
    endDate: '2025-06-05',
    company: 'xxx有限责任公司（自然人投资或控股）',
    agent: 'xxx有限责任公司（自然人投资或控股）',
    location: '湖南 长沙',
    contactPerson: '张先生',
    contactPhone: '138****1234',
    content: `一、项目基本情况
招标人：xxx有限责任公司
项目名称：北京亿达腾飞科技有限公司招标公告
项目编号：BJYD20250505
项目地点：湖南省长沙市岳麓区
项目预算：20000元
招标内容：软件开发服务，包括但不限于系统设计、开发、测试、部署和维护。

二、投标人资格要求
1. 具有独立法人资格，持有有效的营业执照；
2. 具有相关行业软件开发资质证书；
3. 近三年内无重大违法记录；
4. 具有类似项目经验。

三、投标截止时间
2025年6月5日17:00前，逾期不予受理。

四、联系方式
联系人：张先生
联系电话：138****1234
电子邮箱：<EMAIL>`,
    attachments: ['招标文件.pdf', '技术要求.docx'],
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-100">
      <Header title="招标详情" showBackButton={true} />

      <ScrollView className="flex-1 p-4">
        {/* 标题 */}
        <Text className="text-xl font-bold mb-4">{bidDetail.title}</Text>

        {/* 基本信息 */}
        <View className="bg-white rounded-md p-4 mb-4">
          <View className="flex-row mb-2">
            <View className="bg-blue-100 px-2 py-0.5 rounded mr-2">
              <Text className="text-blue-500 text-xs">{bidDetail.type}</Text>
            </View>
            <View className="bg-yellow-50 px-2 py-0.5 rounded">
              <Text className="text-yellow-600 text-xs">
                {bidDetail.amount}
              </Text>
            </View>
          </View>

          <View className="border-t border-gray-100 pt-2 mt-2">
            <View className="flex-row justify-between py-1">
              <Text className="text-gray-500">发布日期</Text>
              <Text className="text-gray-700">{bidDetail.publishDate}</Text>
            </View>
            <View className="flex-row justify-between py-1">
              <Text className="text-gray-500">截止日期</Text>
              <Text className="text-gray-700">{bidDetail.endDate}</Text>
            </View>
            <View className="flex-row justify-between py-1">
              <Text className="text-gray-500">招标单位</Text>
              <Text className="text-gray-700">{bidDetail.company}</Text>
            </View>
            {bidDetail.agent && (
              <View className="flex-row justify-between py-1">
                <Text className="text-gray-500">代理单位</Text>
                <Text className="text-gray-700">{bidDetail.agent}</Text>
              </View>
            )}
            <View className="flex-row justify-between py-1">
              <Text className="text-gray-500">项目地点</Text>
              <Text className="text-gray-700">{bidDetail.location}</Text>
            </View>
          </View>
        </View>

        {/* 联系方式 */}
        <View className="bg-white rounded-md p-4 mb-4">
          <Text className="font-bold mb-2">联系方式</Text>
          <View className="flex-row justify-between py-1">
            <Text className="text-gray-500">联系人</Text>
            <Text className="text-gray-700">{bidDetail.contactPerson}</Text>
          </View>
          <View className="flex-row justify-between py-1">
            <Text className="text-gray-500">联系电话</Text>
            <Text className="text-gray-700">{bidDetail.contactPhone}</Text>
          </View>
        </View>

        {/* 招标内容 */}
        <View className="bg-white rounded-md p-4 mb-4">
          <Text className="font-bold mb-2">招标内容</Text>
          <Text className="text-gray-700 leading-6">{bidDetail.content}</Text>
        </View>

        {/* 附件 */}
        <View className="bg-white rounded-md p-4 mb-4">
          <Text className="font-bold mb-2">招标附件</Text>
          {bidDetail.attachments.map((attachment, index) => (
            <TouchableOpacity
              key={index}
              className="flex-row items-center py-2 border-b border-gray-100"
            >
              <Text className="text-blue-500 mr-2">📎</Text>
              <Text className="text-blue-500">{attachment}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      {/* 底部操作按钮 */}
      <View className="flex-row p-4 bg-white border-t border-gray-200">
        <TouchableOpacity className="flex-1 py-3 mr-2 items-center justify-center border border-blue-500 rounded-md">
          <Text className="text-blue-500">收藏</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-1 py-3 ml-2 items-center justify-center bg-blue-500 rounded-md">
          <Text className="text-white">联系招标方</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default BidDetailScreen;
