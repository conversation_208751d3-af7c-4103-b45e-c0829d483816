import React, { useState, useRef, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Platform,
  Image,
  ImageBackground,
  StyleSheet,
  Animated,
  Easing,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { HomeStackParamList, RootStackParamList } from '../../navigation/types';
import SearchBar from '../../components/common/SearchBar';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import LocationIcon from '@assets/svg/location.svg';

type Props = NativeStackScreenProps<HomeStackParamList, 'Home'>;
type RootNavigationProp = NativeStackNavigationProp<RootStackParamList>;

// 工具项的类型定义
interface ToolItem {
  id: string;
  name: string;
  icon: any;
  onPress: () => void;
  badge?: string;
}

const HomeScreen: React.FC<Props> = () => {
  const [activeTab, setActiveTab] = useState('company'); // 'company' 或 'bidding'
  const [activeDataTab, setActiveDataTab] = useState('package'); // 'package' 或 'ranking'

  const navigation = useNavigation<RootNavigationProp>();

  // 动画相关
  const scrollY = useRef(new Animated.Value(0)).current;
  const [showFixedSearchBar, setShowFixedSearchBar] = useState(false);
  const [showStickyFilter, setShowStickyFilter] = useState(false);
  const tabIndicatorAnim = useRef(new Animated.Value(0)).current;

  // 存储每个标签的布局信息
  const tabLayouts = useRef<{
    [key: string]: { x: number; width: number; centerX: number };
  }>({});

  // 滚动监听
  const handleScroll = Animated.event(
    [{ nativeEvent: { contentOffset: { y: scrollY } } }],
    {
      useNativeDriver: false,
      listener: (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        // 当滚动超过100px时显示固定搜索栏
        setShowFixedSearchBar(offsetY > 100);
        // 当滚动超过250px且在标讯tab时显示吸顶筛选区域
        // 250px大约是头部区域 + 工具区域的高度
        setShowStickyFilter(offsetY > 250 && activeTab === 'bidding');
      },
    },
  );

  // 更新下划线位置 - 参考Tabs组件实现
  const updateUnderlinePosition = useCallback(
    (tabId: string) => {
      const layout = tabLayouts.current[tabId];
      if (!layout) return;

      // 计算下划线位置（居中）
      const underlineWidth = 33; // 指示器宽度
      const toValue = layout.centerX - underlineWidth / 2;

      // 动画过渡到新位置
      Animated.timing(tabIndicatorAnim, {
        toValue,
        duration: 300,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }).start();
    },
    [tabIndicatorAnim],
  );

  // 处理标签布局变化
  const handleTabLayout = (tabId: string, event: any) => {
    const { x, width } = event.nativeEvent.layout;
    const centerX = x + width / 2;
    tabLayouts.current[tabId] = { x, width, centerX };

    // 如果是当前活动标签，立即更新下划线位置
    if (tabId === activeTab) {
      updateUnderlinePosition(tabId);
    }
  };

  // 处理tab切换
  const handleTabPress = (tabId: string) => {
    setActiveTab(tabId);
    updateUnderlinePosition(tabId);
    // 切换到企业查询时隐藏吸顶筛选区域
    if (tabId === 'company') {
      setShowStickyFilter(false);
    }
  };

  // 计算工具项位置
  const getToolItemPosition = (index: number) => {
    const itemsPerRow = 3;
    const itemWidth = 70;
    const itemHeight = 76;
    const horizontalSpacing = 8; // 水平间距
    const verticalSpacing = 8; // 垂直间距

    const row = Math.floor(index / itemsPerRow);
    const col = index % itemsPerRow;

    const left = col * (itemWidth + horizontalSpacing);
    const top = row * (itemHeight + verticalSpacing);

    return { left, top };
  };

  // 热搜词标签
  const hotSearchTags = [
    { id: '1', name: '招投' },
    { id: '2', name: '装修' },
    { id: '3', name: '体检' },
    { id: '4', name: '银行' },
    { id: '5', name: '医保' },
    { id: '6', name: '智慧医院' },
  ];

  // 企业查询工具数据
  const companyToolItems: ToolItem[] = [
    {
      id: '1',
      name: '查产品',
      icon: require('@assets/home/<USER>'),
      onPress: () => navigation.navigate('SearchResult', { type: 'product' }),
    },
    {
      id: '2',
      name: '查业绩',
      icon: require('@assets/home/<USER>'),
      onPress: () =>
        navigation.navigate('SearchResult', { type: 'performance' }),
    },
    {
      id: '3',
      name: '查人员',
      icon: require('@assets/home/<USER>'),
      onPress: () => navigation.navigate('SearchResult', { type: 'person' }),
    },
    {
      id: '4',
      name: '查资质',
      icon: require('@assets/home/<USER>'),
      onPress: () =>
        navigation.navigate('SearchResult', { type: 'qualification' }),
    },
    {
      id: '5',
      name: '查专利',
      icon: require('@assets/home/<USER>'),
      onPress: () => navigation.navigate('SearchResult', { type: 'patent' }),
    },
    {
      id: '6',
      name: '更多工具',
      icon: require('@assets/home/<USER>'),
      onPress: () => {},
      badge: '即将上线',
    },
  ];

  // 标讯工具数据
  const biddingToolItems: ToolItem[] = [
    {
      id: '1',
      name: '查标讯',
      icon: require('@assets/home/<USER>'),
      onPress: () => navigation.navigate('BidNotice'),
    },
    {
      id: '2',
      name: '查业主',
      icon: require('@assets/home/<USER>'),
      onPress: () => navigation.navigate('OwnerSearch'),
    },
    {
      id: '3',
      name: '查渠道',
      icon: require('@assets/home/<USER>'),
      onPress: () => {},
    },
    {
      id: '4',
      name: '查代理',
      icon: require('@assets/home/<USER>'),
      onPress: () => {},
    },
  ];

  // 潜客数据包
  const dataPackages = [
    {
      id: '1',
      title: '失信被执行人企业',
      description: '有失信被执行人的企业',
      count: '100w+',
      type: 'dishonest',
    },
    {
      id: '2',
      title: '失信被执行人企业',
      description: '有失信被执行人的企业',
      count: '100w+',
      type: 'dishonest',
    },
    {
      id: '3',
      title: '失信被执行人企业',
      description: '有失信被执行人的企业',
      count: '100w+',
      type: 'dishonest',
    },
    {
      id: '4',
      title: '失信被执行人企业',
      description: '有失信被执行人的企业',
      count: '100w+',
      type: 'dishonest',
    },
  ];

  // 企业榜单数据
  const companyRankings = [
    {
      id: '1',
      title: '科技型中小企业',
      description:
        '在《国家重点支持的高新技术领域》内，持续进行研究开发与技术成果转化，形成企业核心自主知识产权，并以此为基础开展经营活动的居民企业，是科技型中小企业。',
      count: '10000',
      iconBg: '#4F7CFF',
      iconText: '科技型',
    },
    {
      id: '2',
      title: '科技型中小企业',
      description:
        '在《国家重点支持的高新技术领域》内，持续进行研究开发与技术成果转化，形成企业核心自主知识产权，并以此为基础开展经营活动的居民企业，是科技型中小企业。',
      count: '10000',
      iconBg: '#52C41A',
      iconText: '科技型',
    },
    {
      id: '3',
      title: '科技型中小企业',
      description:
        '在《国家重点支持的高新技术领域》内，持续进行研究开发与技术成果转化，形成企业核心自主知识产权，并以此为基础开展经营活动的居民企业，是科技型中小企业。',
      count: '10000',
      iconBg: '#FF8C00',
      iconText: '科技型',
    },
    {
      id: '4',
      title: '科技型中小企业',
      description:
        '在《国家重点支持的高新技术领域》内，持续进行研究开发与技术成果转化，形成企业核心自主知识产权，并以此为基础开展经营活动的居民企业，是科技型中小企业。',
      count: '10000',
      iconBg: '#FF4D4F',
      iconText: '科技型',
    },
  ];

  // 最新标讯数据
  const latestBiddings = [
    {
      id: '1',
      title:
        '北京亿达腾飞科技有限公司北京亿达腾飞腾飞科技有限公司腾飞科技有腾飞...',
      type: '中标公告',
      amount: '20000元',
      bidder: '2个联系人',
      companies: [
        { label: '中标单位', name: 'xxx有限责任公司（自然人投资或控股）' },
      ],
      location: '湖南 长沙',
      date: '10:00',
    },
    {
      id: '2',
      title:
        '北京亿达腾飞科技有限公司北京亿达腾飞腾飞科技有限公司腾飞科技有腾飞...',
      type: '招标公告',
      amount: '20000元',
      companies: [
        { label: '招标单位', name: 'xxx有限责任公司（自然人投资或控股）' },
        { label: '代理单位', name: 'xxx有限责任公司（自然人投资或控股）' },
      ],
      location: '湖南 长沙',
      date: '2025-05-05',
    },
    {
      id: '3',
      title: '上海智慧城市建设项目信息化系统集成采购招标公告',
      type: '招标公告',
      amount: '500万元',
      companies: [
        { label: '招标单位', name: '上海市政府采购中心' },
        { label: '代理单位', name: '上海招标有限公司' },
      ],
      location: '上海 浦东',
      date: '2025-01-15',
    },
    {
      id: '4',
      title: '深圳市南山区智能交通管理系统升级改造项目',
      type: '中标公告',
      amount: '1200万元',
      bidder: '5个联系人',
      companies: [{ label: '中标单位', name: '深圳智能科技股份有限公司' }],
      location: '广东 深圳',
      date: '14:30',
    },
    {
      id: '5',
      title: '杭州市西湖区教育信息化平台建设采购项目',
      type: '招标公告',
      amount: '800万元',
      companies: [
        { label: '招标单位', name: '杭州市西湖区教育局' },
        { label: '代理单位', name: '浙江省政府采购中心' },
      ],
      location: '浙江 杭州',
      date: '2025-01-20',
    },
    {
      id: '6',
      title: '成都高新区数字化园区管理平台开发项目中标结果公示',
      type: '中标公告',
      amount: '350万元',
      bidder: '3个联系人',
      companies: [{ label: '中标单位', name: '四川数字科技有限责任公司' }],
      location: '四川 成都',
      date: '09:15',
    },
    {
      id: '7',
      title: '武汉市江汉区政务服务大厅智能化改造工程招标',
      type: '招标公告',
      amount: '280万元',
      companies: [
        { label: '招标单位', name: '武汉市江汉区政务服务局' },
        { label: '代理单位', name: '湖北省招标集团有限公司' },
      ],
      location: '湖北 武汉',
      date: '2025-01-25',
    },
    {
      id: '8',
      title: '西安市雁塔区智慧社区综合服务平台建设项目',
      type: '中标公告',
      amount: '680万元',
      bidder: '4个联系人',
      companies: [{ label: '中标单位', name: '陕西智慧城市建设集团有限公司' }],
      location: '陕西 西安',
      date: '16:45',
    },
  ];

  // 渲染工具区域
  const renderToolsSection = () => {
    if (activeTab === 'company') {
      // 企业查询工具 - 原有样式
      return (
        <ImageBackground
          source={require('@assets/home/<USER>')}
          style={styles.toolsContainer}
          resizeMode="stretch"
        >
          <View style={styles.toolsContent}>
            {/* 企业查询工具图片 */}
            <View style={styles.companyToolsImageContainer}>
              <Image
                source={require('@assets/home/<USER>')}
                style={styles.companyToolsImage}
              />
              <Text style={styles.companyToolsText}>企业查询工具</Text>
            </View>
            {/* 工具网格 */}
            <View style={styles.toolsGrid}>
              {companyToolItems.map((item, index) => {
                const position = getToolItemPosition(index);
                return (
                  <TouchableOpacity
                    key={item.id}
                    style={[
                      styles.toolItem,
                      { left: position.left, top: position.top },
                    ]}
                    onPress={item.onPress}
                  >
                    <View style={styles.toolIconContainer}>
                      <Image
                        source={item.icon}
                        style={styles.toolIcon}
                        resizeMode="contain"
                      />
                      {item.badge && (
                        <View style={styles.badge}>
                          <Text style={styles.badgeText}>{item.badge}</Text>
                        </View>
                      )}
                    </View>
                    <Text style={styles.toolName}>{item.name}</Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
        </ImageBackground>
      );
    } else {
      // 标讯查询工具 - 新样式
      return (
        <View style={styles.biddingToolsContainer}>
          <View style={styles.biddingToolsRow}>
            {biddingToolItems.map(item => (
              <TouchableOpacity
                key={item.id}
                style={styles.biddingToolItem}
                onPress={item.onPress}
              >
                <View style={styles.biddingToolIconContainer}>
                  <Image
                    source={item.icon}
                    style={styles.biddingToolIcon}
                    resizeMode="contain"
                  />
                </View>
                <Text style={styles.biddingToolName}>{item.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      );
    }
  };

  // 渲染数据切换区域
  const renderDataTabsSection = () => {
    return (
      <View style={styles.dataTabsContainer}>
        <View style={styles.dataTabsWrapper}>
          <TouchableOpacity
            style={[
              styles.dataTabButton,
              activeDataTab === 'ranking' && styles.leftTabOffset,
              activeDataTab === 'package' && styles.activeTabButton,
            ]}
            onPress={() => setActiveDataTab('package')}
          >
            <ImageBackground
              source={
                activeDataTab === 'package'
                  ? require('@assets/home/<USER>')
                  : require('@assets/home/<USER>')
              }
              style={[
                styles.dataTabImage,
                activeDataTab === 'package'
                  ? styles.activeDataTabImage
                  : styles.inactiveDataTabImage,
              ]}
              resizeMode="stretch"
            >
              <Text
                style={[
                  styles.dataTabText,
                  activeDataTab === 'package' && styles.activeDataTabText,
                ]}
              >
                潜客数据包
              </Text>
            </ImageBackground>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.dataTabButton,
              activeDataTab === 'package' && styles.rightTabOffset,
              activeDataTab === 'ranking' && styles.activeTabButton,
            ]}
            onPress={() => setActiveDataTab('ranking')}
          >
            <ImageBackground
              source={
                activeDataTab === 'ranking'
                  ? require('@assets/home/<USER>')
                  : require('@assets/home/<USER>')
              }
              style={[
                styles.dataTabImage,
                activeDataTab === 'ranking'
                  ? styles.activeDataTabImage
                  : styles.inactiveDataTabImage,
              ]}
              resizeMode="stretch"
            >
              <Text
                style={[
                  styles.dataTabText,
                  activeDataTab === 'ranking' && styles.activeDataTabText,
                ]}
              >
                企业榜单
              </Text>
            </ImageBackground>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // 渲染数据内容
  const renderDataContent = () => {
    if (activeDataTab === 'package') {
      return (
        <View style={styles.dataContentContainer}>
          {dataPackages.map(item => (
            <TouchableOpacity key={item.id} style={styles.packageItem}>
              <View style={styles.dataItemContent}>
                <Text style={styles.dataItemTitle}>{item.title}</Text>
                <Text style={styles.dataItemDescription}>
                  {item.description}
                </Text>
                <Text style={styles.dataItemCount}>
                  企业数量:{' '}
                  <Text style={styles.dataItemCountNumber}>{item.count}</Text>
                </Text>
              </View>
              <Image
                source={require('@assets/icon_more.png')}
                style={styles.dataItemArrow}
                resizeMode="contain"
              />
            </TouchableOpacity>
          ))}
        </View>
      );
    } else {
      return (
        <View style={styles.dataContentContainer}>
          {companyRankings.map(item => (
            <TouchableOpacity key={item.id} style={styles.dataItem}>
              <View style={styles.dataItemTop}>
                <View
                  style={[
                    styles.dataItemIcon,
                    { backgroundColor: item.iconBg },
                  ]}
                >
                  <Text style={styles.dataItemIconText}>{item.iconText}</Text>
                </View>
                <View style={styles.dataItemContent}>
                  <Text style={styles.dataItemTitle}>{item.title}</Text>
                  <Text style={styles.dataItemCount}>
                    企业数量：
                    <Text style={styles.dataItemCountNumber}>{item.count}</Text>
                  </Text>
                </View>
                <Image
                  source={require('@assets/icon_more.png')}
                  style={styles.dataItemArrow}
                  resizeMode="contain"
                />
              </View>
              <Text style={styles.dataItemDescription} numberOfLines={3}>
                {item.description}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      );
    }
  };

  // 渲染标讯内容
  const renderBiddingContent = () => {
    return (
      <View style={styles.biddingContentContainer}>
        {/* 筛选区域 */}
        <View style={styles.filterContainer}>
          <View style={styles.filterRow}>
            {/* 左侧标签带渐变装饰条 */}
            <View style={styles.filterLabelContainer}>
              <LinearGradient
                colors={['#8ea8ff', '#4b74ff']}
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 1 }}
                style={styles.filterLabelBorder}
              />
              <View style={styles.filterLabel}>
                <Text style={styles.filterLabelText}>最新标讯</Text>
              </View>
            </View>

            {/* 右侧筛选按钮 */}
            <View style={styles.filterButtons}>
              <TouchableOpacity style={styles.filterDropdown}>
                <Text style={styles.filterDropdownText}>信息类型</Text>
                <Image
                  source={require('@assets/icon_arrow_bottom.png')}
                  style={styles.filterDropdownArrow}
                  resizeMode="contain"
                />
              </TouchableOpacity>
              <TouchableOpacity style={styles.filterDropdown}>
                <Text style={styles.filterDropdownText}>地区</Text>
                <Image
                  source={require('@assets/icon_arrow_bottom.png')}
                  style={styles.filterDropdownArrow}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* 标讯列表 */}
        <View style={styles.biddingListContainer}>
          {latestBiddings.map(item => (
            <TouchableOpacity key={item.id} style={styles.biddingItem}>
              <Text style={styles.biddingTitle} numberOfLines={2}>
                {item.title}
              </Text>
              <View style={styles.biddingTags}>
                <LinearGradient
                  colors={
                    item.type === '中标公告'
                      ? ['#FE8484', '#FF5555']
                      : ['#82ACFF', '#4B74FF']
                  }
                  style={styles.biddingTypeTag}
                  start={{ x: 0, y: 1 }}
                  end={{ x: 0, y: 0 }}
                  angle={270}
                >
                  <View style={styles.biddingTagInner}>
                    <Text style={styles.biddingTagText}>{item.type}</Text>
                  </View>
                </LinearGradient>
                {item.type === '中标公告' && (
                  <View style={[styles.biddingTag, styles.biddingIndustryTag]}>
                    <Text style={styles.biddingIndustryText}>行业</Text>
                  </View>
                )}
                {item.amount && (
                  <View style={styles.biddingAmountTag}>
                    <Text style={styles.biddingAmountText}>{item.amount}</Text>
                  </View>
                )}
                {item.bidder && (
                  <View style={styles.biddingBidderTag}>
                    <Text style={styles.biddingBidderText}>{item.bidder}</Text>
                  </View>
                )}
              </View>
              <View style={styles.biddingCompaniesContainer}>
                {item.companies.map((company, index) => (
                  <Text
                    key={index}
                    style={[
                      styles.biddingCompany,
                      index === item.companies.length - 1 &&
                        styles.biddingCompanyLast,
                    ]}
                    numberOfLines={1}
                  >
                    <Text style={styles.biddingCompanyLabel}>
                      {company.label}:{' '}
                    </Text>
                    <Text style={styles.biddingCompanyName}>
                      {company.name}
                    </Text>
                  </Text>
                ))}
              </View>
              <View style={styles.biddingFooter}>
                <View style={styles.biddingLocationContainer}>
                  <LocationIcon
                    width={16}
                    height={16}
                    style={styles.biddingLocationIcon}
                  />
                  <Text style={styles.biddingLocation}>{item.location}</Text>
                </View>
                <Text style={styles.biddingDate}>{item.date}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  // 获取状态栏高度
  const statusBarHeight =
    Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0;

  // 动态样式
  const dynamicStyles = StyleSheet.create({
    headerContainerDynamic: {
      paddingTop: statusBarHeight + 20,
    },
  });

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />

      <Animated.ScrollView
        style={styles.container}
        bounces={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
        {/* 顶部背景区域 */}
        <ImageBackground
          source={require('@assets/home/<USER>')}
          style={[styles.headerContainer, dynamicStyles.headerContainerDynamic]}
          resizeMode="cover"
        >
          {/* Tab切换 */}
          <View style={styles.tabContainer}>
            <View style={styles.tabWrapper}>
              <TouchableOpacity
                style={styles.tab}
                onPress={() => handleTabPress('company')}
                onLayout={event => handleTabLayout('company', event)}
              >
                <Text
                  style={[
                    styles.tabText,
                    activeTab === 'company' && styles.activeTabText,
                  ]}
                >
                  查企业
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.tab}
                onPress={() => handleTabPress('bidding')}
                onLayout={event => handleTabLayout('bidding', event)}
              >
                <Text
                  style={[
                    styles.tabText,
                    activeTab === 'bidding' && styles.activeTabText,
                  ]}
                >
                  查标讯
                </Text>
              </TouchableOpacity>

              {/* 渐变指示器 */}
              <Animated.View
                style={[
                  styles.tabIndicatorContainer,
                  {
                    transform: [
                      {
                        translateX: tabIndicatorAnim,
                      },
                    ],
                  },
                ]}
              >
                <LinearGradient
                  colors={['#ffffff', '#4b74ff']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={styles.tabIndicator}
                />
              </Animated.View>
            </View>
          </View>

          {/* 搜索框 */}
          <View style={styles.searchBarContainer}>
            <SearchBar
              value=""
              onChangeText={() => {}}
              onSearch={() => {}}
              placeholder={
                activeTab === 'company'
                  ? '请输入名称、关键词'
                  : '请输入项目名称、项目编号等关键词'
              }
              onPressSearchBar={() => {
                if (activeTab === 'company') {
                  navigation.navigate('CompanySearch');
                } else {
                  navigation.navigate('BidNoticeSearch', { keyword: '' });
                }
              }}
            />
          </View>
          {/* 热搜词 */}
          <View style={styles.hotSearchContainer}>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.hotSearchScroll}
              contentContainerStyle={styles.hotSearchScrollContent}
            >
              <Image
                source={require('@assets/home/<USER>')}
                style={styles.hotSearchIcon}
                resizeMode="contain"
              />
              {hotSearchTags.map(tag => (
                <TouchableOpacity key={tag.id} style={[styles.hotSearchTag]}>
                  <Text style={[styles.hotSearchTagText]}>{tag.name}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </ImageBackground>

        {/* 工具区域 */}
        {renderToolsSection()}

        {/* 根据activeTab显示不同内容 */}
        {activeTab === 'company' ? (
          <>
            {/* 数据切换区域 */}
            {renderDataTabsSection()}

            {/* 数据内容区域 */}
            {renderDataContent()}

            {/* 查看更多按钮 */}
            <TouchableOpacity style={styles.moreButton}>
              <Text style={styles.moreButtonText}>查看更多 {'>'}</Text>
            </TouchableOpacity>
          </>
        ) : (
          <>
            {/* 标讯内容区域 */}
            {renderBiddingContent()}
          </>
        )}
      </Animated.ScrollView>

      {/* 固定搜索栏 */}
      {showFixedSearchBar && (
        <Animated.View style={styles.fixedSearchBarContainer}>
          <ImageBackground
            source={require('@assets/home/<USER>')}
            style={styles.fixedSearchBarBackground}
            resizeMode="cover"
          >
            <View style={styles.fixedSearchBarContent}>
              <SearchBar
                value=""
                onChangeText={() => {}}
                onSearch={() => {}}
                placeholder={
                  activeTab === 'company'
                    ? '请输入名称、关键词'
                    : '请输入项目名称、项目编号等关键词'
                }
                onPressSearchBar={() => {
                  if (activeTab === 'company') {
                    navigation.navigate('CompanySearch');
                  } else {
                    navigation.navigate('BidNoticeSearch', { keyword: '' });
                  }
                }}
              />
            </View>
          </ImageBackground>
        </Animated.View>
      )}

      {/* 吸顶筛选区域 */}
      {showStickyFilter && (
        <Animated.View style={styles.stickyFilterContainer}>
          <View style={styles.stickyFilterContent}>
            <View style={styles.filterRow}>
              {/* 左侧标签带渐变装饰条 */}
              <View style={styles.filterLabelContainer}>
                <LinearGradient
                  colors={['#8ea8ff', '#4b74ff']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 0, y: 1 }}
                  style={styles.filterLabelBorder}
                />
                <View style={styles.filterLabel}>
                  <Text style={styles.filterLabelText}>最新标讯</Text>
                </View>
              </View>

              {/* 右侧筛选按钮 */}
              <View style={styles.filterButtons}>
                <TouchableOpacity style={styles.filterDropdown}>
                  <Text style={styles.filterDropdownText}>信息类型</Text>
                  <Image
                    source={require('@assets/icon_arrow_bottom.png')}
                    style={styles.filterDropdownArrow}
                    resizeMode="contain"
                  />
                </TouchableOpacity>
                <TouchableOpacity style={styles.filterDropdown}>
                  <Text style={styles.filterDropdownText}>地区</Text>
                  <Image
                    source={require('@assets/icon_arrow_bottom.png')}
                    style={styles.filterDropdownArrow}
                    resizeMode="contain"
                  />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  fixedSearchBarContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    elevation: 10,
  },
  fixedSearchBarBackground: {
    paddingTop: Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0,
    paddingBottom: 12,
  },
  fixedSearchBarContent: {
    marginHorizontal: 16,
    marginTop: 12,
  },
  stickyFilterContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 44 + 68 : (StatusBar.currentHeight || 0) + 68, // 搜索栏高度 + 状态栏高度
    left: 0,
    right: 0,
    zIndex: 999,
    elevation: 9,
    backgroundColor: '#f5f5f5',
    borderBottomWidth: 1,
    borderBottomColor: '#E4E6EE',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  stickyFilterContent: {
    marginHorizontal: 16,
    paddingVertical: 17,
  },

  headerContainer: {
    paddingBottom: 12,
    height: 253,
  },
  tabContainer: {
    alignItems: 'center',
    marginBottom: 18,
  },
  tabWrapper: {
    flexDirection: 'row',
    position: 'relative',
    alignItems: 'center',
  },
  tab: {
    marginHorizontal: 18,
    paddingBottom: 8,
    paddingTop: 4,
  },
  tabIndicatorContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
  },
  tabIndicator: {
    width: 33,
    height: 5,
    borderRadius: 3,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#ffffff',
  },
  tabText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '500',
  },
  activeTabText: {
    fontWeight: '600',
  },
  hotSearchContainer: {},
  searchBarContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  hotSearchScroll: {
    paddingHorizontal: 12,
  },
  hotSearchScrollContent: {
    alignItems: 'center',
  },
  hotSearchTag: {
    marginRight: 12,
  },

  hotSearchTagText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#FFFFFF',
    lineHeight: 12,
  },

  toolsContainer: {
    marginHorizontal: 12,
    marginTop: -55,
    overflow: 'hidden',
  },
  toolsContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    // paddingVertical: 12,
  },
  companyToolsImageContainer: {
    position: 'relative',
    marginLeft: 8,
  },
  companyToolsImage: {
    width: 80,
    height: 136,
  },
  companyToolsText: {
    position: 'absolute',
    top: 15,
    left: 11,
    fontSize: 14,
    fontWeight: '600',
    color: '#4B74FF',
    width: 58,
    lineHeight: 20,
  },
  toolsGrid: {
    flex: 1,
    position: 'relative',
    height: 160,
    paddingHorizontal: 8,
  },
  toolItem: {
    position: 'absolute',
    width: 70,
    height: 76,
    alignItems: 'center',
    justifyContent: 'center',
  },
  toolIconContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  toolIcon: {
    width: 38,
    height: 38,
  },
  badge: {
    position: 'absolute',
    top: -2,
    left: 12,
    backgroundColor: '#FF3535',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderTopLeftRadius: 6,
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
    borderBottomLeftRadius: 0,
    overflow: 'hidden',
  },
  badgeText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: '500',
  },
  toolName: {
    fontSize: 12,
    fontWeight: '500',
    color: '#363B4D',
  },
  dataTabsContainer: {
    marginHorizontal: 16,
    marginTop: 12,
  },
  dataTabsWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  dataTabButton: {
    flex: 1,
  },
  activeTabButton: {
    zIndex: 10,
  },
  leftTabOffset: {
    marginRight: -20,
  },
  rightTabOffset: {
    marginLeft: -20,
  },
  dataTabImage: {
    paddingVertical: 12,
    paddingHorizontal: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeDataTabImage: {
    height: 49,
  },
  inactiveDataTabImage: {
    height: 40,
  },
  dataTab: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  activeDataTab: {
    backgroundColor: '#3b82f6',
  },
  dataTabText: {
    fontSize: 16,
    color: '#363B4D',
  },
  activeDataTabText: {
    fontWeight: '600',
  },
  dataContentContainer: {
    marginHorizontal: 12,
    marginTop: 12,
  },
  dataItem: {
    backgroundColor: '#ffffff',
    borderRadius: 5,
    padding: 12,
    marginBottom: 12,
  },
  packageItem: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  dataItemTop: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  dataItemIcon: {
    width: 38,
    height: 38,
    borderRadius: 4,
    paddingHorizontal: 7,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  dataItemIconText: {
    fontSize: 11,
    color: '#ffffff',
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: 14,
  },
  dataItemContent: {
    flex: 1,
  },
  dataItemTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#363B4D',
    marginBottom: 10,
  },
  dataItemDescription: {
    fontSize: 11,
    color: '#7C8294',
    lineHeight: 21,
  },
  dataItemCount: {
    fontSize: 11,
    color: '#7C8294',
  },
  dataItemCountNumber: {
    color: '#F62828',
    fontWeight: '600',
    fontSize: 12,
  },
  dataItemArrow: {
    width: 5,
    height: 9,
  },
  moreButton: {
    width: 190,
    height: 30,
    marginHorizontal: 'auto',
    marginVertical: 24,
    borderWidth: 1,
    borderColor: '#E4E6EE',
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  moreButtonText: {
    fontSize: 12,
    fontWeight: '400',
    color: '#7C8294',
  },
  hotSearchIcon: {
    width: 44,
    height: 20,
    marginRight: 8,
  },
  // 标讯工具样式
  biddingToolsContainer: {
    marginHorizontal: 12,
    marginTop: -50,
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
  },
  biddingToolsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  biddingToolItem: {
    alignItems: 'center',
    flex: 1,
  },
  biddingToolIconContainer: {
    width: 35,
    height: 35,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 9,
  },
  biddingToolIcon: {
    width: 35,
    height: 35,
  },
  biddingToolName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    textAlign: 'center',
  },
  // 标讯相关样式
  biddingContentContainer: {
    marginHorizontal: 16,
  },
  filterContainer: {
    marginVertical: 17,
  },
  filterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  filterLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterLabelBorder: {
    width: 4,
    height: 15,
    borderTopLeftRadius: 50,
    borderTopRightRadius: 0,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 50,
    marginRight: 8,
  },
  filterLabel: {
    paddingVertical: 8,
    borderRadius: 6,
  },
  filterLabelText: {
    color: '#323232',
    fontSize: 15,
    fontWeight: '500',
  },
  filterButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterDropdown: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 6,
    marginLeft: 20,
  },
  filterDropdownText: {
    fontSize: 14,
    color: '#333333',
    marginRight: 8,
  },
  filterDropdownArrow: {
    width: 6,
    height: 4,
  },
  biddingListContainer: {
    borderRadius: 8,
  },
  biddingItem: {
    padding: 16,
    backgroundColor: '#ffffff',
    marginBottom: 12,
    borderRadius: 5,
  },
  biddingTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#363B4D',
    lineHeight: 23,
    marginBottom: 10,
  },
  biddingTags: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  biddingTag: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 3,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  biddingTypeTag: {
    borderRadius: 3,
    marginRight: 8,
  },
  biddingTagInner: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },

  biddingIndustryTag: {
    backgroundColor: '#dfecff',
  },
  biddingAmountTag: {
    backgroundColor: '#fef1df',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 3,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  biddingBidderTag: {
    backgroundColor: '#e8f7e3',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 3,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  biddingTagText: {
    fontSize: 11,
    color: '#ffffff',
    fontWeight: '500',
  },
  biddingIndustryText: {
    fontSize: 11,
    color: '#4B74FE',
    fontWeight: '500',
    borderRadius: 3,
  },
  biddingAmountText: {
    fontSize: 11,
    color: '#FF8411',
    fontWeight: '500',
    borderRadius: 3,
  },
  biddingBidderText: {
    fontSize: 11,
    color: '#50B72A',
    fontWeight: '500',
    borderRadius: 3,
  },
  biddingCompaniesContainer: {
    backgroundColor: '#F6F8FF',
    padding: 12,
    borderRadius: 4,
    marginBottom: 12,
  },
  biddingCompany: {
    fontSize: 12,
    marginBottom: 10,
  },
  biddingCompanyLast: {
    marginBottom: 0,
  },
  biddingCompanyLabel: {
    color: '#7C8294',
    fontSize: 12,
  },
  biddingCompanyName: {
    color: '#4B74FE',
    fontSize: 12,
  },

  biddingFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  biddingLocationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  biddingLocationIcon: {
    marginRight: 11,
  },
  biddingLocation: {
    fontSize: 13,
    color: '#4B74FE',
  },
  biddingDate: {
    fontSize: 12,
    color: '#7C8294',
  },
});

export default HomeScreen;
