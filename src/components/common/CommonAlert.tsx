import React, { useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Dimensions,
  TouchableWithoutFeedback,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import CloseIcon from '@assets/svg/close.svg';
import TipIcon from '@assets/svg/tip.svg';
import Button from './Button';

const { height: screenHeight } = Dimensions.get('window');

export interface CommonAlertProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  content: string;
  confirmText?: string;
  onConfirm?: () => void;
  showCloseButton?: boolean;
  closeOnBackdropPress?: boolean;
  showIcon?: boolean;
}

const CommonAlert: React.FC<CommonAlertProps> = ({
  visible,
  onClose,
  title = '温馨提示',
  content,
  confirmText = '知道了',
  onConfirm,
  showCloseButton = true,
  closeOnBackdropPress = true,
  showIcon = true,
}) => {
  const translateY = useSharedValue(screenHeight);
  const opacity = useSharedValue(0);

  useEffect(() => {
    if (visible) {
      // 显示动画
      opacity.value = withTiming(1, { duration: 300 });
      translateY.value = withSpring(0, {
        damping: 20,
        stiffness: 90,
      });
    } else {
      // 隐藏动画
      translateY.value = withTiming(screenHeight, { duration: 250 });
      opacity.value = withTiming(0, { duration: 250 });
    }
  }, [visible]);

  const handleClose = () => {
    translateY.value = withTiming(screenHeight, { duration: 250 });
    opacity.value = withTiming(0, { duration: 250 }, () => {
      runOnJS(onClose)();
    });
  };

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    }
    handleClose();
  };

  const handleBackdropPress = () => {
    if (closeOnBackdropPress) {
      handleClose();
    }
  };

  const backdropAnimatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={handleClose}
      statusBarTranslucent
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <Animated.View style={[styles.backdrop, backdropAnimatedStyle]}>
          <TouchableWithoutFeedback>
            <Animated.View style={[styles.container, contentAnimatedStyle]}>
              {/* 图标 */}
              {showIcon && (
                <View style={styles.iconContainer}>
                  <TipIcon width={48} height={48} />
                </View>
              )}

              {/* 头部 */}
              <View style={styles.header}>
                <Text style={styles.title}>{title}</Text>
                {showCloseButton && (
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={handleClose}
                    hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                  >
                    <CloseIcon width={20} height={20} fill="#9CA3AF" />
                  </TouchableOpacity>
                )}
              </View>

              {/* 内容 */}
              <View style={styles.content}>
                <Text style={styles.contentText}>{content}</Text>
              </View>

              {/* 确认按钮 */}
              <View style={styles.buttonContainer}>
                <Button
                  title={confirmText}
                  onPress={handleConfirm}
                  gradient={true}
                  borderRadius={8}
                  style={styles.confirmButton}
                />
              </View>
            </Animated.View>
          </TouchableWithoutFeedback>
        </Animated.View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    paddingHorizontal: 24,
    paddingTop: 32,
    paddingBottom: 24,
    width: '100%',
    maxWidth: 320,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    position: 'relative',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'center',
    flex: 1,
  },
  closeButton: {
    position: 'absolute',
    right: 0,
    top: -2,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    marginBottom: 24,
    alignItems: 'center',
  },
  contentText: {
    fontSize: 16,
    lineHeight: 24,
    color: '#6B7280',
    textAlign: 'center',
  },
  buttonContainer: {
    width: '100%',
  },
  confirmButton: {
    width: '100%',
  },
});

export default CommonAlert;
