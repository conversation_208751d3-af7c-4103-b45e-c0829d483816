import React from 'react';
import { Text, ScrollView, TouchableOpacity } from 'react-native';

export interface Tab {
  id: string;
  title: string;
}

export interface ScrollTabsProps {
  tabs: Tab[];
  activeTab: string;
  onTabPress: (id: string) => void;
}

const ScrollTabs: React.FC<ScrollTabsProps> = ({
  tabs,
  activeTab,
  onTabPress,
}) => {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      className="border-b border-gray-200"
    >
      {tabs.map(tab => (
        <TouchableOpacity
          key={tab.id}
          className={`px-4 py-3 ${
            activeTab === tab.id ? 'border-b-2 border-blue-500' : ''
          }`}
          onPress={() => onTabPress(tab.id)}
        >
          <Text
            className={activeTab === tab.id ? 'text-blue-500' : 'text-gray-600'}
          >
            {tab.title}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};

export default ScrollTabs;
