import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../../navigation/types';
import Header from '../../../components/common/Header';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

type Props = NativeStackScreenProps<RootStackParamList, 'BidNoticeFilter'>;
type RootNavigationProp = NativeStackNavigationProp<RootStackParamList>;

interface FilterOption {
  id: string;
  name: string;
}

const BidNoticeFilterScreen: React.FC<Props> = () => {
  // 使用根导航
  const navigation = useNavigation<RootNavigationProp>();

  // 筛选选项
  const [selectedRegion, setSelectedRegion] = useState<string[]>([]);
  const [selectedIndustry, setSelectedIndustry] = useState<string[]>([]);
  const [selectedType, setSelectedType] = useState<string[]>([]);
  const [selectedTime, setSelectedTime] = useState<string>('all');
  const [selectedAmount, setSelectedAmount] = useState<string>('all');

  // 地区选项
  const regions: FilterOption[] = [
    { id: 'beijing', name: '北京' },
    { id: 'shanghai', name: '上海' },
    { id: 'guangdong', name: '广东' },
    { id: 'jiangsu', name: '江苏' },
    { id: 'zhejiang', name: '浙江' },
    { id: 'sichuan', name: '四川' },
    { id: 'hubei', name: '湖北' },
    { id: 'shandong', name: '山东' },
    { id: 'fujian', name: '福建' },
  ];

  // 行业选项
  const industries: FilterOption[] = [
    { id: 'construction', name: '建筑' },
    { id: 'medical', name: '医疗' },
    { id: 'education', name: '教育' },
    { id: 'it', name: '信息技术' },
    { id: 'transportation', name: '交通运输' },
    { id: 'energy', name: '能源' },
    { id: 'agriculture', name: '农业' },
    { id: 'finance', name: '金融' },
  ];

  // 类型选项
  const types: FilterOption[] = [
    { id: 'bidding', name: '招标公告' },
    { id: 'winning', name: '中标公告' },
    { id: 'qualification', name: '资格预审' },
    { id: 'change', name: '变更公告' },
    { id: 'correction', name: '更正公告' },
    { id: 'cancel', name: '废标公告' },
  ];

  // 时间选项
  const times: FilterOption[] = [
    { id: 'all', name: '全部时间' },
    { id: 'today', name: '今天' },
    { id: 'week', name: '近一周' },
    { id: 'month', name: '近一个月' },
    { id: 'three_month', name: '近三个月' },
    { id: 'year', name: '近一年' },
  ];

  // 金额选项
  const amounts: FilterOption[] = [
    { id: 'all', name: '全部金额' },
    { id: 'below_10w', name: '10万以下' },
    { id: '10w_50w', name: '10-50万' },
    { id: '50w_100w', name: '50-100万' },
    { id: '100w_500w', name: '100-500万' },
    { id: 'above_500w', name: '500万以上' },
  ];

  const toggleRegion = (id: string) => {
    if (selectedRegion.includes(id)) {
      setSelectedRegion(selectedRegion.filter(item => item !== id));
    } else {
      setSelectedRegion([...selectedRegion, id]);
    }
  };

  const toggleIndustry = (id: string) => {
    if (selectedIndustry.includes(id)) {
      setSelectedIndustry(selectedIndustry.filter(item => item !== id));
    } else {
      setSelectedIndustry([...selectedIndustry, id]);
    }
  };

  const toggleType = (id: string) => {
    if (selectedType.includes(id)) {
      setSelectedType(selectedType.filter(item => item !== id));
    } else {
      setSelectedType([...selectedType, id]);
    }
  };

  const resetFilters = () => {
    setSelectedRegion([]);
    setSelectedIndustry([]);
    setSelectedType([]);
    setSelectedTime('all');
    setSelectedAmount('all');
  };

  const applyFilters = () => {
    // 这里应该将筛选条件传回上一页
    // 由于是模拟，这里只是简单返回
    navigation.goBack();
  };

  return (
    <View className="flex-1 bg-gray-100">
      <Header title="筛选条件" showBackButton={true} />

      <ScrollView className="flex-1">
        {/* 地区 */}
        <View className="bg-white p-4 mb-2">
          <Text className="text-base font-medium mb-3">地区</Text>
          <View className="flex-row flex-wrap">
            {regions.map(region => (
              <TouchableOpacity
                key={region.id}
                className={`px-3 py-1 rounded-full mr-2 mb-2 ${
                  selectedRegion.includes(region.id)
                    ? 'bg-blue-500'
                    : 'bg-gray-100'
                }`}
                onPress={() => toggleRegion(region.id)}
              >
                <Text
                  className={
                    selectedRegion.includes(region.id)
                      ? 'text-white'
                      : 'text-gray-600'
                  }
                >
                  {region.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* 行业 */}
        <View className="bg-white p-4 mb-2">
          <Text className="text-base font-medium mb-3">行业</Text>
          <View className="flex-row flex-wrap">
            {industries.map(industry => (
              <TouchableOpacity
                key={industry.id}
                className={`px-3 py-1 rounded-full mr-2 mb-2 ${
                  selectedIndustry.includes(industry.id)
                    ? 'bg-blue-500'
                    : 'bg-gray-100'
                }`}
                onPress={() => toggleIndustry(industry.id)}
              >
                <Text
                  className={
                    selectedIndustry.includes(industry.id)
                      ? 'text-white'
                      : 'text-gray-600'
                  }
                >
                  {industry.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* 类型 */}
        <View className="bg-white p-4 mb-2">
          <Text className="text-base font-medium mb-3">类型</Text>
          <View className="flex-row flex-wrap">
            {types.map(type => (
              <TouchableOpacity
                key={type.id}
                className={`px-3 py-1 rounded-full mr-2 mb-2 ${
                  selectedType.includes(type.id) ? 'bg-blue-500' : 'bg-gray-100'
                }`}
                onPress={() => toggleType(type.id)}
              >
                <Text
                  className={
                    selectedType.includes(type.id)
                      ? 'text-white'
                      : 'text-gray-600'
                  }
                >
                  {type.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* 时间 */}
        <View className="bg-white p-4 mb-2">
          <Text className="text-base font-medium mb-3">时间</Text>
          <View className="flex-row flex-wrap">
            {times.map(time => (
              <TouchableOpacity
                key={time.id}
                className={`px-3 py-1 rounded-full mr-2 mb-2 ${
                  selectedTime === time.id ? 'bg-blue-500' : 'bg-gray-100'
                }`}
                onPress={() => setSelectedTime(time.id)}
              >
                <Text
                  className={
                    selectedTime === time.id ? 'text-white' : 'text-gray-600'
                  }
                >
                  {time.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* 金额 */}
        <View className="bg-white p-4 mb-4">
          <Text className="text-base font-medium mb-3">金额</Text>
          <View className="flex-row flex-wrap">
            {amounts.map(amount => (
              <TouchableOpacity
                key={amount.id}
                className={`px-3 py-1 rounded-full mr-2 mb-2 ${
                  selectedAmount === amount.id ? 'bg-blue-500' : 'bg-gray-100'
                }`}
                onPress={() => setSelectedAmount(amount.id)}
              >
                <Text
                  className={
                    selectedAmount === amount.id
                      ? 'text-white'
                      : 'text-gray-600'
                  }
                >
                  {amount.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* 底部按钮 */}
      <View className="flex-row p-4 bg-white border-t border-gray-200">
        <TouchableOpacity
          className="flex-1 py-3 mr-2 items-center justify-center border border-gray-300 rounded-md"
          onPress={resetFilters}
        >
          <Text className="text-gray-700">重置</Text>
        </TouchableOpacity>
        <TouchableOpacity
          className="flex-1 py-3 ml-2 items-center justify-center bg-blue-500 rounded-md"
          onPress={applyFilters}
        >
          <Text className="text-white">确定</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default BidNoticeFilterScreen;
