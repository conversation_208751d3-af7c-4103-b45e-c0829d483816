import React from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  Text,
  StyleSheet,
  Image,
} from 'react-native';

interface SearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  onSearch: () => void;
  onClear?: () => void;
  placeholder?: string;
  autoFocus?: boolean;
  returnKeyType?: 'search' | 'done' | 'go' | 'next';
  showSearchButton?: boolean;
  searchButtonText?: string;
  onPressSearchBar?: () => void; // 新增点击搜索框回调
}

const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChangeText,
  onSearch,
  onClear,
  placeholder = '输入关键词搜索',
  autoFocus = false,
  returnKeyType = 'search',
  showSearchButton = true,
  searchButtonText = '搜索',
  onPressSearchBar,
}) => {
  if (onPressSearchBar) {
    // 不可编辑模式，用于首页点击跳转到搜索页
    return (
      <TouchableOpacity
        style={styles.clickableSearchContainer}
        onPress={onPressSearchBar}
      >
        <Image
          source={require('@assets/icon_search.png')}
          style={styles.searchImage}
        />
        <Text style={styles.placeholderText}>{placeholder}</Text>
      </TouchableOpacity>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchInputContainer}>
        <Image
          source={require('@assets/icon_search.png')}
          style={styles.searchImage}
        />
        <TextInput
          style={styles.textInput}
          placeholder={placeholder}
          placeholderTextColor="#9CA3AF"
          value={value}
          onChangeText={onChangeText}
          returnKeyType={returnKeyType}
          onSubmitEditing={onSearch}
          autoFocus={autoFocus}
        />
        {value.length > 0 && onClear && (
          <TouchableOpacity onPress={onClear}>
            <Text style={styles.clearButton}>✕</Text>
          </TouchableOpacity>
        )}
      </View>
      {showSearchButton && (
        <TouchableOpacity style={styles.searchButton} onPress={onSearch}>
          <Text style={styles.searchButtonText}>{searchButtonText}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  clickableSearchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 25,
    paddingHorizontal: 16,
    height: 40,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },

  placeholderText: {
    flex: 1,
    color: '#B1B8C7',
    fontSize: 14,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#F9FAFB',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 25,
    paddingHorizontal: 16,
    height: 40,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchImage: {
    width: 14,
    height: 14,
    marginRight: 8,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#111827',
  },
  clearButton: {
    color: '#9CA3AF',
    fontSize: 16,
    paddingHorizontal: 4,
  },
  searchButton: {
    marginLeft: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchButtonText: {
    color: '#3B82F6',
    fontWeight: '500',
    fontSize: 16,
  },
});

export default SearchBar;
