/**
 * 根导航工具
 * 提供在应用中任意位置进行导航的函数
 */

import { createNavigationContainerRef } from '@react-navigation/native';
import { CommonActions } from '@react-navigation/native';
import {
  RootStackParamList,
  AuthStackParamList,
  DetailStackParamList,
  CustomerStackParamList,
  GraphStackParamList,
  ProfileStackParamList,
  FollowStackParamList,
} from '@navigation/types';

/**
 * 创建导航引用，用于在组件树外部控制导航
 */
export const navigationRef = createNavigationContainerRef<RootStackParamList>();

/**
 * 导航到任意主路由
 * @param name 主路由名称
 * @param params 路由参数
 */
export function navigate(name: keyof RootStackParamList, params?: any) {
  if (navigationRef.isReady()) {
    navigationRef.navigate(name, params);
  }
}

/**
 * 导航到Auth栈中的特定页面
 * @param screen Auth栈中的页面名称
 * @param params 页面参数
 */
export function navigateToAuth(screen: keyof AuthStackParamList, params?: any) {
  if (navigationRef.isReady()) {
    navigationRef.navigate('Auth', {
      screen,
      params,
    });
  }
}

/**
 * 导航到找客户Tab的特定页面
 * @param screen CustomerStack中的页面名称
 * @param params 页面参数
 */
export function navigateToCustomer(
  screen: keyof CustomerStackParamList,
  params?: any,
) {
  if (navigationRef.isReady()) {
    navigationRef.navigate('Main', {
      screen: 'CustomerTab',
      params: {
        screen,
        params,
      },
    });
  }
}

/**
 * 导航到企业图谱Tab的特定页面
 * @param screen GraphStack中的页面名称
 * @param params 页面参数
 */
export function navigateToGraph(
  screen: keyof GraphStackParamList,
  params?: any,
) {
  if (navigationRef.isReady()) {
    navigationRef.navigate('Main', {
      screen: 'GraphTab',
      params: {
        screen,
        params,
      },
    });
  }
}

/**
 * 导航到关注Tab的特定页面
 * @param screen FollowStack中的页面名称
 * @param params 页面参数
 */
export function navigateToFollow(
  screen: keyof FollowStackParamList,
  params?: any,
) {
  if (navigationRef.isReady()) {
    navigationRef.navigate('Main', {
      screen: 'FollowTab',
      params: {
        screen,
        params,
      },
    });
  }
}

/**
 * 导航到个人中心Tab的特定页面
 * @param screen ProfileStack中的页面名称
 * @param params 页面参数
 */
export function navigateToProfile(
  screen: keyof ProfileStackParamList,
  params?: any,
) {
  if (navigationRef.isReady()) {
    navigationRef.navigate('Main', {
      screen: 'ProfileTab',
      params: {
        screen,
        params,
      },
    });
  }
}

/**
 * 导航到Detail栈中的特定页面
 * @param screen Detail栈中的页面名称
 * @param params 页面参数
 */
export function navigateToDetail(
  screen: keyof DetailStackParamList,
  params?: any,
) {
  if (navigationRef.isReady()) {
    navigationRef.navigate('Detail', {
      screen,
      params,
    });
  }
}

/**
 * 返回上一页
 */
export function goBack() {
  if (navigationRef.isReady() && navigationRef.canGoBack()) {
    navigationRef.goBack();
  }
}

/**
 * 重置整个导航状态到首页
 */
export function resetToMain() {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name: 'Main' }],
      }),
    );
  }
}

export default {
  navigate,
  navigateToAuth,
  navigateToCustomer,
  navigateToGraph,
  navigateToFollow,
  navigateToProfile,
  navigateToDetail,
  goBack,
  resetToMain,
};
