import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { DetailStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';

type Props = NativeStackScreenProps<DetailStackParamList, 'PatentDetail'>;

interface Patent {
  id: string;
  name: string;
  applicationNumber: string;
  applicationDate: string;
  publicationNumber: string;
  publicationDate: string;
  priorityNumber?: string;
  priorityDate?: string;
  ipcClassification: string;
  cpcClassification?: string;
  patentType: string;
  legalStatus: string;
  abstract: string;
  progress: ProgressItem[];
}

interface ProgressItem {
  id: string;
  name: string;
  date: string;
  isActive: boolean;
}

type TabType =
  | '基本信息'
  | '申请人/代理机构'
  | '法律状态'
  | '权利要求'
  | '申请进度';

const PatentDetailScreen: React.FC<Props> = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState<TabType>('基本信息');

  // 模拟专利数据
  const patent: Patent = {
    id: '1',
    name: '专利名称专利名称专利名称专利名称专利名称专利名称专利名称专利名称专利名称专利名称专利名称专利名称利名称',
    applicationNumber: 'CN201710712027.7',
    applicationDate: '2017 - 08 - 18',
    publicationNumber: 'CN107464598A',
    publicationDate: '2017 - 12 - 12',
    ipcClassification: 'H01B1/02',
    cpcClassification: 'H01B1/02; H01B13/00; C22C21/00; C22C21/14; C22C21/16',
    patentType: '发明公告',
    legalStatus: '发明专利申请公布后的驳回',
    abstract:
      '本发明涉及铝合金基复合导体及其制造方法以及相应的衍生品。该铝合金基体具有如下组成：铁 0.2% - 0.5%, 硅 0.04% - 0.08%, 铜 0.22% - 0.40%, 锑 0.001% - 0.01%, 钾 0.001% - 0.005%, 镁 0.01% - 0.1%, 钛、锰、钠和铬的总和不大于 0.015%, 余量为铝 AL; 本发明的铝合金基复合导体, 覆铜层的厚度为铝合金基体直径的 1% - 5%, 镀银层厚度≥0.005MM、铝合金基复合导体的强度≥255MPA, 延伸率≥15%, 导电率≥61%IACS, 弯曲性能优良。',
    progress: [
      {
        id: '1',
        name: '专利申请',
        date: '2022-09-09',
        isActive: true,
      },
      {
        id: '2',
        name: '申请公布',
        date: '2022-09-09',
        isActive: true,
      },
      {
        id: '3',
        name: '授权',
        date: '2022-09-09',
        isActive: true,
      },
      {
        id: '4',
        name: '预估到期',
        date: '2022-09-09',
        isActive: false,
      },
    ],
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const handleTabPress = (tab: TabType) => {
    setActiveTab(tab);
  };

  const renderTabs = () => {
    const tabs: TabType[] = [
      '申请进度',
      '基本信息',
      '申请人/代理机构',
      '法律状态',
      '权利要求',
    ];

    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        className="bg-white"
      >
        {tabs.map(tab => (
          <TouchableOpacity
            key={tab}
            className={`px-4 py-3 ${
              activeTab === tab ? 'border-b-2 border-blue-500' : ''
            }`}
            onPress={() => handleTabPress(tab)}
          >
            <Text
              className={activeTab === tab ? 'text-blue-500' : 'text-gray-500'}
            >
              {tab}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  const renderBasicInfo = () => {
    return (
      <View className="bg-white p-4 mt-2">
        <View className="mb-4">
          <Text className="text-gray-500">
            申请号：{patent.applicationNumber}
          </Text>
        </View>

        <View className="mb-4">
          <Text className="text-gray-500">
            申请日期：{patent.applicationDate}
          </Text>
        </View>

        <View className="mb-4">
          <Text className="text-gray-500">
            申请公布号：{patent.publicationNumber}
          </Text>
        </View>

        <View className="mb-4">
          <Text className="text-gray-500">
            申请公布日：{patent.publicationDate}
          </Text>
        </View>

        {patent.priorityNumber && (
          <View className="mb-4">
            <Text className="text-gray-500">
              优先权号：{patent.priorityNumber}
            </Text>
          </View>
        )}

        {patent.priorityDate && (
          <View className="mb-4">
            <Text className="text-gray-500">
              优先权日：{patent.priorityDate}
            </Text>
          </View>
        )}

        <View className="mb-4">
          <Text className="text-gray-500">
            IPC 分类号：{patent.ipcClassification}
          </Text>
        </View>

        {patent.cpcClassification && (
          <View className="mb-4">
            <Text className="text-gray-500">
              CPC 分类号：{patent.cpcClassification}
            </Text>
          </View>
        )}

        <View className="mb-4">
          <Text className="text-gray-500">专利类型：{patent.patentType}</Text>
        </View>

        <View className="mb-4">
          <Text className="text-gray-500">法律状态：{patent.legalStatus}</Text>
        </View>

        <View>
          <Text className="text-gray-500">专利摘要：{patent.abstract}</Text>
        </View>
      </View>
    );
  };

  const renderProgress = () => {
    return (
      <View className="bg-white p-4 mt-2">
        <View className="relative">
          {/* 竖线 */}
          <View className="absolute left-3 top-6 bottom-6 w-0.5 bg-gray-200" />

          {patent.progress.map((item, index) => (
            <View key={item.id} className="flex-row items-center mb-12">
              <View
                className={`w-6 h-6 rounded-full ${
                  item.isActive
                    ? index === 1
                      ? 'bg-orange-500'
                      : 'bg-blue-500'
                    : 'border-2 border-gray-300 bg-white'
                } items-center justify-center z-10`}
              >
                {item.isActive && index === 1 && (
                  <View className="w-3 h-3 bg-white rounded-full" />
                )}
              </View>

              <View className="ml-4">
                <View
                  className={`px-3 py-1 rounded ${
                    index === 0
                      ? 'bg-blue-100'
                      : index === 1
                      ? 'bg-orange-100'
                      : index === 2
                      ? 'bg-green-100'
                      : 'bg-gray-100'
                  }`}
                >
                  <Text
                    className={`${
                      index === 0
                        ? 'text-blue-500'
                        : index === 1
                        ? 'text-orange-500'
                        : index === 2
                        ? 'text-green-500'
                        : 'text-gray-500'
                    }`}
                  >
                    {item.name}
                  </Text>
                </View>
                <Text className="text-gray-500 mt-2">{item.date}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-100">
      <Header title="专利详情" showBackButton onBackPress={handleBack} />

      <ScrollView className="flex-1">
        {/* 专利名称 */}
        <View className="bg-white p-4 mb-2">
          <Text className="text-lg font-medium mb-4" numberOfLines={3}>
            {patent.name}
          </Text>

          <View className="flex-row">
            <View className="bg-blue-100 px-3 py-1 rounded mr-2">
              <Text className="text-blue-500 text-xs">发明公告</Text>
            </View>
            <View className="bg-orange-100 px-3 py-1 rounded">
              <Text className="text-orange-500 text-xs">公布驳回级</Text>
            </View>
          </View>
        </View>

        {/* 标签页 */}
        {renderTabs()}

        {/* 内容区域 */}
        {activeTab === '基本信息' && renderBasicInfo()}
        {activeTab === '申请进度' && renderProgress()}
        {/* 其他标签页内容可以根据需要添加 */}
      </ScrollView>
    </SafeAreaView>
  );
};

export default PatentDetailScreen;
