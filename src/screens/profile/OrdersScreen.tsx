import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  FlatList,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ProfileStackParamList } from '../../navigation/types';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

type Props = NativeStackScreenProps<ProfileStackParamList, 'Orders'>;

// 订单状态类型
type OrderStatus = '待支付' | '已完成' | '已取消';

// 标签类型
type TabType = '全部订单' | '未支付' | '已完成';

// 订单类型
interface Order {
  id: string;
  productName: string;
  orderNumber: string;
  createTime: string;
  payTime?: string;
  amount: number;
  status: OrderStatus;
}

const OrdersScreen: React.FC<Props> = ({ navigation }) => {
  // 当前选中的标签
  const [activeTab, setActiveTab] = useState<TabType>('全部订单');

  // 模拟订单数据
  const orders: Order[] = [
    {
      id: '1',
      productName: '启蒙方会员-1年',
      orderNumber: '3456789067890',
      createTime: '2025-09-09 12:00:00',
      amount: 500,
      status: '待支付',
    },
    {
      id: '2',
      productName: '启蒙方会员-1年',
      orderNumber: '3456789067890',
      createTime: '2025-09-09 12:00:00',
      payTime: '2025-09-09 12:00:00',
      amount: 500,
      status: '已完成',
    },
    {
      id: '3',
      productName: '启蒙方会员-1年',
      orderNumber: '3456789067890',
      createTime: '2025-09-09 12:00:00',
      amount: 500,
      status: '已取消',
    },
  ];

  // 根据当前标签筛选订单
  const getFilteredOrders = () => {
    switch (activeTab) {
      case '未支付':
        return orders.filter(order => order.status === '待支付');
      case '已完成':
        return orders.filter(order => order.status === '已完成');
      default:
        return orders;
    }
  };

  // 渲染标签
  const renderTabs = () => {
    const tabs: TabType[] = ['全部订单', '未支付', '已完成'];

    return (
      <View className="flex-row border-b border-gray-100">
        {tabs.map(tab => (
          <TouchableOpacity
            key={tab}
            className={`flex-1 py-3 items-center ${
              activeTab === tab ? 'border-b-2 border-blue-500' : ''
            }`}
            onPress={() => setActiveTab(tab)}
          >
            <Text
              className={
                activeTab === tab
                  ? 'text-blue-500 font-medium'
                  : 'text-gray-500'
              }
            >
              {tab}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  // 渲染状态标签
  const renderStatusTag = (status: OrderStatus) => {
    let bgColor = 'bg-yellow-100';
    let textColor = 'text-yellow-600';

    if (status === '已完成') {
      bgColor = 'bg-green-100';
      textColor = 'text-green-600';
    } else if (status === '已取消') {
      bgColor = 'bg-gray-100';
      textColor = 'text-gray-600';
    }

    return (
      <View className={`px-2 py-1 rounded ${bgColor}`}>
        <Text className={`text-xs ${textColor}`}>{status}</Text>
      </View>
    );
  };

  // 渲染订单底部按钮
  const renderOrderButtons = (order: Order) => {
    switch (order.status) {
      case '待支付':
        return (
          <View className="flex-row justify-end mt-4">
            <TouchableOpacity
              className="px-4 py-2 border border-gray-300 rounded-sm mr-3"
              onPress={() => console.log('取消支付')}
            >
              <Text className="text-gray-500">取消支付</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className="px-4 py-2 bg-blue-500 rounded-sm"
              onPress={() => console.log('立即支付')}
            >
              <Text className="text-white">立即支付</Text>
            </TouchableOpacity>
          </View>
        );
      case '已完成':
        return (
          <View className="flex-row justify-end mt-4">
            <TouchableOpacity
              className="px-4 py-2 border border-gray-300 rounded-sm"
              onPress={() => console.log('删除订单')}
            >
              <Text className="text-gray-500">删除订单</Text>
            </TouchableOpacity>
          </View>
        );
      case '已取消':
        return (
          <View className="flex-row justify-end mt-4">
            <TouchableOpacity
              className="px-4 py-2 border border-gray-300 rounded-sm"
              onPress={() => console.log('删除订单')}
            >
              <Text className="text-gray-500">删除订单</Text>
            </TouchableOpacity>
          </View>
        );
      default:
        return null;
    }
  };

  // 渲染单个订单项
  const renderOrderItem = ({ item }: { item: Order }) => {
    return (
      <View className="bg-white rounded-lg mb-3 p-4">
        <View className="flex-row justify-between items-center mb-2">
          <Text className="text-base font-medium">{item.productName}</Text>
          {renderStatusTag(item.status)}
        </View>

        <View className="border-b border-gray-100 pb-3">
          <Text className="text-gray-500 mb-1">
            订单编号：{item.orderNumber}
          </Text>
          <Text className="text-gray-500 mb-1">
            下单时间：{item.createTime}
          </Text>
          {item.payTime && (
            <Text className="text-gray-500">支付时间：{item.payTime}</Text>
          )}
        </View>

        <View className="flex-row justify-between items-center mt-3">
          <Text className="text-red-500 text-lg font-medium">
            ¥{item.amount}
          </Text>
        </View>

        {renderOrderButtons(item)}
      </View>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-100">
      {/* 标题栏 */}
      <View className="bg-white flex-row items-center p-4 border-b border-gray-200">
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Icon name="arrow-left" size={24} color="#333" />
        </TouchableOpacity>
        <Text className="flex-1 text-center text-lg font-medium">我的订单</Text>
        <View style={{ width: 24 }} />
      </View>

      {/* 标签页 */}
      {renderTabs()}

      {/* 订单列表 */}
      <FlatList
        data={getFilteredOrders()}
        renderItem={renderOrderItem}
        keyExtractor={item => item.id}
        contentContainerStyle={{ padding: 12 }}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => (
          <View className="items-center justify-center p-10">
            <Text className="text-gray-400">暂无订单</Text>
          </View>
        )}
      />
    </SafeAreaView>
  );
};

export default OrdersScreen;
