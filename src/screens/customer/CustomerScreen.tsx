import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  SafeAreaView,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { HomeStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';
import Card from '../../components/common/Card';

type Props = NativeStackScreenProps<HomeStackParamList, 'Home'>;

interface CustomerCategory {
  id: string;
  name: string;
  count: number;
  icon: string; // 这里使用emoji作为简单图标
}

interface CustomerItem {
  id: string;
  name: string;
  industry: string;
  size: string;
  registeredCapital: string;
  tags: string[];
  match: number; // 匹配度 0-100
}

const CustomerScreen: React.FC<Props> = ({ navigation }) => {
  const [searchText, setSearchText] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');

  // 模拟客户分类数据
  const categories: CustomerCategory[] = [
    { id: 'all', name: '全部', count: 1258, icon: '🏢' },
    { id: 'construction', name: '建筑/工程', count: 532, icon: '🏗️' },
    { id: 'tech', name: '科技/IT', count: 328, icon: '💻' },
    { id: 'manufacturing', name: '制造业', count: 294, icon: '🏭' },
    { id: 'realestate', name: '房地产', count: 104, icon: '🏘️' },
  ];

  // 模拟潜在客户数据
  const customerData: CustomerItem[] = [
    {
      id: '1',
      name: '某建筑工程有限公司',
      industry: '建筑/工程',
      size: '大型企业',
      registeredCapital: '1亿元',
      tags: ['AAA资质', '国企背景', '大型项目'],
      match: 95,
    },
    {
      id: '2',
      name: '某科技有限公司',
      industry: '科技/IT',
      size: '中型企业',
      registeredCapital: '5000万元',
      tags: ['高新技术', '上市公司'],
      match: 87,
    },
    {
      id: '3',
      name: '某机械制造有限公司',
      industry: '制造业',
      size: '大型企业',
      registeredCapital: '8000万元',
      tags: ['大型项目', '专利技术'],
      match: 80,
    },
    {
      id: '4',
      name: '某房地产开发有限公司',
      industry: '房地产',
      size: '中型企业',
      registeredCapital: '3000万元',
      tags: ['地产开发', '商业地产'],
      match: 76,
    },
    {
      id: '5',
      name: '某建设集团有限公司',
      industry: '建筑/工程',
      size: '特大型企业',
      registeredCapital: '5亿元',
      tags: ['特级资质', '国企背景', '重大项目'],
      match: 68,
    },
  ];

  // 根据当前分类筛选客户
  const filteredCustomers = customerData.filter(customer => {
    // 文本搜索
    const matchesSearch =
      searchText === '' ||
      customer.name.includes(searchText) ||
      customer.industry.includes(searchText);

    // 分类筛选
    const matchesCategory =
      activeCategory === 'all' ||
      (activeCategory === 'construction' &&
        customer.industry === '建筑/工程') ||
      (activeCategory === 'tech' && customer.industry === '科技/IT') ||
      (activeCategory === 'manufacturing' && customer.industry === '制造业') ||
      (activeCategory === 'realestate' && customer.industry === '房地产');

    return matchesSearch && matchesCategory;
  });

  const handleSearch = () => {
    // 这里可以实现搜索逻辑
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <Header title="找客户" />

      <View className="p-4">
        {/* 搜索框 */}
        <View className="flex-row items-center bg-gray-100 rounded-md px-3 py-2 mb-4">
          <TextInput
            className="flex-1 text-base"
            placeholder="搜索潜在客户"
            value={searchText}
            onChangeText={setSearchText}
            returnKeyType="search"
            onSubmitEditing={handleSearch}
          />
          <TouchableOpacity onPress={handleSearch}>
            <Text className="text-blue-500">搜索</Text>
          </TouchableOpacity>
        </View>

        {/* 分类标签 */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          className="mb-4"
        >
          {categories.map(category => (
            <TouchableOpacity
              key={category.id}
              className={`mr-3 px-4 py-2 rounded-full ${
                activeCategory === category.id ? 'bg-blue-500' : 'bg-gray-100'
              }`}
              onPress={() => setActiveCategory(category.id)}
            >
              <View className="flex-row items-center">
                <Text className="mr-1">{category.icon}</Text>
                <Text
                  className={`${
                    activeCategory === category.id
                      ? 'text-white'
                      : 'text-gray-700'
                  }`}
                >
                  {category.name} ({category.count})
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <ScrollView className="flex-1 px-4">
        <Text className="font-bold text-lg mb-4">潜在客户推荐</Text>

        {filteredCustomers.map(customer => (
          <TouchableOpacity
            key={customer.id}
            onPress={() =>
              navigation.navigate('Detail', {
                screen: 'CompanyDetail',
                params: { id: customer.id },
              })
            }
          >
            <Card className="mb-4">
              <View className="flex-row justify-between items-start">
                <View className="flex-1">
                  <Text className="text-lg font-bold">{customer.name}</Text>
                  <Text className="text-gray-500 text-xs mt-1">
                    {customer.industry} | {customer.size} | 注册资本:{' '}
                    {customer.registeredCapital}
                  </Text>

                  <View className="flex-row flex-wrap mt-2">
                    {customer.tags.map((tag, index) => (
                      <View
                        key={index}
                        className="bg-blue-50 rounded-md px-2 py-1 mr-2 mb-1"
                      >
                        <Text className="text-blue-500 text-xs">{tag}</Text>
                      </View>
                    ))}
                  </View>
                </View>

                <View className="bg-blue-50 px-3 py-2 rounded-full">
                  <Text className="text-blue-500 font-bold">
                    {customer.match}%
                  </Text>
                </View>
              </View>

              <View className="flex-row justify-between items-center mt-3 pt-3 border-t border-gray-100">
                <TouchableOpacity
                  className="flex-row items-center"
                  onPress={() => {
                    // 添加到关注列表
                  }}
                >
                  <Text className="text-blue-500 text-sm">+ 添加关注</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  className="flex-row items-center"
                  onPress={() =>
                    navigation.navigate('Detail', {
                      screen: 'CompanyGraph',
                      params: { id: customer.id },
                    })
                  }
                >
                  <Text className="text-blue-500 text-sm">查看关系图谱</Text>
                </TouchableOpacity>

                <TouchableOpacity className="flex-row items-center">
                  <Text className="text-blue-500 text-sm">查看更多</Text>
                </TouchableOpacity>
              </View>
            </Card>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
};

export default CustomerScreen;
